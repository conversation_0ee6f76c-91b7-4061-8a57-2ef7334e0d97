{"cells": [{"cell_type": "code", "execution_count": null, "id": "b7b7eded", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4defa0aa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "a9c474ff", "metadata": {}, "outputs": [], "source": ["import sys"]}, {"cell_type": "code", "execution_count": 3, "id": "deb4cde6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import xlsxwriter\n", "import numpy as np\n", "import google.generativeai as genai\n", "import json\n", "import logging\n", "import statsmodels.api as sm\n", "import os"]}, {"cell_type": "markdown", "id": "cc125cd5", "metadata": {}, "source": ["#Functions "]}, {"cell_type": "code", "execution_count": 4, "id": "970ce0d1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import google.generativeai as genai\n", "import json\n", "import logging\n", "import statsmodels.api as sm\n", "import os\n", "import sys\n", "import matplotlib.pyplot as plt\n", "import matplotlib_venn\n", "import seaborn as sns\n", "import datetime\n", "final_df = pd.DataFrame()\n", "# Configure logging\n", "logging.basicConfig(level=logging.ERROR)\n", "\n", "# Initialize Gemini API\n", "genai.configure(api_key=\"AIzaSyA3cXv0-vsm31kXkJtLUT3W_bHooZm3hN8\")\n", "model = genai.GenerativeModel(\"gemini-2.0-flash\")\n", "\n", "\n", "\n", "def get_unit_price():\n", "    \"\"\"Helper function to get user input for unit price\"\"\"\n", "    return float(input(\"Enter the price of each unit: \").strip())\n", "\n", "\n", "def load_file(file_path):\n", "    \"\"\"\n", "    Load data from a file based on its extension.\n", "\n", "    Args:\n", "        file_path (str): Path to the file to be loaded\n", "\n", "    Returns:\n", "        pandas.DataFrame: Loaded dataframe\n", "    \"\"\"\n", "    try:\n", "        logging.info(\"Loading data from file.\")\n", "\n", "        # Load data based on file extension\n", "        if file_path.endswith(\".csv\"):\n", "            df = pd.read_csv(file_path)\n", "        elif file_path.endswith(\".xlsx\"):\n", "            df = pd.read_excel(file_path)\n", "        else:\n", "            error_msg = f\"ERROR: Unsupported file format for '{file_path}'. Only CSV and Excel files are supported.\"\n", "            logging.error(error_msg)\n", "            print(error_msg)\n", "            sys.exit(1)  # Exit immediately\n", "\n", "        return df\n", "\n", "    except FileNotFoundError:\n", "        error_msg = f\"ERROR: File '{file_path}' not found. Please check the file path and try again.\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately for file not found\n", "    except Exception as e:\n", "        error_msg = f\"ERROR: Failed to load file '{file_path}': {str(e)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately for other loading errors\n", "import os\n", "import logging\n", "import psycopg2\n", "\n", "def get_db_connection():\n", "    \"\"\"\n", "    Create and return a connection to the PostgreSQL database\n", "    \n", "    Returns:\n", "        connection: PostgreSQL database connection object\n", "    \"\"\"\n", "    try:\n", "        # Connect to the PostgreSQL database\n", "        conn = psycopg2.connect(\n", "            host=os.environ.get(\"DB_HOST\", \"localhost\"),\n", "            database=os.environ.get(\"DB_NAME\", \"MMX\"),\n", "            user=os.environ.get(\"DB_USER\", \"postgres\"),\n", "            password=os.environ.get(\"DB_PASSWORD\", \"something\")\n", "        )\n", "        return conn\n", "    except Exception as e:\n", "        logging.error(f\"Error connecting to database: {str(e)}\")\n", "        return None\n", "\n", "\n", "\n", "def initialize_db_connection():\n", "    \"\"\"Initialize database connection\"\"\"\n", "    return get_db_connection()\n", "\n", "def close_db_connection(conn):\n", "    \"\"\"Close database connection\"\"\"\n", "    if conn:\n", "        try:\n", "            conn.close()\n", "            return True\n", "        except Exception as e:\n", "            logging.error(f\"Error closing database connection: {str(e)}\")\n", "            return False\n", "    return True\n", "def process_dates(df,date_column,date_format):\n", "    \"\"\"Process and clean date column in dataframe\"\"\"\n", "    # Make a copy of the original date column for debugging\n", "    df['original_date'] = df[date_column].copy()\n", "     # Your custom function to select format\n", "    df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))\n", "\n", "\n", "    # Print debugging info about the conversion success\n", "    na_count = df[date_column].isna().sum()\n", "    total_rows = len(df)\n", "    print(f\"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted\")\n", "\n", "    if na_count > 0:\n", "        # Show examples of problematic values\n", "        problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]\n", "        print(f\"Examples of problematic date values: {problem_examples}\")\n", "\n", "    # Drop the empty dates\n", "    orig_len = len(df)\n", "    df = df.dropna(subset=[date_column])\n", "    print(f\"Dropped {orig_len - len(df)} rows with invalid dates\")\n", "\n", "    # Drop the debug column\n", "    df = df.drop(columns=['original_date'])\n", "    \n", "    return df\n", "\n", "\n", "\n", "def get_main_data_columns(file_path):\n", "    \n", "    df = load_file(file_path)\n", "    \"\"\"Get and validate column names for main data from user input\"\"\"\n", "\n", "    \n", "    # For testing, using hardcoded values\n", "    promo_channels = ['PDE','Copay']\n", "    promo_channels = [col.strip() for col in promo_channels]\n", "    date_column = 'Date'\n", "    id_column = 'ID'\n", "    target_column = 'NRx'\n", "    start_date = '202411'\n", "    end_date = '202512'\n", "    start_date = datetime.datetime.strptime(start_date, '%Y%m')\n", "    end_date = datetime.datetime.strptime(end_date, '%Y%m')\n", "\n", "    \n", "    # Check if required columns exist\n", "    missing_cols = [col for col in [date_column, target_column, id_column] if col not in df.columns]\n", "    if missing_cols:\n", "        error_msg = f\"ERROR: The following required columns are missing from your main data: {', '.join(missing_cols)}\"\n", "        error_msg += f\"\\nAvailable columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "\n", "    # Check if promotional channels exist\n", "    missing_promo_cols = [col for col in promo_channels if col not in df.columns]\n", "    if missing_promo_cols:\n", "        error_msg = f\"ERROR: The following promotional channel columns are missing: {', '.join(missing_promo_cols)}\"\n", "        error_msg += f\"\\nAvailable columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "    \n", "    print(f\"Validated main data columns: {date_column}, {id_column}, {target_column}\")\n", "    print(f\"Promotional channels: {', '.join(promo_channels)}\")\n", "    print(f\"Date range: {start_date} to {end_date}\")\n", "    \n", "    return df,promo_channels, date_column, id_column, target_column, start_date, end_date\n", "\n", "def select_date_format():\n", "    \"\"\"Select and return the date format from user input\"\"\"\n", "    global date_format\n", "    \n", "    print(\"\\nSelect date format from the following options:\")\n", "    print(\"1. MM/DD/YYYY\")\n", "    print(\"2. YYYY-MM-DD\")\n", "    print(\"3. YYYY-MM\")\n", "    print(\"4. MM-YYYY\")\n", "    \n", "    # For production, uncomment to get user input\n", "    # format_choice = input(\"Enter your choice (1-4): \").strip()\n", "    \n", "    # For testing, using a default value\n", "    format_choice = '4'\n", "    \n", "    if format_choice == '1':\n", "        date_format = 'MM/DD/YYYY'\n", "    elif format_choice == '2':\n", "        date_format = 'YYYY-MM-DD'\n", "    elif format_choice == '3':\n", "        date_format = 'YYYY-MM'\n", "    elif format_choice == '4':\n", "        date_format = 'MM-YYYY'\n", "    else:\n", "        print(\"Invalid choice. Defaulting to YYYY-MM-DD.\")\n", "        date_format = 'YYYY-MM-DD'\n", "    \n", "    print(f\"Selected date format: {date_format}\")\n", "    return date_format\n", "\n", "def convert_date(date_val,date_format):\n", "    \"\"\"Convert various date formats to YYYYMM format\"\"\"\n", "    if pd.isna(date_val):\n", "        return np.nan\n", "\n", "    # Convert to string if it's not already\n", "    date_str = str(date_val).strip()\n", "\n", "    try:\n", "        # First, try to parse based on user-selected format\n", "        try:\n", "            if date_format == 'MM/DD/YYYY':\n", "                date_obj = pd.to_datetime(date_str, format='%m/%d/%Y', errors='raise')\n", "            elif date_format == 'YYYY-MM-DD':\n", "                date_obj = pd.to_datetime(date_str, format='%Y-%m-%d', errors='raise')\n", "            elif date_format == 'YYYY-MM':\n", "                date_obj = pd.to_datetime(date_str, format='%Y-%m', errors='raise')\n", "            elif date_format == 'MM-YYYY':\n", "                date_obj = pd.to_datetime(date_str, format='%m-%Y', errors='raise')\n", "            else:\n", "                date_obj = None\n", "            if date_obj is not None:\n", "                return date_obj.replace(day=1)\n", "        except Exception:\n", "            # If specific format parsing fails, continue with fallback options\n", "            pass\n", "        \n", "        # For formats like '20247' (YYYYM) - year 2024, month 7\n", "        if date_str.isdigit() and len(date_str) == 5:\n", "            year = date_str[:4]\n", "            month = date_str[4:5]  # Just one digit\n", "            return f\"{year}{month.zfill(2)}\"  # zfill adds leading zero if needed\n", "\n", "        # For formats like '202407' (YYYYMM)\n", "        elif date_str.isdigit() and len(date_str) == 6:\n", "            year = date_str[:4]\n", "            month = date_str[4:6]\n", "            return f\"{year}{month}\"\n", "\n", "        # For integer timestamps\n", "        elif date_str.isdigit() and len(date_str) > 6:\n", "            # Convert timestamp to datetime\n", "            date_obj = pd.to_datetime(int(date_str), errors='coerce')\n", "            if not pd.isna(date_obj):\n", "                return date_obj.strftime('%Y%m')\n", "\n", "        # Try standard datetime parsing\n", "        date_obj = pd.to_datetime(date_str, errors='coerce')\n", "        if not pd.isna(date_obj):\n", "            return date_obj.strftime('%Y%m')\n", "\n", "        return np.nan\n", "    except:\n", "        return np.nan\n", "\n", "\n", "\n", "def handle_outliers(df, promo_channels, target_column, num_sigmas=3):\n", "    \"\"\"Handle outliers in promotional channels\"\"\"\n", "    all_columns = promo_channels + [target_column]\n", "\n", "    # Store initial sums\n", "    initial_sums = {col: df[col].sum() for col in all_columns if col in df.columns}\n", "    initial_sums_df = pd.DataFrame(initial_sums, index=[\"before\"])\n", "\n", "    # Track indices of all outliers to remove\n", "    outlier_indices = set()\n", "\n", "    # Dictionary to store outlier indices by column\n", "    column_outlier_indices = {}\n", "\n", "    for col in promo_channels:\n", "        if col in df.columns:\n", "            data = df[col]\n", "            mean = np.mean(data)\n", "            std = np.std(data)\n", "\n", "            # Identify outliers\n", "            lower_bound = mean - num_sigmas * std\n", "            upper_bound = mean + num_sigmas * std\n", "            outliers = df[(data < lower_bound) | (data > upper_bound)]\n", "            outlier_idx = outliers.index.tolist()\n", "\n", "            # Store outlier indices for this column\n", "            column_outlier_indices[col] = outlier_idx\n", "\n", "            # Calculate the percentage drop if we remove these outliers\n", "            if outlier_idx:\n", "                col_sum_before = df[col].sum()\n", "                col_sum_after = df.loc[~df.index.isin(outlier_idx), col].sum()\n", "                percentage_drop = ((col_sum_before - col_sum_after) / col_sum_before * 100) if col_sum_before != 0 else 0\n", "\n", "                # Only add to outlier indices if percentage drop is less than 10%\n", "                if percentage_drop <= 10:\n", "                    outlier_indices.update(outlier_idx)\n", "                    print(f\"[{col}] Removed {len(outlier_idx)} outliers (mu ± {num_sigmas}σ) - {percentage_drop:.2f}% drop\")\n", "                else:\n", "                    print(f\"[{col}] Skipping {len(outlier_idx)} outliers - would cause {percentage_drop:.2f}% drop (>10%)\")\n", "\n", "    # Drop all outliers and reset index\n", "    df = df.drop(index=outlier_indices).reset_index(drop=True)\n", " \n", "    # Store final sums\n", "    final_sums = {col: df[col].sum() for col in all_columns if col in df.columns}\n", "    final_sums_df = pd.DataFrame(final_sums, index=[\"after\"])\n", "\n", "    # Combine before/after\n", "    summary_df = pd.concat([initial_sums_df, final_sums_df]).T.reset_index()\n", "    summary_df.columns = [\"channel\", \"before\", \"after\"]\n", "    summary_df[\"Percentage drop\"] = summary_df.apply(\n", "        lambda row: ((row[\"before\"] - row[\"after\"]) / row[\"before\"] * 100) if row[\"before\"] != 0 else 0,\n", "        axis=1\n", "    )\n", "\n", "    print(f\"Total outliers removed: {len(outlier_indices)}\")\n", "    print(f\"Remaining rows: {len(df)}\")\n", "\n", "    print(\"Outlier summary:\")\n", "    print(summary_df)\n", "    \n", "    return df\n", "\n", "\n", "\n", "def add_control_variables(df, id_col, date_col):\n", "    \"\"\"Add control variables to the dataframe\"\"\"\n", "    df['Rtime'] = df.groupby(id_col).cumcount() + 1\n", "    control_variables = []\n", "\n", "    months = pd.to_datetime(df[date_col], errors='coerce').dt.month\n", "    # Uncomment to create month dummies\n", "    # for m in range(1, 13):\n", "    #     df[f'm{m}'] = (months == m).astype(int)\n", "    #     control_variables.append(f'm{m}')\n", "\n", "    max_period = df.groupby(id_col).size().max()\n", "    for t in range(1, max_period + 1):\n", "        df[f'T{t}'] = (df['Rtime'] == t).astype(int)\n", "        control_variables.append(f'T{t}')\n", "    control_variables.append('Rtime')\n", "\n", "    print(f\"Added {len(control_variables)} control variables\")\n", "    return df, control_variables\n", "\n", "#################################\n", "# REGRESSION FUNCTIONS\n", "#################################\n", "# def perform_regression(df, promo_columns, target_column, date_column,start_date,end_date):\n", "\n", "#     \"\"\"\n", "#     Perform regression analysis with the specified promotional columns.\n", "\n", "#     Args:\n", "#         df (pd.DataFrame): Input dataframe\n", "#         promo_columns (list): List of promotional channel columns to use as predictors\n", "#         target_column (str): Target variable name\n", "#         date_column (str): Column containing date information\n", "\n", "#     Returns:\n", "#         pd.DataFrame: Regression results table\n", "#     \"\"\"\n", "\n", "#     df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "#     try:\n", "#         # Make a copy of the input DataFrame to avoid modifying the original\n", "#         df = df.copy()\n", "#         final_df = df.copy()\n", "\n", "#         if not promo_columns:\n", "#             logging.error(\"No valid promotional columns selected for regression.\")\n", "#             return pd.DataFrame([['Error', 'No valid promotional columns selected.']])\n", "\n", "#         # Ensure all promo columns exist in the dataframe\n", "#         missing_columns = [col for col in promo_columns if col not in df.columns]\n", "#         if missing_columns:\n", "#             error_msg = f\"Missing columns in dataframe: {missing_columns}\"\n", "#             logging.error(error_msg)\n", "#             return pd.DataFrame([['Error', error_msg]])\n", "\n", "#         # Prepare data for regression\n", "#         X = df[promo_columns]\n", "#         y = df[target_column]\n", "#         X = sm.add_constant(X)\n", "\n", "#         # Fit OLS regression model\n", "#         model = sm.OLS(y, X).fit()\n", "#         predictions = model.predict(X)\n", "\n", "#         # Metrics\n", "#         mape = np.mean(np.abs((y - predictions) / y)) * 100\n", "#         r_squared = model.rsquared\n", "#         adj_r_squared = model.rsquared_adj\n", "#         modeled_sales = predictions.sum()\n", "#         actual_sales = y.sum()\n", "\n", "#         # Initialize an empty list to store result rows instead of empty DataFrame\n", "#         result_rows = []\n", "\n", "#         # Total modeled activity for impact calculation\n", "#         total_activity = sum(df[c].sum() * model.params[c] for c in promo_columns if c in model.params)\n", "#         channel_impacts = {}\n", "#         for col in promo_columns:\n", "#             if col in model.params:\n", "#                 estimate = model.params[col]\n", "#                 activity = df[col].sum()\n", "#                 channel_impacts[col] = estimate * activity\n", "\n", "#         # Total impact is the sum of all individual channel impacts\n", "#         total_impact = sum(channel_impacts.values())\n", "#         for col in promo_columns:\n", "#             if col not in model.params:\n", "#                 continue\n", "\n", "#             estimate = model.params[col]\n", "#             p_value = model.pvalues[col]\n", "#             activity = df[col].sum()\n", "\n", "#             # Extract original variable name for linear activity\n", "#             parts = col.split(\"_\")\n", "\n", "#             if \"Tier\" in parts:\n", "#                 tier_index = parts.index(\"Tier\")\n", "#                 name = \"_\".join([parts[0], \"Tier\", parts[tier_index + 1]])\n", "#             else:\n", "#                 if \"Adstock\" in parts:\n", "#                     adstock_index = parts.index(\"Adstock\")\n", "#                     name = \"_\".join(parts[:adstock_index])\n", "#                 else:\n", "#                     name = parts[0]\n", "\n", "#             try:\n", "#                 linear_activity = df[name].sum()\n", "#             except KeyError:\n", "#                 linear_activity = activity\n", "\n", "#             target_sum = df[target_column].sum()\n", "#             impact_percentage = ((estimate * activity) / modeled_sales)*100 if modeled_sales != 0 else 0\n", "#             effectiveness = (estimate * linear_activity)\n", "\n", "#             # Append row to list instead of concatenating DataFrames\n", "#             result_rows.append({\n", "#                 'Channel': col,\n", "#                 'Estimate': estimate,\n", "#                 'Impact Percentage': impact_percentage,\n", "#                 'P-Value': p_value,\n", "#                 'Effectiveness': effectiveness,\n", "#                 'Linear Activity': linear_activity,\n", "#                 'R²': r_squared,\n", "#                 'Adjusted R²': adj_r_squared,\n", "#                 'Total Modeled Activity': activity,\n", "#                 'Modeled Sales': modeled_sales,\n", "#                 'Actual Sales': actual_sales\n", "#             })\n", "\n", "#         # Include Intercept (const) in result rows\n", "#         if 'const' in model.params:\n", "#             estimate = model.params['const']\n", "#             p_value = model.pvalues['const']\n", "#             impact=estimate*len(df)\n", "#             impact_percentage=(impact/modeled_sales)*100\n", "\n", "#             result_rows.append({\n", "#                 'Channel': 'Intercept',\n", "#                 'Estimate': estimate,\n", "#                 'Impact Percentage': impact_percentage,\n", "#                 'P-Value': p_value,\n", "#                 'Effectiveness': np.nan,\n", "#                 'Linear Activity': np.nan,\n", "#                 'R²': r_squared,\n", "#                 'Adjusted R²': adj_r_squared,\n", "#                 'Total Modeled Activity': np.nan,\n", "#                 'Modeled Sales': modeled_sales,\n", "#                 'Actual Sales': actual_sales\n", "#             })\n", "\n", "#         # Create result DataFrame from the list of rows after all rows are collected\n", "#         result_table = pd.DataFrame(result_rows)\n", "\n", "#         logging.info(\"OLS linear regression performed successfully.\")\n", "#         logging.info(f\"Final Regression DataFrame head:\\n{result_table.head()}\")\n", "#         return result_table\n", "\n", "#     except Exception as e:\n", "#         logging.error(f\"Error performing OLS linear regression: {e}\")\n", "#         import traceback\n", "#         logging.error(traceback.format_exc())\n", "#         return pd.DataFrame([['Error', str(e)]])\n", "\n", "\n", "# for this function one more column should be added in the result table one thta will calculate the no. of promo_columns and other column with list of significant columns\n", "def perform_regression(df, promo_columns, target_column, date_column, start_date, end_date):\n", "    \"\"\"\n", "    Perform regression analysis with the specified promotional columns.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promo_columns (list): List of promotional channel columns to use as predictors\n", "        target_column (str): Target variable name\n", "        date_column (str): Column containing date information\n", "        start_date: Start date for filtering data\n", "        end_date: End date for filtering data\n", "\n", "    Returns:\n", "        pd.DataFrame: Regression results table\n", "    \"\"\"\n", "\n", "    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    try:\n", "        # Make a copy of the input DataFrame to avoid modifying the original\n", "        df = df.copy()\n", "        final_df = df.copy()\n", "\n", "        if not promo_columns:\n", "            logging.error(\"No valid promotional columns selected for regression.\")\n", "            return pd.DataFrame([['Error', 'No valid promotional columns selected.']])\n", "\n", "        # Ensure all promo columns exist in the dataframe\n", "        missing_columns = [col for col in promo_columns if col not in df.columns]\n", "        if missing_columns:\n", "            error_msg = f\"Missing columns in dataframe: {missing_columns}\"\n", "            logging.error(error_msg)\n", "            return pd.DataFrame([['Error', error_msg]])\n", "\n", "        # Prepare data for regression\n", "        X = df[promo_columns]\n", "        y = df[target_column]\n", "        X = sm.add_constant(X)\n", "\n", "        # Fit OLS regression model\n", "        model = sm.OLS(y, X).fit()\n", "        predictions = model.predict(X)\n", "\n", "        # Metrics\n", "        mape = np.mean(np.abs((y - predictions) / y)) * 100\n", "        r_squared = model.rsquared\n", "        adj_r_squared = model.rsquared_adj\n", "        modeled_sales = predictions.sum()\n", "        actual_sales = y.sum()\n", "        \n", "        # Calculate RMSE\n", "        rmse = np.sqrt(np.mean((y - predictions) ** 2))\n", "        \n", "        # Get AIC\n", "        aic = model.aic\n", "\n", "        # Initialize an empty list to store result rows instead of empty DataFrame\n", "        result_rows = []\n", "\n", "        # Total modeled activity for impact calculation\n", "        total_activity = sum(df[c].sum() * model.params[c] for c in promo_columns if c in model.params)\n", "        channel_impacts = {}\n", "        for col in promo_columns:\n", "            if col in model.params:\n", "                estimate = model.params[col]\n", "                activity = df[col].sum()\n", "                channel_impacts[col] = estimate * activity\n", "\n", "        # Total impact is the sum of all individual channel impacts\n", "        total_impact = sum(channel_impacts.values())\n", "        for col in promo_columns:\n", "            if col not in model.params:\n", "                continue\n", "\n", "            estimate = model.params[col]\n", "            p_value = model.pvalues[col]\n", "            activity = df[col].sum()\n", "\n", "            # Extract original variable name for linear activity\n", "            parts = col.split(\"_\")\n", "\n", "            if \"Tier\" in parts:\n", "                tier_index = parts.index(\"Tier\")\n", "                name = \"_\".join([parts[0], \"Tier\", parts[tier_index + 1]])\n", "            else:\n", "                if \"Adstock\" in parts:\n", "                    adstock_index = parts.index(\"Adstock\")\n", "                    name = \"_\".join(parts[:adstock_index])\n", "                else:\n", "                    name = parts[0]\n", "\n", "            try:\n", "                linear_activity = df[name].sum()\n", "            except KeyError:\n", "                linear_activity = activity\n", "\n", "            target_sum = df[target_column].sum()\n", "            impact_percentage = ((estimate * activity) / modeled_sales)*100 if modeled_sales != 0 else 0\n", "            effectiveness = (estimate * linear_activity)\n", "            significance= p_value<0.05\n", "            channel_count=len(promo_columns)\n", "            # Append row to list instead of concatenating DataFrames\n", "            result_rows.append({\n", "                'Channel': col,\n", "                'Estimate': estimate,\n", "                'Impact Percentage': impact_percentage,\n", "                'P-Value': p_value,\n", "                'Effectiveness': effectiveness,\n", "                'Linear Activity': linear_activity,\n", "                'R²': r_squared,\n", "                'Adjusted R²': adj_r_squared,\n", "                'AIC': aic,\n", "                'RMSE': rmse,\n", "                'Total Modeled Activity': activity,\n", "                'Modeled Sales': modeled_sales,\n", "                'Actual Sales': actual_sales,\n", "                'Channel_count':channel_count,\n", "                'significance':significance\n", "\n", "                \n", "            })\n", "\n", "        # Include Intercept (const) in result rows\n", "        if 'const' in model.params:\n", "            estimate = model.params['const']\n", "            p_value = model.pvalues['const']\n", "            impact=estimate*len(df)\n", "            impact_percentage=(impact/modeled_sales)*100\n", "\n", "            result_rows.append({\n", "                'Channel': 'Intercept',\n", "                'Estimate': estimate,\n", "                'Impact Percentage': impact_percentage,\n", "                'P-Value': p_value,\n", "                'Effectiveness': np.nan,\n", "                'Linear Activity': np.nan,\n", "                'R²': r_squared,\n", "                'Adjusted R²': adj_r_squared,\n", "                'AIC': aic,\n", "                'RMSE': rmse,\n", "                'Total Modeled Activity': np.nan,\n", "                'Modeled Sales': modeled_sales,\n", "                'Actual Sales': actual_sales,\n", "                'Channel_count':channel_count,\n", "                'significance':0\n", "            })\n", "\n", "        # Create result DataFrame from the list of rows after all rows are collected\n", "        result_table = pd.DataFrame(result_rows)\n", "\n", "        logging.info(\"OLS linear regression performed successfully.\")\n", "        logging.info(f\"Final Regression DataFrame head:\\n{result_table.head()}\")\n", "        return result_table\n", "\n", "    except Exception as e:\n", "        logging.error(f\"Error performing OLS linear regression: {e}\")\n", "        import traceback\n", "        logging.error(traceback.format_exc())\n", "        return pd.DataFrame([['Error', str(e)]])\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "9a155288", "metadata": {}, "source": ["# Functions to process data"]}, {"cell_type": "code", "execution_count": 5, "id": "d72fce9f", "metadata": {}, "outputs": [], "source": ["def process_main_data(file_path,promo_channels,target_column,id_column,date_column,date_format,start_date,end_date, model_run_id=None,db_conn=None):\n", "    \"\"\"\n", "    Clean and process the main data and update the PostgreSQL database directly.\n", "    \n", "    Args:\n", "        file_path (str): Path to the input file\n", "        model_run_id (UUID, optional): UUID of the model run to update in the database\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed main dataframe and related metadata\n", "    \"\"\"\n", "\n", "    \n", "    try:\n", "        # Get column names and date format\n", "        df = load_file(file_path)\n", "        unit_price=get_unit_price()\n", "        # Process dates\n", "        df = process_dates(df, date_column,date_format)\n", "        \n", "        # Ensure numeric columns\n", "        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()\n", "        for col in numeric_columns:\n", "            df[col] = pd.to_numeric(df[col], errors='coerce')\n", "        df = df.drop_duplicates()\n", "        logging.info(\"Data successfully loaded and cleaned.\")\n", "        \n", "        # Create lag variable\n", "        df[f\"{target_column}_lag_1\"] = df.groupby(id_column)[target_column].shift(1).fillna(0)\n", "        df = df.sort_values(by=[id_column, date_column])\n", "        \n", "        # Handle outliers\n", "        df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)\n", "        \n", "        # If model_run_id is provided and we have a connection, update the database directly\n", "        if model_run_id and db_conn:\n", "            try:\n", "                # Create a cursor object\n", "                cursor = db_conn.cursor()\n", "                \n", "                # SQL to update model_data table\n", "                sql = \"\"\"\n", "                    UPDATE model_data\n", "                    SET \n", "                        date_column = %s,\n", "                        id_column = %s,\n", "                        target_column = %s,\n", "                        promotional_columns = %s,\n", "                        start_date = %s,\n", "                        end_date = %s,\n", "                        date_format = %s,\n", "                        unit_price = %s,\n", "                        updated_at = CURRENT_TIMESTAMP\n", "                    WHERE model_run_id = %s\n", "                \"\"\"\n", "                \n", "                # Execute the update query with the values\n", "                cursor.execute(sql, [\n", "                    date_column,\n", "                    id_column,\n", "                    target_column,\n", "                    promo_channels,  # This should be an array\n", "                    start_date,\n", "                    end_date,\n", "                    date_format,\n", "                    unit_price,\n", "                    model_run_id\n", "                ])\n", "                \n", "                # Commit the transaction\n", "                db_conn.commit()\n", "                \n", "                # Close the cursor (but not the connection)\n", "                cursor.close()\n", "                \n", "                logging.info(f\"Successfully updated model_data for model_run_id: {model_run_id}\")\n", "                \n", "            except Exception as e:\n", "                logging.error(f\"Error updating database in process_main_data: {str(e)}\")\n", "                # Try to rollback if possible\n", "                try:\n", "                    db_conn.rollback()\n", "                except:\n", "                    pass\n", "        \n", "        return df,unit_price\n", "    \n", "    except Exception as e:\n", "        logging.error(f\"Error processing main data: {str(e)}\")\n", "        raise\n", "\n", "def process_spend_data(spends_file_path,spend_column,spend_channel_column, model_run_id=None,db_conn=None):\n", "    \"\"\"\n", "    Process spend data and update the PostgreSQL database directly\n", "    \n", "    Args:\n", "        spends_file_path (str): Path to the spend data file\n", "        model_run_id (UUID, optional): UUID of the model run to update in the database\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed spend dataframe\n", "    \"\"\"\n", "\n", "    \n", "    df = load_file(spends_file_path)\n", "    \n", "    # Define spend column names (hardcoded for testing)\n", "    \n", "    \n", "    # Check if spend columns exist in the dataframe\n", "    if spend_column not in df.columns:\n", "        error_msg = f\"ERROR: Column '{spend_column}' not found in the spend dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "        \n", "    if spend_channel_column not in df.columns:\n", "        error_msg = f\"ERROR: Column '{spend_channel_column}' not found in the spend dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "    \n", "    # If model_run_id is provided and we have a database connection, update directly\n", "    if model_run_id and db_conn:\n", "        try:\n", "            # Create a cursor object\n", "            cursor = db_conn.cursor()\n", "            \n", "            # SQL to update model_data table\n", "            sql = \"\"\"\n", "                UPDATE model_data\n", "                SET \n", "                    spend_channel_column = %s,\n", "                    spend_column = %s,\n", "                    updated_at = CURRENT_TIMESTAMP\n", "                WHERE model_run_id = %s\n", "            \"\"\"\n", "            \n", "            # Execute the update query with the values\n", "            cursor.execute(sql, [\n", "                spend_channel_column,\n", "                spend_column,\n", "                model_run_id\n", "            ])\n", "            \n", "            # Commit the transaction\n", "            db_conn.commit()\n", "            \n", "            # Close the cursor (but not the connection)\n", "            cursor.close()\n", "            \n", "            logging.info(f\"Successfully updated spend data for model_run_id: {model_run_id}\")\n", "            \n", "        except Exception as e:\n", "            logging.error(f\"Error updating database in process_spend_data: {str(e)}\")\n", "            # Try to rollback if possible\n", "            try:\n", "                db_conn.rollback()\n", "            except:\n", "                pass\n", "    \n", "    return df\n", "\n", "def process_historical_data(historical_file_path,historical_impact,historical_channel_column, model_run_id=None,db_conn=None):\n", "    \"\"\"\n", "    Process historical impact data and update the PostgreSQL database directly\n", "    \n", "    Args:\n", "        historical_file_path (str): Path to the historical data file\n", "        model_run_id (UUID, optional): UUID of the model run to update in the database\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed historical dataframe\n", "    \"\"\"\n", "\n", "    df = load_file(historical_file_path)\n", "    \n", "    # Define historical column names (hardcoded for testing)\n", "   \n", "    \n", "    # Check if historical columns exist in the dataframe\n", "    if historical_impact not in df.columns:\n", "        error_msg = f\"ERROR: Column '{historical_impact}' not found in the historical dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "        \n", "    if historical_channel_column not in df.columns:\n", "        error_msg = f\"ERROR: Column '{historical_channel_column}' not found in the historical dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "    \n", "    # Additional processing specific to historical data can be added here\n", "    print(f\"Processed historical data with columns: {', '.join(df.columns)}\")\n", "    \n", "    # If model_run_id is provided and we have a database connection, update directly\n", "    if model_run_id and db_conn:\n", "        try:\n", "            # Create a cursor object\n", "            cursor = db_conn.cursor()\n", "            \n", "            # SQL to update model_data table\n", "            sql = \"\"\"\n", "                UPDATE model_data\n", "                SET \n", "                    historical_channel_column = %s,\n", "                    historical_impact = %s,\n", "                    updated_at = CURRENT_TIMESTAMP\n", "                WHERE model_run_id = %s\n", "            \"\"\"\n", "            \n", "            # Execute the update query with the values\n", "            cursor.execute(sql, [\n", "                historical_channel_column,\n", "                historical_impact,\n", "                model_run_id\n", "            ])\n", "            \n", "            # Commit the transaction\n", "            db_conn.commit()\n", "            \n", "            # Close the cursor (but not the connection)\n", "            cursor.close()\n", "            \n", "            logging.info(f\"Successfully updated historical data for model_run_id: {model_run_id}\")\n", "            \n", "        except Exception as e:\n", "            logging.error(f\"Error updating database in process_historical_data: {str(e)}\")\n", "            # Try to rollback if possible\n", "            try:\n", "                db_conn.rollback()\n", "            except:\n", "                pass\n", "    \n", "    return df"]}, {"cell_type": "markdown", "id": "999828fc", "metadata": {}, "source": ["# Selecct best Adstock functions"]}, {"cell_type": "code", "execution_count": 6, "id": "d7f80c71", "metadata": {}, "outputs": [], "source": ["def get_adstock_ranges_from_gemini(promo_channels):\n", "    \"\"\"Use Gemini API to get recommended adstock ranges for different promotion types\"\"\"\n", "    prompt = f\"\"\"\n", "    For the following promotional channels in pharmaceutical marketing, provide the ideal adstock decay rate ranges.\n", "\n", "    For adstock settings, categorize promotions into two groups: Personal Promotions and Non-Personal Promotions. Set the adstock range for Personal Promotions (including PDE, Call, Call Activity, and TV)\n", "    between 70-80. For Non-Personal Promotions (such as Display and Banner), set the adstock range between 20-50.\n", "    Only Speaker programs or conferences can have adstock range from 70-90\n", "    Personal promotions include: Calls, PDE, TV etc\n", "    Non-Personal include: Banners ,Headlines ,Ads etc\n", "    Use the web and gather additional insights on these promotional activities, their impact on sales,\n", "    and industry best practices. Incorporate any relevant findings to refine adstock selection and improve model accuracy.\n", "\n", "    Categorize each channel as either \"Personal Promotion\", \"Non-Personal Promotion\", or \"Other\" and provide a\n", "    recommended min and max adstock decay rate (as percentages between 10 and 90).\n", "\n", "    Channels: {', '.join(promo_channels)}\n", "\n", "    Return results in JSON format with this structure:\n", "    {{\n", "        \"Channel_Name\": {{\n", "            \"type\": \"Personal Promotion|Non-Personal Promotion|Other\",\n", "            \"min_adstock\": 10-90,\n", "            \"max_adstock\": 10-90\n", "        }},\n", "        ...\n", "    }}\n", "\n", "    Ensure min_adstock is less than max_adstock. Base your ranges on typical pharmaceutical marketing benchmarks.\n", "    \"\"\"\n", "\n", "    try:\n", "        response = model.generate_content(prompt)\n", "        raw_output = response.text.strip()\n", "        raw_output = raw_output.replace(\"```json\", \"\").replace(\"```\", \"\").strip()\n", "        adstock_ranges = json.loads(raw_output)\n", "\n", "        logging.info(f\"Retrieved adstock ranges from Gemini API: {adstock_ranges}\")\n", "        return adstock_ranges\n", "    except Exception as e:\n", "        logging.error(f\"Error getting adstock ranges from Gemini: {e}\")\n", "        # Fallback default ranges if API call fails\n", "        default_ranges = {}\n", "        for channel in promo_channels:\n", "            default_ranges[channel] = {\n", "                \"type\": \"Other\",\n", "                \"min_adstock\": 10,\n", "                \"max_adstock\": 90\n", "            }\n", "        return default_ranges\n", "\n", "def get_channel_adstock_range(promo_col):\n", "    \"\"\"\n", "    Get recommended adstock range for a specific promotional channel.\n", "\n", "    Args:\n", "        promo_col (str): Name of the promotional channel column\n", "\n", "    Returns:\n", "        dict: Dictionary with channel type and min/max adstock values\n", "    \"\"\"\n", "    # Get adstock ranges for all promo channels\n", "    all_adstock_ranges = get_adstock_ranges_from_gemini([promo_col])\n", "    print(f\"All adstock ranges:{all_adstock_ranges}\")\n", "    # Get recommended range for this channel\n", "    channel_range = all_adstock_ranges.get(promo_col, {\n", "        \"type\": \"Other\",\n", "        \"min_adstock\": 10,\n", "        \"max_adstock\": 90\n", "    })\n", "\n", "    return channel_range\n", "def get_transformation_functions():\n", "    def safe_power(x, exp):\n", "        return np.power(np.clip(x, 0, None), exp)\n", "\n", "    def safe_log(x):\n", "        return np.log1p(np.clip(x, 0, None))\n", "\n", "    return {\n", "        \"Log\": safe_log,\n", "        # \"Root1\": lambda x: safe_power(x, 1/10),\n", "        # \"Root2\": lambda x: safe_power(x, 2/10),\n", "        # \"Root3\": lambda x: safe_power(x, 3/10),\n", "        \"Root4\": lambda x: safe_power(x, 4/10),\n", "        \"Root5\": lambda x: safe_power(x, 5/10),\n", "        \"Root6\": lambda x: safe_power(x, 6/10),\n", "        \"Root7\": lambda x: safe_power(x, 7/10),\n", "        \"Root8\": lambda x: safe_power(x, 8/10),\n", "        \"Root9\": lambda x: safe_power(x, 9/10)\n", "        # \"Sigmoid\": lambda x: 1 / (1 + np.exp(-x))  # Optional, safe by design\n", "    }\n", "\n", "def apply_adstock(df, promo_col, id_col, adstock_rate):\n", "    \"\"\"\n", "    Apply adstock transformation to a promotional channel with specified rate.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promo_col (str): Name of promotional column\n", "        id_col (str): Column that identifies time series groups\n", "        adstock_rate (int): Adstock rate (0-100)\n", "\n", "    Returns:\n", "        tuple: (Column name, Series with adstocked values)\n", "    \"\"\"\n", "    # print(f\"apply_adedstock length: {len(df)}\")\n", "    rate = adstock_rate / 100\n", "    adstocked = []\n", "\n", "    for _, group in df.groupby(id_col):\n", "        cumulative = 0\n", "        group_adstocked = []\n", "        for value in group[promo_col]:\n", "            cumulative = value + rate * cumulative\n", "            group_adstocked.append(cumulative)\n", "        adstocked.extend(group_adstocked)\n", "\n", "    adstock_col = f\"{promo_col}_Adstock_{adstock_rate}\"\n", "    return adstock_col, pd.Series(adstocked, index=df.index)\n", "\n", "def apply_transformation(series, transform_name, transform_func, base_col_name):\n", "    \"\"\"\n", "    Apply transformation function to a series.\n", "\n", "    Args:\n", "        series (pd.Series): Data to transform\n", "        transform_name (str): Name of transformation\n", "        transform_func (callable): Transformation function\n", "        base_col_name (str): Base column name\n", "\n", "    Returns:\n", "        tuple: (Column name, Transformed series)\n", "    \"\"\"\n", "    # print(f\"apply_transformation length: {len(df)}\")\n", "    transformed_col = f\"{base_col_name}_{transform_name}\"\n", "    transformed_series = transform_func(series)\n", "    return transformed_col, transformed_series\n", "def fit_regression(df, feature_col,date_column, target_column,start_date,end_date, control_variables=None):\n", "    \"\"\"\n", "    Fit regression model and return key metrics.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        feature_col (str): Feature column name\n", "        target_column (str): Target column name\n", "        control_variables (list): Additional control variables\n", "\n", "    Returns:\n", "        dict: Regression metrics or None if error\n", "    \"\"\"\n", "\n", "    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    # print(f\"Fit_Regression length: {len(df)}\")\n", "    if control_variables is None:\n", "        control_variables = []\n", "\n", "    try:\n", "        features = [feature_col] +[f\"{target_column}_lag_1\"]\n", "        # features = [feature_col] + control_variables +[f\"{target_column}_lag_1\"]\n", "        X = df[features].copy()\n", "        X = sm.add_constant(X)\n", "        y = df[target_column]\n", "\n", "        model = sm.OLS(y, X).fit()\n", "\n", "        return {\n", "            'r_squared': model.rsquared,\n", "            'p_value': model.pvalues[feature_col],\n", "            'aic': model.aic\n", "        }\n", "    except Exception as e:\n", "        logging.error(f\"Error fitting model for {feature_col}: {e}\")\n", "        return None\n", "    \n", "def normalize_metric(values, higher_is_better=True):\n", "    \"\"\"\n", "    Normalize metric values to 0-1 range.\n", "\n", "    Args:\n", "        values (array): Metric values\n", "        higher_is_better (bool): Whether higher values are better\n", "\n", "    Returns:\n", "        array: Normalized values\n", "    \"\"\"\n", "    if len(values) <= 1:\n", "        return np.ones_like(values)\n", "\n", "    min_val = np.min(values)\n", "    max_val = np.max(values)\n", "\n", "    if max_val == min_val:\n", "        return np.ones_like(values)\n", "\n", "    if higher_is_better:\n", "        return (values - min_val) / (max_val - min_val)\n", "    else:\n", "        return (max_val - values) / (max_val - min_val)\n", "def calculate_composite_score_transformation(results_df):\n", "    \"\"\"\n", "    Calculate composite score based on metrics and business rules.\n", "\n", "    Args:\n", "        results_df (pd.DataFrame): DataFrame with model results\n", "\n", "    Returns:\n", "        pd.DataFrame: DataFrame with scores added\n", "    \"\"\"\n", "    # Normalize metrics\n", "    results_df['r_squared_norm'] = normalize_metric(results_df['r_squared'].values, higher_is_better=True)\n", "    results_df['p_value_norm'] = normalize_metric(results_df['p_value'].values, higher_is_better=False)\n", "    results_df['aic_norm'] = normalize_metric(results_df['aic'].values, higher_is_better=False)\n", "\n", "    # Apply business score based on recommended range\n", "    results_df['business_score'] = results_df['in_range'].apply(lambda x: 1.0 if x else 0.5)\n", "\n", "    # Apply weights and calculate composite score\n", "    # Mathematical metrics (100%)\n", "    # R²: 40%, p-value: 20%, AIC: 40%\n", "    results_df['math_score'] = (\n", "        0.4 * results_df['r_squared_norm'] +\n", "        0.2 * results_df['p_value_norm'] +\n", "        0.4 * results_df['aic_norm']\n", "    )\n", "\n", "    # Final composite score - business score can boost combinations within recommended range\n", "    results_df['final_score'] = 0.5*results_df['math_score'] +0.5* results_df['business_score']\n", "\n", "    return results_df\n", "def select_best_adstock_and_transformation(df, promo_col, id_col, target_column,date_column,start_date,end_date,control_variables=None):\n", "    global temp_df, global_feature_df\n", "    import gc\n", "    \"\"\"\n", "    Select the top 3 adstock rate and transformation combinations for a promotional channel\n", "    using composite scoring of multiple metrics. Include control variables in the regression.\n", "\n", "    Optimized to avoid dataframe fragmentation.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promo_col (str): Name of promotional column\n", "        id_col (str): Column that identifies time series groups\n", "        target_column (str): Target variable name\n", "        control_variables (list): Additional control variables to include in regression\n", "\n", "    Returns:\n", "        tuple: (Updated dataframe, list of top 3 transformed column names)\n", "    \"\"\"\n", "    print(f\"\\nSelecting top 3 adstock and transformation combinations for: {promo_col}\")\n", "\n", "    \n", "\n", "    # Get channel info\n", "    channel_range = get_channel_adstock_range(promo_col)\n", "    min_adstock = channel_range[\"min_adstock\"]\n", "    max_adstock = channel_range[\"max_adstock\"]\n", "    channel_type = channel_range[\"type\"]\n", "\n", "    print(f\"Channel type: {channel_type}, Recommended adstock range: {min_adstock}% to {max_adstock}%\")\n", "\n", "    # Get transformation functions\n", "    transformations = get_transformation_functions()\n", "\n", "    # Variables to store results and new columns to be added\n", "    results = []\n", "    new_columns = {}\n", "    new_global_columns = {}  # To store all columns for global_feature_df\n", "\n", "    \n", "\n", "    # Iterate over all adstock rates\n", "    for adstock_rate in range(10, 100, 10):\n", "        # Apply adstock\n", "        temp_df = df.copy()\n", "        adstock_col, adstocked_series = apply_adstock(df, promo_col, id_col, adstock_rate)\n", "        new_columns[adstock_col] = adstocked_series\n", "        temp_df[adstock_col] = adstocked_series\n", "\n", "        # Store for later batch addition to global_feature_df\n", "        new_global_columns[adstock_col] = adstocked_series\n", "\n", "        # Check if adstock rate is within recommended range (business context)\n", "        in_recommended_range = min_adstock <= adstock_rate <= max_adstock\n", "\n", "        # Apply all transformations to this adstocked column\n", "        for transform_name, transform_func in transformations.items():\n", "            transformed_col, transformed_series = apply_transformation(\n", "                temp_df[adstock_col], transform_name, transform_func, adstock_col\n", "            )\n", "            new_columns[transformed_col] = transformed_series\n", "            temp_df[transformed_col] = transformed_series\n", "\n", "            # Store for later batch addition to global_feature_df\n", "            new_global_columns[transformed_col] = transformed_series\n", "\n", "            # Fit regression and get metrics\n", "            metrics = fit_regression(temp_df, transformed_col,date_column,target_column,start_date,end_date, control_variables)\n", "\n", "            if metrics:\n", "                # Add to results\n", "                results.append({\n", "                    'adstock_rate': adstock_rate,\n", "                    'transform': transform_name,\n", "                    'col_name': transformed_col,\n", "                    'r_squared': metrics['r_squared'],\n", "                    'p_value': metrics['p_value'],\n", "                    'aic': metrics['aic'],\n", "                    'in_range': in_recommended_range\n", "                })\n", "    del temp_df\n", "    # Add all new columns to global_feature_df at once to avoid fragmentation\n", "    # if new_global_columns:\n", "    #     global_feature_df = pd.concat([global_feature_df, pd.DataFrame(new_global_columns)], axis=1)\n", "    #     # Make a copy to defragment after all additions\n", "    #     global_feature_df = global_feature_df.copy()\n", "\n", "    # Check if we have valid results\n", "    if not results:\n", "        logging.error(f\"No valid models found for {promo_col}\")\n", "        return df, [], []\n", "\n", "    # Convert results to DataFrame\n", "    results_df = pd.DataFrame(results)\n", "\n", "    # Calculate scores\n", "    results_df = calculate_composite_score_transformation(results_df)\n", "\n", "\n", "    # Get the top 3 combinations\n", "    results_df = results_df.sort_values('final_score', ascending=False)\n", "    top_3_rows = results_df.head(3)\n", "\n", "    # Create lists to store top 3 columns\n", "    top_adstock_cols = []\n", "    top_transformed_cols = []\n", "\n", "    # Print details of the top 3 combinations\n", "    print(f\"\\nTop 3 combinations selected for {promo_col}:\")\n", "\n", "    for i, (_, row) in enumerate(top_3_rows.iterrows(), 1):\n", "        adstock_col = f\"{promo_col}_Adstock_{int(row['adstock_rate'])}\"\n", "        transformed_col = row['col_name']\n", "\n", "        top_adstock_cols.append(adstock_col)\n", "        top_transformed_cols.append(transformed_col)\n", "\n", "        print(f\"\\nCombination #{i}:\")\n", "        print(f\"- Adstock rate: {int(row['adstock_rate'])}%\")\n", "        print(f\"- Transformation: {row['transform']}\")\n", "        print(f\"- R²: {row['r_squared']:.4f}\")\n", "        print(f\"- p-value: {row['p_value']:.4f}\")\n", "        print(f\"- AIC: {row['aic']:.4f}\")\n", "        print(f\"- In recommended range: {'Yes' if row['in_range'] else 'No'}\")\n", "        print(f\"- Final score: {row['final_score']:.4f}\")\n", "\n", "    # Only add the necessary columns to the original dataframe using concat\n", "    # This includes all columns needed for the top 3 combinations\n", "    columns_to_add = {}\n", "    for col in set(top_adstock_cols + top_transformed_cols):\n", "        columns_to_add[col] = new_columns[col]\n", "\n", "    new_df = pd.concat([df, pd.DataFrame(columns_to_add)], axis=1)\n", "\n", "    return new_df, top_adstock_cols, top_transformed_cols,channel_range\n", "\n", "def process_promo_channels(df,promo_channels,target_column,id_column,date_column,start_date,end_date,control_variables):\n", "    \"\"\"Process each promotional channel to find best transformations\"\"\"\n", "   \n", "    \n", "    # Dictionary to store top transformed columns for each promo channel\n", "    transformed_channels_by_promo = {}\n", "    adstocked_channels_by_promo = {}\n", "    adstock_range_channel = {}\n", "    all_transformed_features = []\n", "\n", "    for promo_col in promo_channels:\n", "        print(f\"\\nProcessing promotional channel: {promo_col}\")\n", "\n", "        # Use the modified method to get top 3 combinations with control variables\n", "        df, best_adstock_cols, best_transformed_cols, adstock_range = select_best_adstock_and_transformation(\n", "            df, promo_col, id_column, target_column,date_column, start_date, end_date, control_variables\n", "        )\n", "\n", "        print(f\"- Best Adstock columns: {', '.join(best_adstock_cols)}\")\n", "        print(f\"- Best Transformed columns: {', '.join(best_transformed_cols)}\")\n", "\n", "        # Store the top 3 transformed columns for this promo channel\n", "        adstocked_channels_by_promo[promo_col] = best_adstock_cols\n", "        adstock_range_channel[promo_col] = adstock_range\n", "        transformed_channels_by_promo[promo_col] = best_transformed_cols\n", "        all_transformed_features.extend(best_transformed_cols)\n", "\n", "    if not all_transformed_features:\n", "        return False, df, {}, {}, {}, [] \n", "    \n", "    return True,df,transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_transformed_features"]}, {"cell_type": "code", "execution_count": 7, "id": "aa434997", "metadata": {}, "outputs": [], "source": ["def get_benchmark_values(promo_channels, historical_df,historical_channel_column,historical_impact):\n", "    \"\"\"\n", "    Gets benchmark values for promotional channels, asking only once per base channel\n", "    and applying that value to all its transformations.\n", "\n", "    Args:\n", "        promo_channels: List of transformed promotional channel names\n", "\n", "    Returns:\n", "        Dictionary mapping each transformed channel to its benchmark value\n", "    \"\"\"\n", "    \n", "    # Identify base channel names and group transformations\n", "    base_channels = {}\n", "\n", "    # Common transformation indicators that would appear in transformed channel names\n", "    transform_indicators = ['_adstock', '_decay', '_power', '_lag', '_transform', '_delayed']\n", "\n", "    for channel in promo_channels:\n", "        # Find if this is a transformed channel by looking for transformation indicators\n", "        is_transformed = False\n", "        base_name = channel\n", "\n", "        for indicator in transform_indicators:\n", "            if indicator.lower() in channel.lower():\n", "                is_transformed = True\n", "                # Extract the base name (everything before the transformation indicator)\n", "                parts = channel.lower().split(indicator.lower(), 1)\n", "                base_name = parts[0].rstrip('_')\n", "                break\n", "\n", "        # If no transformation indicator found, it's likely a base channel itself\n", "        if not is_transformed:\n", "            # For channels with format like \"Channel_Adstock10\", try to detect numbers\n", "            numeric_split = re.match(r'(.+?)(\\d+)$', channel)\n", "            if numeric_split:\n", "                base_name = numeric_split.group(1).rstrip('_')\n", "\n", "        # Store in our dictionary\n", "        if base_name not in base_channels:\n", "            base_channels[base_name] = []\n", "        base_channels[base_name].append(channel)\n", "\n", "    # Get benchmark values for each base channel\n", "    typical_values = {}\n", "    \n", "    d={str(k).lower(): v for k, v in zip(historical_df[historical_channel_column], historical_df[historical_impact])}\n", "    \n", "\n", "    # Ask for benchmark values only once per base channel\n", "    for base_name, transformed_channels in base_channels.items():\n", "        try:\n", "            # benchmark = float(input(f\"Enter benchmark for channel: {base_name}\"))\n", "            benchmark=d[base_name]\n", "\n", "            # Apply this benchmark to all transformations of this base channel\n", "            for channel in transformed_channels:\n", "                typical_values[channel] = benchmark\n", "\n", "        except ValueError:\n", "            print(f\"Invalid input for {base_name}. Using default value of 0.\")\n", "            for channel in transformed_channels:\n", "                typical_values[channel] = 0.0\n", "\n", "    return typical_values\n", "\n", "def filter_dataframe_by_date(df, date_column, start_date, end_date):\n", "    \"\"\"Filter dataframe by start and end dates\"\"\"\n", "   \n", "    \n", "    print(\"\\nSelecting optimal channel combination...\")\n", "    print(f\"before optimal channel selection {len(df)}\")\n", "\n", "    # Filter the dataframe before optimal channel selection\n", "    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    if start_date and end_date:\n", "            df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "84eac2fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "252f0528", "metadata": {}, "source": ["# Params setup"]}, {"cell_type": "code", "execution_count": 8, "id": "89605493", "metadata": {}, "outputs": [], "source": ["def setup_parameters(date_column, id_column, target_column, promo_channels, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact):\n", "    \"\"\"Set up the parameters dictionary for modeling\"\"\"\n", "    global params\n", "    \n", "    params = {\n", "        'main_dataset': {\n", "            'date_column': date_column,\n", "            'id_column': id_column,\n", "            'target_column': target_column,\n", "            'promo_channels': promo_channels,\n", "            'normalization_method': 'Percentile',\n", "            'population_column': None,\n", "            'start_date': start_date,\n", "            'end_date': end_date,\n", "            'data_level': 'HCP'\n", "        },\n", "        'spend_dataset': {\n", "            'channel_column': spend_channel_column,\n", "            'spend_column': spend_column\n", "        },\n", "        'historical_dataset': {\n", "            'channel_column': historical_channel_column,\n", "            'historical_impact': historical_impact\n", "        }\n", "    }\n", "    \n", "    return params"]}, {"cell_type": "markdown", "id": "625a5615", "metadata": {}, "source": ["# select optimal channels"]}, {"cell_type": "code", "execution_count": 9, "id": "c7a18277", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import statsmodels.api as sm\n", "import itertools\n", "import logging\n", "import traceback\n", "\n", "def evaluate_model(df, features, target_column, benchmark_values=None):\n", "    \"\"\"\n", "    Evaluate linear regression model with given features.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input data\n", "        features (list): List of feature columns\n", "        target_column (str): Target variable name\n", "        benchmark_values (dict): Dictionary of benchmark values\n", "\n", "    Returns:\n", "        dict: Dictionary of model metrics\n", "    \"\"\"\n", "    # Prepare data\n", "    X = df[features].dropna()\n", "    y = df.loc[X.index, target_column]\n", "\n", "    if len(X) < len(features) + 2:\n", "        print(f\"  Not enough data points ({len(X)}) for {len(features)} features\")\n", "        return None\n", "\n", "    # Add constant for intercept\n", "    X_with_const = sm.add_constant(X)\n", "\n", "    try:\n", "        # Fit model\n", "        model = sm.OLS(y, X_with_const).fit()\n", "\n", "        # Get coefficients\n", "        coefficients = model.params.drop('const').to_dict() if 'const' in model.params else model.params.to_dict()\n", "        target_sum=df[target_column].sum()\n", "\n", "        for col in features:\n", "          estimate = model.params[col]\n", "          activity = df[col].sum()\n", "\n", "          impact_percentage = ((estimate * activity) / target_sum) if target_sum != 0 else 0\n", "\n", "        # Calculate p-values for features\n", "        p_values = model.pvalues.drop('const').to_dict() if 'const' in model.pvalues else model.pvalues.to_dict()\n", "        avg_p_value = np.mean(list(p_values.values()))\n", "\n", "        # Calculate benchmark deviations if benchmarks provided\n", "        benchmark_deviations = {}\n", "        if benchmark_values:\n", "            for feature in coefficients:\n", "                if feature in benchmark_values:\n", "                    benchmark_val = benchmark_values[feature]\n", "                    model_val = impact_percentage\n", "                    relative_deviation = (model_val - benchmark_val) / ((benchmark_val) + 1e-6)\n", "                    benchmark_deviations[feature] = relative_deviation\n", "\n", "        avg_benchmark_deviation = np.mean(list(benchmark_deviations.values())) if benchmark_deviations else 0\n", "\n", "        # Return metrics\n", "        return {\n", "            'R2': model.rsquared,\n", "            'adj_R2': model.rsquared_adj,\n", "            'AIC': model.aic,\n", "            'BIC': model.bic,\n", "            'coefficients': coefficients,\n", "            'p_values': p_values,\n", "            'avg_p_value': avg_p_value,\n", "            'benchmark_deviations': benchmark_deviations,\n", "            'avg_benchmark_deviation': avg_benchmark_deviation\n", "        }\n", "\n", "    except Exception as e:\n", "        print(f\"  Error evaluating model: {str(e)}\")\n", "        return None\n", "\n", "\n", "\n", "def calculate_incremental_impact(baseline_model, augmented_model):\n", "    \"\"\"\n", "    Calculate the impact of adding a new variable to the model.\n", "\n", "    Args:\n", "        baseline_model (dict): Metrics from baseline model\n", "        augmented_model (dict): Metrics from augmented model (with additional variable)\n", "\n", "    Returns:\n", "        float: Incremental impact score\n", "    \"\"\"\n", "    if not baseline_model or not augmented_model:\n", "        return 0.0\n", "\n", "    baseline_coefs = baseline_model['coefficients']\n", "    augmented_coefs = augmented_model['coefficients']\n", "\n", "    # Get common coefficients\n", "    common_features = set(baseline_coefs.keys()) & set(augmented_coefs.keys())\n", "\n", "    changes = []\n", "    epsilon = 1e-6  # Small value to avoid division by zero\n", "\n", "    for feat in common_features:\n", "        baseline_val = baseline_coefs[feat]\n", "        augmented_val = augmented_coefs[feat]\n", "\n", "        relative_change = (augmented_val - baseline_val) / ((baseline_val) + epsilon)\n", "        changes.append(relative_change)\n", "\n", "    # Return average relative change\n", "    return np.mean(changes) if changes else 0.0\n", "def normalize_metric_channel(value, min_val, max_val, higher_is_better=True):\n", "    \"\"\"\n", "    Normalize a metric to a 0-1 scale.\n", "\n", "    Args:\n", "        value (float): Metric value\n", "        min_val (float): Minimum value in range\n", "        max_val (float): Maximum value in range\n", "        higher_is_better (bool): Whether higher values are better\n", "\n", "    Returns:\n", "        float: Normalized value\n", "    \"\"\"\n", "    if max_val <= min_val:\n", "        return 0.5  # De<PERSON><PERSON> if range is invalid\n", "\n", "    # Clip value to range\n", "    value = max(min_val, min(max_val, value))\n", "\n", "    # Normalize\n", "    if higher_is_better:\n", "        return (value - min_val) / (max_val - min_val)\n", "    else:\n", "        return 1 - ((value - min_val) / (max_val - min_val))\n", "\n", "def calculate_composite_score(metrics, ranges, weights):\n", "    \"\"\"\n", "    Calculate composite score based on multiple metrics.\n", "\n", "    Args:\n", "        metrics (dict): Model evaluation metrics\n", "        ranges (dict): Min-max ranges for each metric\n", "        weights (dict): Weight for each metric in final score\n", "\n", "    Returns:\n", "        float: Composite score\n", "    \"\"\"\n", "    # Normalize metrics\n", "    r2_norm = normalize_metric_channel(metrics['R2'], *ranges['R2'], higher_is_better=True)\n", "    p_norm = normalize_metric_channel(metrics['avg_p_value'], *ranges['p_value'], higher_is_better=False)\n", "    aic_norm = normalize_metric_channel(metrics['AIC'], *ranges['AIC'], higher_is_better=False)\n", "\n", "    # if 'incremental_impact' in metrics:\n", "    #     # Lower incremental impact is better (less change to existing coefficients)\n", "    #     incremental_norm = normalize_metric_channel(metrics['incremental_impact'],\n", "    #                                      *ranges['incremental'],\n", "    #                                      higher_is_better=False)\n", "    # else:\n", "    #     incremental_norm = 0.5  # Default if not available\n", "\n", "    # Calculate benchmark score\n", "    if 'avg_benchmark_deviation' in metrics:\n", "        benchmark_score = max(0, (metrics['avg_benchmark_deviation'] / ranges['benchmark_deviation'][1]))\n", "    else:\n", "        benchmark_score = 0.5  # Default if not available\n", "\n", "    # Calculate composite score\n", "    composite_score = (\n", "        weights['R2'] * r2_norm +\n", "        weights['p_value'] * p_norm +\n", "        weights['AIC'] * aic_norm +\n", "        # weights['incremental'] * incremental_norm +\n", "        weights['benchmark'] * benchmark_score\n", "    )\n", "\n", "    return composite_score\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "ca4f9f22", "metadata": {}, "source": ["# Optimal Channel Selection"]}, {"cell_type": "code", "execution_count": 10, "id": "ce9230ec", "metadata": {}, "outputs": [], "source": ["def select_optimal_channels(df, transformed_channels_by_promo, target_column, control_variables=None,\n", "                           benchmark_values=None):\n", "    import numpy as np\n", "    import pandas as pd\n", "    import copy\n", "\n", "    print(\"\\nSelecting optimal channel combination using one-variable-at-a-time optimization...\")\n", "\n", "    # Initialize control variables if not provided\n", "    if control_variables is None:\n", "        control_variables = []\n", "\n", "    # Define ranges for normalization\n", "    ranges = {\n", "        'R2': (0.5, 0.95),  # Range for R²\n", "        'p_value': (0.01, 0.1),  # Range for p-values\n", "        'AIC': (df[target_column].min(), df[target_column].max() * 2),  # Range for AIC\n", "        'benchmark_deviation': (0, 0.5)  # Range for benchmark deviation\n", "    }\n", "\n", "    # Define weights for composite score\n", "    weights = {\n", "        'R2': 0.20,        # 20% weight on R²\n", "        'p_value': 0.20,   # 20% weight on p-value\n", "        'AIC': 0.10,       # 10% weight on AIC\n", "        'benchmark': 0.50  # 50% weight on business benchmark\n", "    }\n", "\n", "    # Results storage for all tested combinations\n", "    all_results = []\n", "\n", "    # Start with baseline model (only control variables)\n", "    # base_features = control_variables.copy() + [f\"{target_column}_lag_1\"]\n", "    base_features = [f\"{target_column}_lag_1\"]\n", "\n", "    # Get promo channels\n", "    promo_channels = list(transformed_channels_by_promo.keys())\n", "    print(f\"Evaluating {len(promo_channels)} promotional channels using one-variable-at-a-time optimization...\")\n", "\n", "    # Initialize with first transformation for each channel\n", "    current_best_transforms = {}\n", "    for channel in promo_channels:\n", "        if transformed_channels_by_promo[channel]:  # Ensure there's at least one transformation\n", "            current_best_transforms[channel] = transformed_channels_by_promo[channel][0]\n", "\n", "    # One variable at a time optimization\n", "    for channel_idx, current_channel in enumerate(promo_channels):\n", "        print(f\"\\nTuning channel {channel_idx+1}/{len(promo_channels)}: {current_channel}\")\n", "\n", "        # Get transformations for this channel\n", "        channel_transforms = transformed_channels_by_promo[current_channel]\n", "        print(f\"Testing {len(channel_transforms)} transformations for {current_channel}...\")\n", "\n", "        # Track best for this channel\n", "        channel_best_score = -np.inf\n", "        channel_best_transform = None\n", "        channel_best_metrics = None\n", "\n", "        # Try each transformation for this channel\n", "        for transform_idx, transform in enumerate(channel_transforms):\n", "            # Build feature set with current transformation for this channel and best for others\n", "            feature_set = base_features.copy()\n", "            for other_channel, best_transform in current_best_transforms.items():\n", "                if other_channel == current_channel:\n", "                    feature_set.append(transform)  # Use current transformation being tested\n", "                else:\n", "                    feature_set.append(best_transform)  # Use best transformation for other channels\n", "\n", "            print(f\"  Testing transformation {transform_idx+1}/{len(channel_transforms)}: {transform}\")\n", "\n", "            # Evaluate this feature set\n", "            print(f\"Feature set{feature_set}\")\n", "            metrics = evaluate_model(df, feature_set, target_column, benchmark_values)\n", "\n", "            if not metrics:\n", "                print(f\"    Skipping invalid combination with {transform}\")\n", "                continue\n", "\n", "            # Calculate composite score\n", "            composite_score = calculate_composite_score(metrics, ranges, weights)\n", "            metrics['composite_score'] = composite_score\n", "\n", "            # Store result\n", "            result = {\n", "                'channel': current_channel,\n", "                'transformation': transform,\n", "                'features': feature_set.copy(),  # Store actual feature list instead of string\n", "                'features_str': str(feature_set),  # Keep string version for display\n", "                'R2': metrics['R2'],\n", "                'AIC': metrics['AIC'],\n", "                'avg_p_value': metrics['avg_p_value'],\n", "                'avg_benchmark_deviation': metrics.get('avg_benchmark_deviation', 0),\n", "                'composite_score': composite_score\n", "            }\n", "            all_results.append(result)\n", "\n", "            # Check if this is best for current channel\n", "            if composite_score > channel_best_score:\n", "                channel_best_score = composite_score\n", "                channel_best_transform = transform\n", "                channel_best_metrics = metrics.copy()\n", "                print(f\"    New best for channel {current_channel}! Score: {composite_score:.4f}\")\n", "                print(f\"    R²: {metrics['R2']:.4f}, AIC: {metrics['AIC']:.2f}\")\n", "\n", "        # Update the best transformation for this channel\n", "        if channel_best_transform:\n", "            print(f\"\\nBest transformation for {current_channel}: {channel_best_transform}\")\n", "            print(f\"Score: {channel_best_score:.4f}\")\n", "\n", "            # Update current best transformation for this channel\n", "            current_best_transforms[current_channel] = channel_best_transform\n", "        else:\n", "            print(f\"Warning: No valid transformation found for channel {current_channel}\")\n", "\n", "    # Convert final best transformations to feature list\n", "    best_features = base_features.copy()\n", "    for channel, transform in current_best_transforms.items():\n", "        best_features.append(transform)\n", "\n", "    # Evaluate final model with all channels\n", "    final_metrics = evaluate_model(df, best_features, target_column, benchmark_values)\n", "    if not final_metrics:\n", "        print(\"Warning: Final model with all channels could not be evaluated\")\n", "        # Fall back to the last valid metrics\n", "        for channel in reversed(promo_channels):\n", "            if channel in current_best_transforms:\n", "                # Remove the last added channel transform\n", "                partial_features = [f for f in best_features if f != current_best_transforms[channel]]\n", "                partial_metrics = evaluate_model(df, partial_features, target_column, benchmark_values)\n", "                if partial_metrics:\n", "                    final_metrics = partial_metrics\n", "                    print(f\"Using metrics from model without {channel}\")\n", "                    best_features = partial_features  # Update best_features to the valid set\n", "                    break\n", "\n", "    # Convert results to DataFrame for easier analysis\n", "    results_df = pd.DataFrame(all_results)\n", "\n", "    # Get top 5 alternative models\n", "    top_5_models = []\n", "    if not results_df.empty:\n", "        # Create a new column with a unique feature set identifier for grouping\n", "        results_df['feature_set_key'] = results_df['features_str'].apply(lambda x: str(sorted(eval(x))))\n", "\n", "        # Group by unique feature sets and get the highest score for each\n", "        grouped_results = results_df.sort_values('composite_score', ascending=False)\n", "        grouped_results = grouped_results.drop_duplicates(subset=['feature_set_key'])\n", "\n", "        # Sort by composite score\n", "        top_models = grouped_results.head(6)  # Get top 6 to include the best one\n", "\n", "        # Compare each model feature set with the best one\n", "        best_model_key = str(sorted(best_features))\n", "\n", "        # Get top 5 models that are different from the best one\n", "        count = 0\n", "        for _, row in top_models.iterrows():\n", "            model_features = row['features']  # Get the actual feature list\n", "\n", "            # Skip if this is the same as the best model\n", "            if str(sorted(model_features)) == best_model_key and count < 5:\n", "                continue\n", "\n", "            if count < 5:\n", "                top_5_models.append({\n", "                    'channel': row['channel'],\n", "                    'transformation': row['transformation'],\n", "                    'features': model_features,  # Store actual feature list\n", "                    'features_str': row['features_str'],  # Keep string version for display\n", "                    'R2': row['R2'],\n", "                    'AIC': row['AIC'],\n", "                    'avg_p_value': row['avg_p_value'],\n", "                    'composite_score': row['composite_score']\n", "                })\n", "                count += 1\n", "\n", "        # Save all results to CSV\n", "        # Convert feature lists back to strings for CSV export\n", "        export_results_df = results_df.copy()\n", "        export_results_df['features'] = export_results_df['features_str']\n", "        export_results_df = export_results_df.drop(['features_str', 'feature_set_key'], axis=1)\n", "\n", "        csv_filename = \"optimal_channel_selection_results.csv\"\n", "        export_results_df.to_csv(csv_filename, index=False)\n", "        print(f\"Saved detailed results to {csv_filename}\")\n", "\n", "        # Save top 5 alternative models to a separate CSV\n", "        if top_5_models:\n", "            # Prepare top 5 models for export\n", "            top5_export = [{\n", "                'channel': model['channel'],\n", "                'transformation': model['transformation'],\n", "                'features': model['features_str'],\n", "                'R2': model['R2'],\n", "                'AIC': model['AIC'],\n", "                'avg_p_value': model['avg_p_value'],\n", "                'composite_score': model['composite_score']\n", "            } for model in top_5_models]\n", "\n", "            top5_df = pd.DataFrame(top5_export)\n", "            top5_csv_filename = \"top5_alternative_models.csv\"\n", "            top5_df.to_csv(top5_csv_filename, index=False)\n", "            print(f\"Saved top 5 alternative models to {top5_csv_filename}\")\n", "\n", "    # Calculate composite score for final best features\n", "    best_score = -np.inf\n", "    if final_metrics:\n", "        best_score = calculate_composite_score(final_metrics, ranges, weights)\n", "\n", "    # Print best model details\n", "    if final_metrics:\n", "        promo_only = [f for f in best_features if f not in base_features]\n", "        print(\"\\nFinal optimal channel combination:\")\n", "        print(f\"- Promotional channels: {', '.join(promo_only)}\")\n", "        print(f\"- R²: {final_metrics['R2']:.4f}\")\n", "        print(f\"- AIC: {final_metrics['AIC']:.2f}\")\n", "        print(f\"- Avg. p-value: {final_metrics['avg_p_value']:.4f}\")\n", "        print(f\"- Composite score: {best_score:.4f}\")\n", "\n", "    # Print top 5 alternative models\n", "    if top_5_models:\n", "        print(\"\\nTop 5 alternative models:\")\n", "        for i, model in enumerate(top_5_models):\n", "            print(f\"{i+1}. Score: {model['composite_score']:.4f}, R²: {model['R2']:.4f}\")\n", "            print(f\"   Channel: {model['channel']}, Transform: {model['transformation']}\")\n", "            promo_only = [f for f in model['features'] if f not in base_features]\n", "            print(f\"   Promotional channels: {', '.join(promo_only)}\")\n", "\n", "    # Extract feature sets for all 6 models (best + top 5 alternatives)\n", "    all_model_features = [best_features]\n", "    all_model_metrics = [final_metrics]\n", "\n", "    for model in top_5_models:\n", "        all_model_features.append(model['features'])\n", "\n", "        # Get metrics for this feature set\n", "        model_metrics = evaluate_model(df, model['features'], target_column, benchmark_values)\n", "        all_model_metrics.append(model_metrics)\n", "\n", "    return best_features, final_metrics, all_model_features, all_model_metrics,top_5_models\n"]}, {"cell_type": "code", "execution_count": 11, "id": "56002db6", "metadata": {}, "outputs": [], "source": ["def fix_negative_estimates(df, best_features, target_column, control_variables, date_column, transformed_channels_by_promo, all_transformed_features, id_column,start_date,end_date):\n", "    \"\"\"\n", "    Fix negative estimates in the best_features model by trying different adstock and transformation combinations.\n", "    If a channel cannot be fixed, it will be removed from the model.\n", "\n", "    Returns:\n", "        Tuple of (new_best_features, fixed_channels_info)\n", "    \"\"\"\n", "\n", "    print(\"\\nChecking for negative estimates in best model...\")\n", "\n", "    # First run the regression with current best features to identify negative estimates\n", "    result_table = perform_regression(df, best_features, target_column, date_column,start_date,end_date)\n", "\n", "    if not isinstance(result_table, pd.DataFrame) or result_table.empty:\n", "        print(\"Error running initial regression. Cannot fix negative estimates.\")\n", "        return best_features, {}\n", "\n", "    # Check if we have the expected format from perform_regression\n", "    if 'Channel' in result_table.columns and 'Estimate' in result_table.columns:\n", "        var_column = 'Channel'\n", "        est_column = 'Estimate'\n", "    else:\n", "        print(\"Warning: Unexpected regression result table format.\")\n", "        print(\"Available columns:\", result_table.columns.tolist())\n", "        print(\"Cannot proceed with fixing negative estimates.\")\n", "        return best_features, {}\n", "\n", "    # Identify promotional channels with negative estimates\n", "    negative_channels = []\n", "    for _, row in result_table.iterrows():\n", "        var_name = row[var_column]\n", "\n", "        # Skip the intercept and any non-promotion variables\n", "        if var_name == 'Intercept' or var_name not in all_transformed_features:\n", "            continue\n", "\n", "        est_value = row[est_column]\n", "\n", "        if est_value < 0:\n", "            # Extract the base channel name from the transformed variable\n", "            base_channel = None\n", "            for promo_col, transformed_cols in transformed_channels_by_promo.items():\n", "                if var_name in transformed_cols:\n", "                    base_channel = promo_col\n", "                    break\n", "\n", "            if base_channel:\n", "                negative_channels.append((base_channel, var_name, est_value))\n", "\n", "    if not negative_channels:\n", "        print(\"No negative estimates found in promotional channels. Model is optimal.\")\n", "        return best_features, {}\n", "\n", "    print(f\"Found {len(negative_channels)} promotional channels with negative estimates:\")\n", "    for base_channel, variable, estimate in negative_channels:\n", "        print(f\"- {base_channel} (via {variable}): {estimate:.4f}\")\n", "\n", "    # Dictionary to track fixed channels and their new transformations\n", "    fixed_channels = {}\n", "    channels_to_remove = []\n", "    \n", "    # Dictionary to store only the final best transformations that we'll add to df\n", "    best_transformations = {}\n", "\n", "    # For each negative channel, try all adstock and transformation combinations\n", "    for base_channel, current_variable, _ in negative_channels:\n", "        print(f\"\\nAttempting to fix negative estimate for {base_channel}...\")\n", "\n", "        # Get adstock ranges for this channel\n", "        adstock_ranges = get_adstock_ranges_from_gemini([base_channel])\n", "        channel_range = adstock_ranges.get(base_channel, {\n", "            \"type\": \"Other\",\n", "            \"min_adstock\": 10,\n", "            \"max_adstock\": 90\n", "        })\n", "\n", "        min_adstock = channel_range[\"min_adstock\"]\n", "        max_adstock = channel_range[\"max_adstock\"]\n", "\n", "        # Define the adstock values to test\n", "        adstock_values = list(range(min_adstock, max_adstock + 1, 10))\n", "\n", "        # Define transformations to test\n", "        transformations = get_transformation_functions()\n", "        best_estimate = 0\n", "        best_variable = None\n", "        best_transformed_series = None\n", "\n", "        # Test each combination\n", "        for adstock_rate in adstock_values:\n", "            # Apply adstock transformation to a temporary series, not the original df\n", "            adstock_col, adstocked_series = apply_adstock(df, base_channel, id_column, adstock_rate)\n", "            \n", "            for transform_name, transform_func in transformations.items():\n", "                # Apply transformation to the temporary series\n", "                transformed_col, transformed_series = apply_transformation(\n", "                    adstocked_series, transform_name, transform_func, adstock_col\n", "                )\n", "                \n", "                # Create a temporary DataFrame for this test\n", "                temp_df = df.copy()\n", "                temp_df[transformed_col] = transformed_series\n", "\n", "                # Create a temporary feature set replacing the current transformation\n", "                temp_features = best_features.copy()\n", "\n", "                # Remove the current transformation of this channel from the features\n", "                for chan_transform in transformed_channels_by_promo.get(base_channel, []):\n", "                    if chan_transform in temp_features:\n", "                        temp_features.remove(chan_transform)\n", "\n", "                # Add the new transformation\n", "                temp_features.append(transformed_col)\n", "\n", "                # Run regression with this combination\n", "                temp_result = perform_regression(temp_df, temp_features, target_column, date_column,start_date,end_date)\n", "\n", "                if isinstance(temp_result, pd.DataFrame) and not temp_result.empty:\n", "                    # Find the estimate for this new transformed variable\n", "                    for _, row in temp_result.iterrows():\n", "                        if var_column in row and row[var_column] == transformed_col:\n", "                            if est_column in row:\n", "                                estimate = row[est_column]\n", "                                if estimate > best_estimate:\n", "                                    best_estimate = estimate\n", "                                    best_variable = transformed_col\n", "                                    best_transformed_series = transformed_series\n", "\n", "                # Clean up the temp_df to free memory\n", "                del temp_df\n", "\n", "        # After testing all combinations, check if we found a positive estimate\n", "        if best_estimate > 0:\n", "            print(f\"Fixed negative estimate for {base_channel}. New variable: {best_variable}, New estimate: {best_estimate:.4f}\")\n", "            fixed_channels[base_channel] = {\n", "                'old_variable': current_variable,\n", "                'new_variable': best_variable,\n", "                'new_estimate': best_estimate\n", "            }\n", "            # Store the best transformation to add to df later\n", "            best_transformations[best_variable] = best_transformed_series\n", "        else:\n", "            print(f\"Could not find positive estimate for {base_channel}. Will remove from model.\")\n", "            channels_to_remove.append(base_channel)\n", "\n", "    # Update the best_features list with the fixed transformations\n", "    new_best_features = best_features.copy()\n", "\n", "    # First, remove ALL transformed variables for channels that need to be replaced or removed\n", "    channels_to_process = list(fixed_channels.keys()) + channels_to_remove\n", "    for base_channel in channels_to_process:\n", "        # Remove all transformations of this channel from the feature list\n", "        for transformed_col in transformed_channels_by_promo.get(base_channel, []):\n", "            if transformed_col in new_best_features:\n", "                new_best_features.remove(transformed_col)\n", "\n", "    # Add back new variables for channels that were successfully fixed\n", "    for base_channel, info in fixed_channels.items():\n", "        # Add the new variable\n", "        if info['new_variable'] not in new_best_features:\n", "            new_best_features.append(info['new_variable'])\n", "            \n", "        # Now add the best transformation to the original df\n", "        if info['new_variable'] in best_transformations:\n", "            df[info['new_variable']] = best_transformations[info['new_variable']]\n", "\n", "    # Run a final check to ensure no negative estimates remain\n", "    final_check_result = perform_regression(df, new_best_features, target_column, date_column,start_date,end_date)\n", "    if isinstance(final_check_result, pd.DataFrame) and not final_check_result.empty:\n", "        still_negative = []\n", "\n", "        for _, row in final_check_result.iterrows():\n", "            if (var_column in row and row[var_column] in all_transformed_features and\n", "                est_column in row and row[est_column] < 0):\n", "                still_negative.append(row[var_column])\n", "\n", "        if still_negative:\n", "            print(f\"Warning: {len(still_negative)} promotional channels still have negative estimates:\")\n", "            for var in still_negative:\n", "                print(f\"- {var}\")\n", "            print(\"Consider manual inspection of these variables.\")\n", "\n", "    print(f\"\\nOriginal model had {len(best_features)} features.\")\n", "    print(f\"New model has {len(new_best_features)} features.\")\n", "    print(f\"Fixed {len(fixed_channels)} channels with negative estimates.\")\n", "    print(f\"Removed {len(channels_to_remove)} channels that couldn't be fixed.\")\n", "\n", "    return new_best_features, fixed_channels"]}, {"cell_type": "code", "execution_count": 12, "id": "fc34cd43", "metadata": {}, "outputs": [], "source": ["def run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics):\n", "    \"\"\"Run regression analyses for top models and save results without Excel export\"\"\"\n", "    global all_regression_results, best_result_table\n", "    print(f\"best fetaures in run_regression_analyses{best_features}\")\n", "    print(f\"all_feature_sets in run_regression_analyses{all_feature_sets}\")\n", "    all_regression_results = []\n", "    \n", "    print(\"\\nRunning regression analysis for top 6 models...\")\n", "    for i, feature_set in enumerate(all_feature_sets[:6]):  # Limit to top 6 models\n", "        model_name = \"Best Model\" if i == 0 else f\"Alternative Model {i}\"\n", "        print(f\"\\nRunning regression for {model_name}...\")\n", "\n", "        # Extract only promotional channels for display\n", "        promo_features = [f for f in feature_set if f in all_transformed_features]\n", "        promo_features_str = ', '.join(promo_features)\n", "        print(f\"Promotional channels: {promo_features_str}\")\n", "\n", "        # Perform regression\n", "        result_table = perform_regression(df, feature_set, target_column, date_column, start_date, end_date)\n", "\n", "        # Add model identifier\n", "        if isinstance(result_table, pd.DataFrame) and not result_table.empty:\n", "            try:\n", "                # Add model columns if there's at least one row\n", "                if len(result_table) > 0:\n", "                    result_table['model'] = model_name\n", "                    result_table['model_rank'] = i + 1\n", "\n", "                all_regression_results.append(result_table)\n", "\n", "                # Get metrics for this model\n", "                metrics = all_metrics[i] if i < len(all_metrics) else None\n", "                if metrics:\n", "                    r2 = metrics['R2']\n", "                    aic = metrics['AIC']\n", "                    avg_p_value = metrics['avg_p_value']\n", "                    print(f\"R²: {r2:.4f}, AIC: {aic:.2f}\")\n", "                else:\n", "                    print(\"Metrics not available for this model\")\n", "            except Exception as e:\n", "                print(f\"Error processing regression results: {e}\")\n", "        else:\n", "            print(f\"Error running regression for {model_name}\")\n", "\n", "    # Set the best result table\n", "    best_result_table = all_regression_results[0] if all_regression_results else None\n", "\n", "    return all_regression_results, best_result_table\n", "\n", "def display_best_model_results():\n", "    \"\"\"Display the results of the best model\"\"\"\n", "    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:\n", "        print(\"\\nBest Model Regression Results:\")\n", "        print(\"=\"*80)\n", "        try:\n", "            print(best_result_table.to_string(index=False))\n", "        except Exception as e:\n", "            print(f\"Error displaying results table: {e}\")\n", "            print(\"Results available in Excel file.\")\n", "        print(\"=\"*80)\n", "        return True\n", "    return False"]}, {"cell_type": "markdown", "id": "89f329bd", "metadata": {}, "source": ["# Update database after regression"]}, {"cell_type": "code", "execution_count": 13, "id": "bdf9863c", "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import json\n", "from psycopg2.extras import <PERSON>son\n", "import pandas as pd\n", "import datetime\n", "import logging\n", "\n", "def update_model_regression(conn, model_run_id, all_transformed_features, best_result_table, \n", "                           transformed_channels_by_promo, adstocked_channels_by_promo, \n", "                           adstock_range_channel, parms, all_regression_results, best_features, all_feature_sets):\n", "    \"\"\"\n", "    Update the JSONB fields in the model_data table for a specific model_run_id.\n", "    \n", "    Parameters:\n", "    conn: PostgreSQL connection object\n", "    model_run_id: UUID of the model run to update\n", "    all_transformed_features: List of feature names\n", "    best_result_table: DataFrame (will be converted to JSON)\n", "    transformed_channels_by_promo: Dict\n", "    adstocked_channels_by_promo: Dict\n", "    adstock_range_channel: Dict\n", "    parms: Dict (contains date column)\n", "    all_regression_results: List of DataFrames\n", "    best_features: List of best features\n", "    all_feature_sets: List of all feature sets\n", "    \"\"\"\n", "    if not conn or conn.closed:\n", "        logging.error(\"Invalid database connection\")\n", "        return False\n", "    \n", "    # Custom JSON encoder to handle datetime objects\n", "    class DateTimeEncoder(json.JSONEncoder):\n", "        def default(self, obj):\n", "            if isinstance(obj, (datetime.datetime, datetime.date)):\n", "                return obj.isoformat()\n", "            return super().default(obj)\n", "    \n", "    # Helper function to safely convert any pandas DataFrames to serializable dictionaries\n", "    def convert_pandas_to_dict(obj):\n", "        if isinstance(obj, pd.DataFrame):\n", "            # Convert DataFrame to dict with records orientation and convert datetime columns to ISO format\n", "            for col in obj.select_dtypes(include=['datetime64']).columns:\n", "                obj[col] = obj[col].astype(str)\n", "            return json.loads(obj.to_json(orient='records', date_format='iso'))\n", "        elif isinstance(obj, pd.Series):\n", "            # Handle datetime Series\n", "            if pd.api.types.is_datetime64_any_dtype(obj):\n", "                obj = obj.astype(str)\n", "            return json.loads(obj.to_json())\n", "        elif isinstance(obj, dict):\n", "            # Handle dictionaries - recursively process all values\n", "            return {k: convert_pandas_to_dict(v) for k, v in obj.items()}\n", "        elif isinstance(obj, list):\n", "            # Handle lists - recursively process all items\n", "            return [convert_pandas_to_dict(item) for item in obj]\n", "        elif isinstance(obj, (datetime.datetime, datetime.date)):\n", "            # Convert datetime objects to ISO format string\n", "            return obj.isoformat()\n", "        else:\n", "            return obj\n", "    \n", "    try:\n", "        # Convert each parameter to a serializable format\n", "        serialized_all_transformed_features = convert_pandas_to_dict(all_transformed_features)\n", "        serialized_best_result_table = convert_pandas_to_dict(best_result_table)\n", "        serialized_transformed_channels = convert_pandas_to_dict(transformed_channels_by_promo)\n", "        serialized_adstocked_channels = convert_pandas_to_dict(adstocked_channels_by_promo)\n", "        serialized_adstock_range = convert_pandas_to_dict(adstock_range_channel)\n", "        serialized_parms = convert_pandas_to_dict(parms)\n", "        serialized_regression_results = convert_pandas_to_dict(all_regression_results)\n", "        serialized_best_features = convert_pandas_to_dict(best_features)\n", "        serialized_all_feature_sets = convert_pandas_to_dict(all_feature_sets)\n", "        \n", "        # Create a cursor\n", "        cursor = conn.cursor()\n", "        \n", "        # SQL query for update - now including model_status\n", "        update_query = \"\"\"\n", "        UPDATE model_data\n", "        SET \n", "            all_transformed_features = %s,\n", "            best_result_table = %s,\n", "            transformed_channels_by_promo = %s,\n", "            adstocked_channels_by_promo = %s,\n", "            adstock_range_channel = %s,\n", "            parms = %s,\n", "            model_results = %s,\n", "            best_features = %s,\n", "            all_features = %s,\n", "            model_status = %s,\n", "            updated_at = CURRENT_TIMESTAMP\n", "        WHERE model_run_id = %s\n", "        \"\"\"\n", "        \n", "        # Execute the query with parameters - now including model_status\n", "        cursor.execute(update_query, (\n", "            <PERSON>son(serialized_all_transformed_features),\n", "            <PERSON>son(serialized_best_result_table),\n", "            Json(serialized_transformed_channels),\n", "            Json(serialized_adstocked_channels),\n", "            Json(serialized_adstock_range),\n", "            <PERSON><PERSON>(serialized_parms),\n", "            <PERSON><PERSON>(serialized_regression_results),\n", "            <PERSON>son(serialized_best_features),\n", "            <PERSON><PERSON>(serialized_all_feature_sets),\n", "            \"complete\",  # Set model_status to \"complete\"\n", "            model_run_id\n", "        ))\n", "        \n", "        # Commit the transaction\n", "        conn.commit()\n", "        \n", "        print(f\"Successfully updated model run data for model_run_id: {model_run_id}, status set to complete\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error updating database: {e}\")\n", "        conn.rollback()\n", "        return False\n", "    finally:\n", "        # Close the cursor if it exists\n", "        if 'cursor' in locals() and cursor is not None:\n", "            cursor.close()"]}, {"cell_type": "code", "execution_count": 14, "id": "3271a475", "metadata": {}, "outputs": [], "source": ["\n", "# adstock_range_channel={}\n", "# def get_channel_stats(best_result_table):\n", "#     \"\"\"\n", "#     Returns the count of channels (excluding 'Intercept'), the R² value, \n", "#     the Modelled Sales value, and a list of significant channels (p-value < 0.05)\n", "#     from best_result_table.\n", "    \n", "#     Parameters:\n", "#     best_result_table (DataFrame): A DataFrame containing columns 'Channel', \n", "#                                   'P-Value', 'Adjusted R²', and 'Modeled Sales'\n", "    \n", "#     Returns:\n", "#     tuple: (channel_count, r_squared, modelled_sales, significant_channels)\n", "#     \"\"\"\n", "#     # Count the number of channels excluding 'Intercept'\n", "#     channel_count = [best_result_table[best_result_table['Channel'] != 'Intercept']]\n", "    \n", "#     # Get the R² value (which is the same throughout)\n", "#     r_squared = best_result_table['Adjusted R²'].iloc[0]\n", "    \n", "#     # Get the Modelled Sales value (which is the same throughout)\n", "#     modelled_sales = best_result_table['Modeled Sales'].iloc[0]\n", "    \n", "#     # Get list of channels with p-value < 0.05 (excluding Intercept)\n", "#     significant_channels = best_result_table[\n", "#         (best_result_table['P-Value'] < 0.05) & \n", "#         (best_result_table['Channel'] != 'Intercept')\n", "#     ]['Channel'].tolist()\n", "    \n", "#     return channel_count, r_squared, modelled_sales, significant_channels\n", "\n", "def get_channel_stats(best_result_table, transformed_channel_by_promo=None):\n", "    \"\"\"\n", "    Returns the names of channels (excluding 'Intercept'), the R² value, \n", "    the Modelled Sales value, and a list of significant channels (p-value < 0.05)\n", "    from best_result_table, with the significant channels converted back to their base names.\n", "    \n", "    Parameters:\n", "    best_result_table (DataFrame): A DataFrame containing columns 'Channel', \n", "                                  'P-Value', 'Adjusted R²', and 'Modeled Sales'\n", "    transformed_channel_by_promo (dict): Dictionary mapping base channel names to their \n", "                                         transformed versions\n", "    \n", "    Returns:\n", "    tuple: (channel_names, r_squared, modelled_sales, significant_channels)\n", "    \"\"\"\n", "    # Get the transformed names of channels excluding 'Intercept'\n", "    transformed_channel_names = best_result_table[best_result_table['Channel'] != 'Intercept']['Channel'].tolist()\n", "    \n", "    # Create a mapping from transformed names to base names\n", "    transformed_to_base = {}\n", "    if transformed_channel_by_promo:\n", "        for base_name, transformed_list in transformed_channel_by_promo.items():\n", "            for transformed_name in transformed_list:\n", "                transformed_to_base[transformed_name] = base_name\n", "    \n", "    # Convert transformed channel names to base names\n", "    channel_names = []\n", "    for channel in transformed_channel_names:\n", "        if channel in transformed_to_base:\n", "            # Use the mapping if available\n", "            base_name = transformed_to_base[channel]\n", "        else:\n", "            # Extract base name by removing transformation segments\n", "            base_parts = []\n", "            for part in channel.split('_'):\n", "                if part not in ['<PERSON><PERSON>', 'Root', 'Log'] and not part.isdigit():\n", "                    base_parts.append(part)\n", "            base_name = '_'.join(base_parts)\n", "        channel_names.append(base_name)\n", "    \n", "    # Get the R² value (which is the same throughout)\n", "    r_squared = best_result_table['Adjusted R²'].iloc[0]\n", "    \n", "    # Get the Modelled Sales value (which is the same throughout)\n", "    modelled_sales = best_result_table['Modeled Sales'].iloc[0]\n", "    \n", "    # Get list of channels with p-value < 0.05 (excluding Intercept)\n", "    significant_transformed_channels = best_result_table[\n", "        (best_result_table['P-Value'] < 0.05) & \n", "        (best_result_table['Channel'] != 'Intercept')\n", "    ]['Channel'].tolist()\n", "    \n", "    # Convert transformed channel names back to base names for significant channels\n", "    significant_channels = []\n", "    for channel in significant_transformed_channels:\n", "        if channel in transformed_to_base:\n", "            # Use the mapping if available\n", "            base_name = transformed_to_base[channel]\n", "        else:\n", "            # Extract base name by removing transformation segments\n", "            base_parts = []\n", "            for part in channel.split('_'):\n", "                if part not in ['<PERSON><PERSON>', 'Root', 'Log'] and not part.isdigit():\n", "                    base_parts.append(part)\n", "            base_name = '_'.join(base_parts)\n", "        significant_channels.append(base_name)\n", "    \n", "    return channel_names, r_squared, modelled_sales, significant_channels"]}, {"cell_type": "markdown", "id": "d2237263", "metadata": {}, "source": ["# Main pipeline"]}, {"cell_type": "code", "execution_count": 15, "id": "f4f322f9", "metadata": {}, "outputs": [], "source": ["def add_roi_to_result_tables(\n", "    result_table,\n", "    promo_channels, \n", "    all_transformed_features, \n", "    spends_df, \n", "    spend_column, \n", "    spend_channel_column,\n", "    target_column,\n", "    unit_price  # Default unit price if not provided\n", "):\n", "    \"\"\"\n", "    Add ROI as a column to regression result tables.\n", "    \n", "    Args:\n", "        all_regression_results: List of DataFrames containing regression results\n", "        promo_channels: List of promotional channel names (list of strings)\n", "        all_transformed_features: List of all transformed feature names (list of strings)\n", "        spends_df: DataFrame containing channel names and their corresponding spend amounts\n", "        spend_column: Spends column name of spends_df\n", "        spend_channel_column: Channel name of spends_df\n", "        target_column: Name of the target variable (string)\n", "        unit_price: Price per unit of the target variable (default=1)\n", "        \n", "    Returns:\n", "        List of DataFrames: Updated regression result tables with ROI column\n", "    \"\"\"\n", "    import pandas as pd\n", "    import numpy as np\n", "    \n", "    # Create a mapping from transformed channel names to base channel names\n", "    transformed_to_base = {}\n", "    for base_channel in promo_channels:\n", "        for transformed_channel in all_transformed_features:\n", "            # Check if the transformed channel starts with the base channel name\n", "            if transformed_channel.startswith(base_channel + \"_\"):\n", "                transformed_to_base[transformed_channel] = base_channel\n", "        # Also map the base channel to itself\n", "        transformed_to_base[base_channel] = base_channel\n", "    \n", "    # Process each result table\n", "    updated_results = []\n", "\n", "    if result_table is None or result_table.empty:\n", "            updated_results.append(result_table)\n", "         \n", "            \n", "        # Make a copy to avoid modifying the original\n", "    result_df = result_table.copy()\n", "        \n", "        # Add ROI column\n", "    result_df['ROI'] = np.nan\n", "        \n", "        # Process each row in the result table\n", "    for idx, row in result_df.iterrows():\n", "            channel_name = row['Channel']\n", "            \n", "            # Skip Intercept and target lag columns\n", "            if channel_name == 'Intercept' or channel_name.startswith(f'Lag({target_column}'):\n", "                continue\n", "                \n", "            # Find the base channel for this transformed channel\n", "            base_channel = None\n", "            \n", "            # First check direct mapping\n", "            if channel_name in transformed_to_base:\n", "                base_channel = transformed_to_base[channel_name]\n", "            else:\n", "                # Try to find by prefix\n", "                for promo in promo_channels:\n", "                    if channel_name.startswith(promo + \"_\") or channel_name == promo:\n", "                        base_channel = promo\n", "                        break\n", "                        \n", "                # If still not found, try more flexible matching\n", "                if base_channel is None:\n", "                    for promo in promo_channels:\n", "                        if promo in channel_name:\n", "                            base_channel = promo\n", "                            break\n", "            \n", "            if base_channel:\n", "                # Get the spend for this channel\n", "                try:\n", "                    channel_spend = spends_df[spends_df[spend_channel_column] == base_channel][spend_column].values[0]\n", "                except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>):\n", "                    print(f\"Warning: No spend data found for channel '{base_channel}'\")\n", "                    channel_spend = 0\n", "                \n", "                # Calculate impact (use the Impact Percentage value directly)\n", "                impact = row['Impact Percentage'] * row['Modeled Sales'] / 100\n", "                \n", "                # Calculate ROI\n", "                if channel_spend > 0:\n", "                    roi = (impact * unit_price) / channel_spend\n", "                    result_df.at[idx, 'ROI'] = roi\n", "                else:\n", "                    result_df.at[idx, 'ROI'] = np.nan\n", "        \n", "        \n", "    \n", "    return result_df\n", "\n", "def run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics,promo_channels,spends_df,spend_column,spend_channel_column,unit_price=1):\n", "    \"\"\"Run regression analyses for top models and save results with ROI calculation\"\"\"\n", "    global all_regression_results, best_result_table\n", "    print(f\"best features in run_regression_analyses{best_features}\")\n", "    print(f\"all_feature_sets in run_regression_analyses{all_feature_sets}\")\n", "    all_regression_results = []\n", "    \n", "    print(\"\\nRunning regression analysis for top 6 models...\")\n", "    for i, feature_set in enumerate(all_feature_sets[:6]):  # Limit to top 6 models\n", "        model_name = \"Best Model\" if i == 0 else f\"Alternative Model {i}\"\n", "        print(f\"\\nRunning regression for {model_name}...\")\n", "\n", "        # Extract only promotional channels for display\n", "        promo_features = [f for f in feature_set if f in all_transformed_features]\n", "        promo_features_str = ', '.join(promo_features)\n", "        print(f\"Promotional channels: {promo_features_str}\")\n", "\n", "        # Perform regression\n", "        result_table = perform_regression(df, feature_set, target_column, date_column, start_date, end_date)\n", "\n", "        # Add model identifier\n", "        if isinstance(result_table, pd.DataFrame) and not result_table.empty:\n", "            try:\n", "                # Add model columns if there's at least one row\n", "                if len(result_table) > 0:\n", "                    result_table['model'] = model_name\n", "                    result_table['model_rank'] = i + 1\n", "\n", "                all_regression_results.append(result_table)\n", "\n", "                # Get metrics for this model\n", "                metrics = all_metrics[i] if i < len(all_metrics) else None\n", "                if metrics:\n", "                    r2 = metrics['R2']\n", "                    aic = metrics['AIC']\n", "                    avg_p_value = metrics['avg_p_value']\n", "                    print(f\"R²: {r2:.4f}, AIC: {aic:.2f}\")\n", "                else:\n", "                    print(\"Metrics not available for this model\")\n", "            except Exception as e:\n", "                print(f\"Error processing regression results: {e}\")\n", "        else:\n", "            print(f\"Error running regression for {model_name}\")\n", "\n", "    # Set the best result table\n", "    best_result_table = all_regression_results[0] if len(all_regression_results) > 0 else None\n", "    \n", "    # Add ROI column to all result tables\n", "    if len(all_regression_results) > 0:\n", "        print(\"\\nAdding ROI calculation to regression results...\")\n", "        for idx, result_table in enumerate(all_regression_results):\n", "            all_regression_results[idx] = add_roi_to_result_tables(\n", "                result_table,\n", "                promo_channels=promo_channels,\n", "                all_transformed_features=all_transformed_features,\n", "                spends_df=spends_df,\n", "                spend_column=spend_column,\n", "                spend_channel_column=spend_channel_column,\n", "                target_column=target_column,\n", "                unit_price=unit_price\n", "            )\n", "        \n", "        # Update best result table reference\n", "        best_result_table = all_regression_results[0] if len(all_regression_results) > 0 else None\n", "        \n", "        if best_result_table is not None:\n", "            # Display summary of best model with ROI\n", "            marketing_channels = best_result_table[(best_result_table['Channel'] != 'Intercept') & \n", "                                                  (~best_result_table['Channel'].str.startswith(f'Lag({target_column}'))]\n", "            if not marketing_channels.empty:\n", "                print(\"\\nBest model results with ROI:\")\n", "                print(marketing_channels[['Channel', 'Estimate', 'Impact Percentage', 'ROI']].to_string(index=False))\n", "\n", "    return all_regression_results, best_result_table"]}, {"cell_type": "code", "execution_count": 18, "id": "7547e118", "metadata": {}, "outputs": [], "source": ["def run_pipeline():\n", "    \"\"\"Main pipeline function to orchestrate the marketing mix modeling process\"\"\"\n", "    # Load input files\n", "    db_conn = initialize_db_connection()\n", "    # file_path = \"dummy1.xlsx\"\n", "    # spends_file_path = \"spends.xlsx\"\n", "    # historical_file_path = \"historical_data.xlsx\"\n", "\n", "    file_path=\"MMix_data_for_testing.csv\"\n", "    spends_file_path=\"Spends_test.xlsx\"\n", "    historical_file_path=\"Historical_test - Copy.xlsx\"\n", "\n", "    #Variables\n", "    # promo_channels = ['PDE','Copay']\n", "    # promo_channels = [col.strip() for col in promo_channels]\n", "    # date_column = 'Date'\n", "    # id_column = 'ID'\n", "    # target_column = 'NRx'\n", "    # start_date = '202411'\n", "    # end_date = '202512'\n", "    # date_format = 'MM-YYYY'\n", "    # start_date = datetime.datetime.strptime(start_date, '%Y%m')\n", "    # end_date = datetime.datetime.strptime(end_date, '%Y%m')\n", "\n", "    # spend_column = 'Spends'\n", "    # spend_channel_column = 'Channel'\n", "\n", "    # historical_impact = 'contributions%'\n", "    # historical_channel_column = 'Channel'\n", "\n", "    promo_channels = [\n", "   'Organic_Engagement', 'Staff_Engagement', 'Email_Engagement', 'Digital_Engagement',\n", "    'Lead_Engagement', 'Speaker_programs', 'Doximity_views', 'ReachMD',\n", "    'CRM_Engagement_Count', 'Event_Attendance']\n", " \n", "    promo_channels = [col.strip() for col in promo_channels]\n", "    date_column = 'Time_Period'\n", "    id_column = 'Identifier'\n", "    target_column ='Units'\n", "    start_date = '202401'\n", "    end_date = '202412'\n", "    date_format = 'YYYY-MM'\n", "    start_date = datetime.datetime.strptime(start_date, '%Y%m')\n", "    end_date = datetime.datetime.strptime(end_date, '%Y%m')\n", "\n", "    # spend_column = 'Spends'\n", "    # spend_channel_column = 'Channel'\n", "\n", "    spend_column = 'Spend ($)'\n", "    spend_channel_column = 'Channel'\n", "\n", "    # historical_impact = 'contributions%'\n", "    # historical_channel_column = 'Channel'\n", "\n", "    historical_impact = 'Impact_Percent_2023'\n", "    historical_channel_column = 'Channel'\n", "\n", "    model_run_id = \"00000000-0000-0000-0000-000000000001\"\n", "    df,unit_price=  process_main_data(file_path,promo_channels,target_column,id_column,date_column,date_format,start_date,end_date,model_run_id,db_conn)\n", "    spends_df = process_spend_data(spends_file_path,spend_column,spend_channel_column, model_run_id=None,db_conn=None)\n", "    historical_df = process_historical_data(historical_file_path,historical_impact,historical_channel_column, model_run_id=None,db_conn=None)\n", "    \n", "    \n", "\n", "    # Setup control variables\n", "    df, control_variables = add_control_variables(df, id_column, date_column)\n", "\n", "    params=setup_parameters(date_column, id_column, target_column, promo_channels, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact)\n", "    params['spends_df']=spends_df\n", "    \n", "    # Process promo channels\n", "    success,df, transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_transformed_features = process_promo_channels(df,promo_channels,target_column,id_column,date_column,start_date,end_date,control_variables)\n", "  \n", "    #Get historical benchmark\n", "    benchmark_values = get_benchmark_values(all_transformed_features, historical_df,historical_channel_column,historical_impact)\n", "    \n", "    #Filter the dataframe\n", "    df = filter_dataframe_by_date(df,date_column,start_date,end_date)\n", "    if start_date and end_date:\n", "            df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    print(df)\n", "\n", "    # Select optimal feature set\n", "    best_features, best_metrics, all_feature_sets, all_metrics,top_5_models = select_optimal_channels(\n", "        df,\n", "        transformed_channels_by_promo,\n", "        target_column,\n", "        control_variables=control_variables,\n", "        benchmark_values=benchmark_values\n", "    )\n", "    \n", "    # Fix negative estimates in model\n", "   \n", "    corrected_features, fixed_channels_info = fix_negative_estimates(\n", "        df,\n", "        best_features,\n", "        target_column,\n", "        control_variables,\n", "        date_column,\n", "        transformed_channels_by_promo,\n", "        all_transformed_features,\n", "        id_column,\n", "        start_date,\n", "        end_date\n", "    )\n", "\n", "    # Update the best features for final regression\n", "    best_features = corrected_features\n", "    all_feature_sets[0] = corrected_features\n", "\n", "    # Run regression analyses\n", "    all_regression_results, best_result_table = run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics,promo_channels,spends_df,spend_column,spend_channel_column,unit_price)\n", "\n", "    channel_count, r_squared, modelled_sales, significant_channels=get_channel_stats(best_result_table,transformed_channels_by_promo)\n", "    print(f\"Channel_count:{channel_count}\")\n", "    params['channel_count']=channel_count\n", "    params['Significant_Channels']=significant_channels\n", "\n", "    update_model_regression(db_conn, model_run_id, all_transformed_features, best_result_table, \n", "                          transformed_channels_by_promo, adstocked_channels_by_promo, \n", "                          adstock_range_channel, params, all_regression_results,best_features,all_feature_sets)\n", "    \n", "   \n", "    print(best_result_table)\n", "    \n", "    close_db_connection(db_conn)\n", "    print(f\"After loading data {len(df)}\")\n", "    if df is None:\n", "        return \"Error in data loading.\"\n", "   "]}, {"cell_type": "code", "execution_count": 64, "id": "2de01507", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Date conversion: 180000/180000 dates successfully converted\n", "Dropped 0 rows with invalid dates\n", "[Organic_Engagement] Skipping 6397 outliers - would cause 84.95% drop (>10%)\n", "[Staff_Engagement] Skipping 1859 outliers - would cause 97.95% drop (>10%)\n", "[Email_Engagement] Skipping 136 outliers - would cause 100.00% drop (>10%)\n", "[Digital_Engagement] Skipping 696 outliers - would cause 100.00% drop (>10%)\n", "[Lead_Engagement] Skipping 1820 outliers - would cause 100.00% drop (>10%)\n", "[Speaker_programs] Skipping 69 outliers - would cause 100.00% drop (>10%)\n", "[Doximity_views] Skipping 2848 outliers - would cause 62.20% drop (>10%)\n", "[ReachMD] Skipping 8472 outliers - would cause 100.00% drop (>10%)\n", "[CRM_Engagement_Count] Skipping 476 outliers - would cause 93.68% drop (>10%)\n", "[Event_Attendance] Skipping 156 outliers - would cause 100.00% drop (>10%)\n", "Total outliers removed: 0\n", "Remaining rows: 180000\n", "Outlier summary:\n", "                 channel        before         after  Percentage drop\n", "0     Organic_Engagement   8530.000000   8530.000000              0.0\n", "1       Staff_Engagement   2091.100000   2091.100000              0.0\n", "2       Email_Engagement    514.000000    514.000000              0.0\n", "3     Digital_Engagement    631.500000    631.500000              0.0\n", "4        Lead_Engagement   1529.900000   1529.900000              0.0\n", "5       Speaker_programs     69.000000     69.000000              0.0\n", "6         Doximity_views  61456.000000  61456.000000              0.0\n", "7                ReachMD   8472.000000   8472.000000              0.0\n", "8   CRM_Engagement_Count   3086.000000   3086.000000              0.0\n", "9       Event_Attendance    156.000000    156.000000              0.0\n", "10                 Units   5127.533333   5127.533333              0.0\n", "Processed historical data with columns: Channel, Impact_Percent_2023\n", "Added 19 control variables\n", "\n", "Processing promotional channel: Organic_Engagement\n", "\n", "Selecting top 3 adstock and transformation combinations for: Organic_Engagement\n", "All adstock ranges:{'Organic_Engagement': {'type': 'Non-Personal Promotion', 'min_adstock': 20, 'max_adstock': 50}}\n", "Channel type: Non-Personal Promotion, Recommended adstock range: 20% to 50%\n", "\n", "Top 3 combinations selected for Organic_Engagement:\n", "\n", "Combination #1:\n", "- Adstock rate: 50%\n", "- Transformation: Root9\n", "- R²: 0.7913\n", "- p-value: 0.0000\n", "- AIC: -119918.1072\n", "- In recommended range: Yes\n", "- Final score: 0.8112\n", "\n", "Combination #2:\n", "- Adstock rate: 50%\n", "- Transformation: Root8\n", "- R²: 0.7913\n", "- p-value: 0.0000\n", "- AIC: -119906.3973\n", "- In recommended range: Yes\n", "- Final score: 0.7923\n", "\n", "Combination #3:\n", "- Adstock rate: 40%\n", "- Transformation: Root9\n", "- R²: 0.7912\n", "- p-value: 0.0000\n", "- AIC: -119893.4983\n", "- In recommended range: Yes\n", "- Final score: 0.7714\n", "- Best Adstock columns: Organic_Engagement_Adstock_50, Organic_Engagement_Adstock_50, Organic_Engagement_Adstock_40\n", "- Best Transformed columns: Organic_Engagement_Adstock_50_Root9, Organic_Engagement_Adstock_50_Root8, Organic_Engagement_Adstock_40_Root9\n", "\n", "Processing promotional channel: Staff_Engagement\n", "\n", "Selecting top 3 adstock and transformation combinations for: Staff_Engagement\n", "All adstock ranges:{'Staff_Engagement': {'type': 'Other', 'min_adstock': 10, 'max_adstock': 20}}\n", "Channel type: Other, Recommended adstock range: 10% to 20%\n", "\n", "Top 3 combinations selected for Staff_Engagement:\n", "\n", "Combination #1:\n", "- Adstock rate: 90%\n", "- Transformation: Root9\n", "- R²: 0.7910\n", "- p-value: 0.0000\n", "- AIC: -119731.9170\n", "- In recommended range: No\n", "- Final score: 0.7500\n", "\n", "Combination #2:\n", "- Adstock rate: 90%\n", "- Transformation: Root8\n", "- R²: 0.7910\n", "- p-value: 0.0000\n", "- AIC: -119729.8705\n", "- In recommended range: No\n", "- Final score: 0.7420\n", "\n", "Combination #3:\n", "- Adstock rate: 90%\n", "- Transformation: Root7\n", "- R²: 0.7910\n", "- p-value: 0.0000\n", "- AIC: -119725.2498\n", "- In recommended range: No\n", "- Final score: 0.7238\n", "- Best Adstock columns: Staff_Engagement_Adstock_90, Staff_Engagement_Adstock_90, Staff_Engagement_Adstock_90\n", "- Best Transformed columns: Staff_Engagement_Adstock_90_Root9, Staff_Engagement_Adstock_90_Root8, Staff_Engagement_Adstock_90_Root7\n", "\n", "Processing promotional channel: Email_Engagement\n", "\n", "Selecting top 3 adstock and transformation combinations for: Email_Engagement\n", "All adstock ranges:{'Email_Engagement': {'type': 'Non-Personal Promotion', 'min_adstock': 20, 'max_adstock': 50}}\n", "Channel type: Non-Personal Promotion, Recommended adstock range: 20% to 50%\n", "\n", "Top 3 combinations selected for Email_Engagement:\n", "\n", "Combination #1:\n", "- Adstock rate: 20%\n", "- Transformation: Root7\n", "- R²: 0.7908\n", "- p-value: 0.0000\n", "- AIC: -119651.3980\n", "- In recommended range: Yes\n", "- Final score: 0.9466\n", "\n", "Combination #2:\n", "- Adstock rate: 20%\n", "- Transformation: Root8\n", "- R²: 0.7908\n", "- p-value: 0.0000\n", "- AIC: -119650.3860\n", "- In recommended range: Yes\n", "- Final score: 0.9375\n", "\n", "Combination #3:\n", "- Adstock rate: 20%\n", "- Transformation: Root6\n", "- R²: 0.7908\n", "- p-value: 0.0000\n", "- AIC: -119649.2130\n", "- In recommended range: Yes\n", "- Final score: 0.9270\n", "- Best Adstock columns: Email_Engagement_Adstock_20, Email_Engagement_Adstock_20, Email_Engagement_Adstock_20\n", "- Best Transformed columns: Email_Engagement_Adstock_20_Root7, Email_Engagement_Adstock_20_Root8, Email_Engagement_Adstock_20_Root6\n", "\n", "Processing promotional channel: Digital_Engagement\n", "\n", "Selecting top 3 adstock and transformation combinations for: Digital_Engagement\n", "All adstock ranges:{'Digital_Engagement': {'type': 'Non-Personal Promotion', 'min_adstock': 20, 'max_adstock': 50}}\n", "Channel type: Non-Personal Promotion, Recommended adstock range: 20% to 50%\n", "\n", "Top 3 combinations selected for Digital_Engagement:\n", "\n", "Combination #1:\n", "- Adstock rate: 90%\n", "- Transformation: Root9\n", "- R²: 0.7935\n", "- p-value: 0.0000\n", "- AIC: -121190.5612\n", "- In recommended range: No\n", "- Final score: 0.7500\n", "\n", "Combination #2:\n", "- Adstock rate: 50%\n", "- Transformation: Root4\n", "- R²: 0.7924\n", "- p-value: 0.0000\n", "- AIC: -120559.1887\n", "- In recommended range: Yes\n", "- Final score: 0.7333\n", "\n", "Combination #3:\n", "- Adstock rate: 50%\n", "- Transformation: Root5\n", "- R²: 0.7924\n", "- p-value: 0.0000\n", "- AIC: -120551.6835\n", "- In recommended range: Yes\n", "- Final score: 0.7301\n", "- Best Adstock columns: Digital_Engagement_Adstock_90, Digital_Engagement_Adstock_50, Digital_Engagement_Adstock_50\n", "- Best Transformed columns: Digital_Engagement_Adstock_90_Root9, Digital_Engagement_Adstock_50_Root4, Digital_Engagement_Adstock_50_Root5\n", "\n", "Processing promotional channel: Lead_Engagement\n", "\n", "Selecting top 3 adstock and transformation combinations for: Lead_Engagement\n", "All adstock ranges:{'Lead_Engagement': {'type': 'Non-Personal Promotion', 'min_adstock': 20, 'max_adstock': 50}}\n", "Channel type: Non-Personal Promotion, Recommended adstock range: 20% to 50%\n", "\n", "Top 3 combinations selected for Lead_Engagement:\n", "\n", "Combination #1:\n", "- Adstock rate: 50%\n", "- Transformation: Root9\n", "- R²: 0.7916\n", "- p-value: 0.0000\n", "- AIC: -120111.8595\n", "- In recommended range: Yes\n", "- Final score: 0.7567\n", "\n", "Combination #2:\n", "- Adstock rate: 90%\n", "- Transformation: Root9\n", "- R²: 0.7922\n", "- p-value: 0.0000\n", "- AIC: -120439.2520\n", "- In recommended range: No\n", "- Final score: 0.7500\n", "\n", "Combination #3:\n", "- Adstock rate: 50%\n", "- Transformation: Root8\n", "- R²: 0.7916\n", "- p-value: 0.0000\n", "- AIC: -120074.3027\n", "- In recommended range: Yes\n", "- Final score: 0.7288\n", "- Best Adstock columns: Lead_Engagement_Adstock_50, Lead_Engagement_Adstock_90, Lead_Engagement_Adstock_50\n", "- Best Transformed columns: Lead_Engagement_Adstock_50_Root9, Lead_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_50_Root8\n", "\n", "Processing promotional channel: Speaker_programs\n", "\n", "Selecting top 3 adstock and transformation combinations for: Speaker_programs\n", "All adstock ranges:{'Speaker_programs': {'type': 'Other', 'min_adstock': 70, 'max_adstock': 90}}\n", "Channel type: Other, Recommended adstock range: 70% to 90%\n", "\n", "Top 3 combinations selected for Speaker_programs:\n", "\n", "Combination #1:\n", "- Adstock rate: 80%\n", "- Transformation: Log\n", "- R²: 0.7907\n", "- p-value: 0.3286\n", "- AIC: -119595.4916\n", "- In recommended range: Yes\n", "- Final score: 1.0000\n", "\n", "Combination #2:\n", "- Adstock rate: 70%\n", "- Transformation: Root5\n", "- R²: 0.7907\n", "- p-value: 0.3354\n", "- AIC: -119595.4649\n", "- In recommended range: Yes\n", "- Final score: 0.9834\n", "\n", "Combination #3:\n", "- Adstock rate: 80%\n", "- Transformation: Root8\n", "- R²: 0.7907\n", "- p-value: 0.3354\n", "- AIC: -119595.4649\n", "- In recommended range: Yes\n", "- Final score: 0.9834\n", "- Best Adstock columns: Speaker_programs_<PERSON><PERSON>_80, Speaker_programs_<PERSON><PERSON>_70, Speaker_programs_<PERSON>stock_80\n", "- Best Transformed columns: Speaker_programs_<PERSON><PERSON>_80_Log, Speaker_programs_<PERSON><PERSON>_70_Root5, Speaker_programs_<PERSON><PERSON>_80_Root8\n", "\n", "Processing promotional channel: Doximity_views\n", "\n", "Selecting top 3 adstock and transformation combinations for: Doximity_views\n", "All adstock ranges:{'Doximity_views': {'type': 'Non-Personal Promotion', 'min_adstock': 20, 'max_adstock': 50}}\n", "Channel type: Non-Personal Promotion, Recommended adstock range: 20% to 50%\n", "\n", "Top 3 combinations selected for Doximity_views:\n", "\n", "Combination #1:\n", "- Adstock rate: 50%\n", "- Transformation: Root4\n", "- R²: 0.7908\n", "- p-value: 0.0003\n", "- AIC: -119607.5888\n", "- In recommended range: Yes\n", "- Final score: 0.8778\n", "\n", "Combination #2:\n", "- Adstock rate: 40%\n", "- Transformation: Root4\n", "- R²: 0.7907\n", "- p-value: 0.0006\n", "- AIC: -119606.3064\n", "- In recommended range: Yes\n", "- Final score: 0.8500\n", "\n", "Combination #3:\n", "- Adstock rate: 30%\n", "- Transformation: Root4\n", "- R²: 0.7907\n", "- p-value: 0.0013\n", "- AIC: -119604.9015\n", "- In recommended range: Yes\n", "- Final score: 0.8195\n", "- Best Adstock columns: Doximity_views_Adstock_50, Doximity_views_Adstock_40, Doximity_views_Adstock_30\n", "- Best Transformed columns: Doximity_views_Adstock_50_Root4, Doximity_views_Adstock_40_Root4, Doximity_views_Adstock_30_Root4\n", "\n", "Processing promotional channel: ReachMD\n", "\n", "Selecting top 3 adstock and transformation combinations for: ReachMD\n", "All adstock ranges:{'ReachMD': {'type': 'Personal Promotion', 'min_adstock': 70, 'max_adstock': 80}}\n", "Channel type: Personal Promotion, Recommended adstock range: 70% to 80%\n", "\n", "Top 3 combinations selected for ReachMD:\n", "\n", "Combination #1:\n", "- Adstock rate: 80%\n", "- Transformation: Root4\n", "- R²: 0.7907\n", "- p-value: 0.0195\n", "- AIC: -119599.9908\n", "- In recommended range: Yes\n", "- Final score: 0.9220\n", "\n", "Combination #2:\n", "- Adstock rate: 80%\n", "- Transformation: Root5\n", "- R²: 0.7907\n", "- p-value: 0.0355\n", "- AIC: -119598.9568\n", "- In recommended range: Yes\n", "- Final score: 0.8591\n", "\n", "Combination #3:\n", "- Adstock rate: 70%\n", "- Transformation: Root4\n", "- R²: 0.7907\n", "- p-value: 0.0430\n", "- AIC: -119598.6326\n", "- In recommended range: Yes\n", "- Final score: 0.8391\n", "- Best Adstock columns: ReachMD_Adstock_80, ReachMD_Adstock_80, ReachMD_Adstock_70\n", "- Best Transformed columns: ReachMD_Adstock_80_Root4, ReachMD_Adstock_80_Root5, ReachMD_Adstock_70_Root4\n", "\n", "Processing promotional channel: CRM_Engagement_Count\n", "\n", "Selecting top 3 adstock and transformation combinations for: CRM_Engagement_Count\n", "All adstock ranges:{'CRM_Engagement_Count': {'type': 'Personal Promotion', 'min_adstock': 70, 'max_adstock': 80}}\n", "Channel type: Personal Promotion, Recommended adstock range: 70% to 80%\n", "\n", "Top 3 combinations selected for CRM_Engagement_Count:\n", "\n", "Combination #1:\n", "- Adstock rate: 80%\n", "- Transformation: Log\n", "- R²: 0.7907\n", "- p-value: 0.6231\n", "- AIC: -119594.7785\n", "- In recommended range: Yes\n", "- Final score: 1.0000\n", "\n", "Combination #2:\n", "- Adstock rate: 70%\n", "- Transformation: Log\n", "- R²: 0.7907\n", "- p-value: 0.6296\n", "- AIC: -119594.7696\n", "- In recommended range: Yes\n", "- Final score: 0.9771\n", "\n", "Combination #3:\n", "- Adstock rate: 70%\n", "- Transformation: Root5\n", "- R²: 0.7907\n", "- p-value: 0.6374\n", "- AIC: -119594.7592\n", "- In recommended range: Yes\n", "- Final score: 0.9503\n", "- Best Adstock columns: CRM_Engagement_Count_Adstock_80, CRM_Engagement_Count_Adstock_70, CRM_Engagement_Count_Adstock_70\n", "- Best Transformed columns: CRM_Engagement_Count_Adstock_80_Log, CRM_Engagement_Count_Adstock_70_Log, CRM_Engagement_Count_Adstock_70_Root5\n", "\n", "Processing promotional channel: Event_Attendance\n", "\n", "Selecting top 3 adstock and transformation combinations for: Event_Attendance\n", "All adstock ranges:{'Event_Attendance': {'type': 'Other', 'min_adstock': 70, 'max_adstock': 90}}\n", "Channel type: Other, Recommended adstock range: 70% to 90%\n", "\n", "Top 3 combinations selected for Event_Attendance:\n", "\n", "Combination #1:\n", "- Adstock rate: 90%\n", "- Transformation: Root9\n", "- R²: 0.7908\n", "- p-value: 0.0002\n", "- AIC: -119608.6600\n", "- In recommended range: Yes\n", "- Final score: 1.0000\n", "\n", "Combination #2:\n", "- Adstock rate: 80%\n", "- Transformation: Log\n", "- R²: 0.7908\n", "- p-value: 0.0002\n", "- AIC: -119608.5580\n", "- In recommended range: Yes\n", "- Final score: 0.9925\n", "\n", "Combination #3:\n", "- Adstock rate: 80%\n", "- Transformation: Root7\n", "- R²: 0.7908\n", "- p-value: 0.0002\n", "- AIC: -119608.4961\n", "- In recommended range: Yes\n", "- Final score: 0.9879\n", "- Best Adstock columns: Event_Attendance_Adstock_90, Event_Attendance_Adstock_80, Event_Attendance_Adstock_80\n", "- Best Transformed columns: Event_Attendance_Adstock_90_Root9, Event_Attendance_Adstock_80_Log, Event_Attendance_Adstock_80_Root7\n", "\n", "Selecting optimal channel combination...\n", "before optimal channel selection 180000\n", "        Identifier Time_Period  Units  Organic_Engagement  Staff_Engagement  \\\n", "6                1  2024-01-01    0.0                 0.0               0.0   \n", "7                1  2024-02-01    0.0                 0.0               0.0   \n", "8                1  2024-03-01    0.0                 0.0               0.0   \n", "9                1  2024-04-01    0.0                 0.0               0.0   \n", "10               1  2024-05-01    0.0                 0.0               0.0   \n", "...            ...         ...    ...                 ...               ...   \n", "179995       48732  2024-08-01    0.0                 0.0               0.0   \n", "179996       48732  2024-09-01    0.0                 0.0               0.0   \n", "179997       48732  2024-10-01    0.0                 0.0               0.0   \n", "179998       48732  2024-11-01    0.0                 0.0               0.0   \n", "179999       48732  2024-12-01    0.0                 0.0               0.0   \n", "\n", "        Email_Engagement  Digital_Engagement  Lead_Engagement  \\\n", "6                      0                 0.0              0.0   \n", "7                      0                 0.0              0.0   \n", "8                      0                 0.0              0.0   \n", "9                      0                 0.0              0.0   \n", "10                     0                 0.0              0.0   \n", "...                  ...                 ...              ...   \n", "179995                 0                 0.0              0.0   \n", "179996                 0                 0.0              0.0   \n", "179997                 0                 0.0              0.0   \n", "179998                 0                 0.0              0.0   \n", "179999                 0                 0.0              0.0   \n", "\n", "        Speaker_programs  Doximity_views  ...  \\\n", "6                      0               0  ...   \n", "7                      0               0  ...   \n", "8                      0               0  ...   \n", "9                      0               0  ...   \n", "10                     0               0  ...   \n", "...                  ...             ...  ...   \n", "179995                 0               0  ...   \n", "179996                 0               0  ...   \n", "179997                 0               0  ...   \n", "179998                 0               0  ...   \n", "179999                 0               0  ...   \n", "\n", "        CRM_Engagement_Count_Adstock_80  CRM_Engagement_Count_Adstock_70_Log  \\\n", "6                                   0.0                                  0.0   \n", "7                                   0.0                                  0.0   \n", "8                                   0.0                                  0.0   \n", "9                                   0.0                                  0.0   \n", "10                                  0.0                                  0.0   \n", "...                                 ...                                  ...   \n", "179995                              0.0                                  0.0   \n", "179996                              0.0                                  0.0   \n", "179997                              0.0                                  0.0   \n", "179998                              0.0                                  0.0   \n", "179999                              0.0                                  0.0   \n", "\n", "        CRM_Engagement_Count_Adstock_70  \\\n", "6                                   0.0   \n", "7                                   0.0   \n", "8                                   0.0   \n", "9                                   0.0   \n", "10                                  0.0   \n", "...                                 ...   \n", "179995                              0.0   \n", "179996                              0.0   \n", "179997                              0.0   \n", "179998                              0.0   \n", "179999                              0.0   \n", "\n", "        CRM_Engagement_Count_Adstock_70_Root5  \\\n", "6                                         0.0   \n", "7                                         0.0   \n", "8                                         0.0   \n", "9                                         0.0   \n", "10                                        0.0   \n", "...                                       ...   \n", "179995                                    0.0   \n", "179996                                    0.0   \n", "179997                                    0.0   \n", "179998                                    0.0   \n", "179999                                    0.0   \n", "\n", "        CRM_Engagement_Count_Adstock_80_Log  Event_Attendance_Adstock_80  \\\n", "6                                       0.0                          0.0   \n", "7                                       0.0                          0.0   \n", "8                                       0.0                          0.0   \n", "9                                       0.0                          0.0   \n", "10                                      0.0                          0.0   \n", "...                                     ...                          ...   \n", "179995                                  0.0                          0.0   \n", "179996                                  0.0                          0.0   \n", "179997                                  0.0                          0.0   \n", "179998                                  0.0                          0.0   \n", "179999                                  0.0                          0.0   \n", "\n", "        Event_Attendance_Adstock_90  Event_Attendance_Adstock_80_Root7  \\\n", "6                               0.0                                0.0   \n", "7                               0.0                                0.0   \n", "8                               0.0                                0.0   \n", "9                               0.0                                0.0   \n", "10                              0.0                                0.0   \n", "...                             ...                                ...   \n", "179995                          0.0                                0.0   \n", "179996                          0.0                                0.0   \n", "179997                          0.0                                0.0   \n", "179998                          0.0                                0.0   \n", "179999                          0.0                                0.0   \n", "\n", "        Event_Attendance_Adstock_90_Root9  Event_Attendance_Adstock_80_Log  \n", "6                                     0.0                              0.0  \n", "7                                     0.0                              0.0  \n", "8                                     0.0                              0.0  \n", "9                                     0.0                              0.0  \n", "10                                    0.0                              0.0  \n", "...                                   ...                              ...  \n", "179995                                0.0                              0.0  \n", "179996                                0.0                              0.0  \n", "179997                                0.0                              0.0  \n", "179998                                0.0                              0.0  \n", "179999                                0.0                              0.0  \n", "\n", "[120000 rows x 96 columns]\n", "\n", "Selecting optimal channel combination using one-variable-at-a-time optimization...\n", "Evaluating 10 promotional channels using one-variable-at-a-time optimization...\n", "\n", "Tuning channel 1/10: Organic_Engagement\n", "Testing 3 transformations for Organic_Engagement...\n", "  Testing transformation 1/3: Organic_Engagement_Adstock_50_Root9\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root9', 'Email_Engagement_Adstock_20_Root7', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Organic_Engagement! Score: 0.2308\n", "    R²: 0.7942, AIC: -121601.73\n", "  Testing transformation 2/3: Organic_Engagement_Adstock_50_Root8\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root8', 'Staff_Engagement_Adstock_90_Root9', 'Email_Engagement_Adstock_20_Root7', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "  Testing transformation 3/3: Organic_Engagement_Adstock_40_Root9\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_40_Root9', 'Staff_Engagement_Adstock_90_Root9', 'Email_Engagement_Adstock_20_Root7', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for Organic_Engagement: Organic_Engagement_Adstock_50_Root9\n", "Score: 0.2308\n", "\n", "Tuning channel 2/10: Staff_Engagement\n", "Testing 3 transformations for Staff_Engagement...\n", "  Testing transformation 1/3: Staff_Engagement_Adstock_90_Root9\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root9', 'Email_Engagement_Adstock_20_Root7', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Staff_Engagement! Score: 0.2308\n", "    R²: 0.7942, AIC: -121601.73\n", "  Testing transformation 2/3: Staff_Engagement_Adstock_90_Root8\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root7', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Staff_Engagement! Score: 0.2308\n", "    R²: 0.7942, AIC: -121601.81\n", "  Testing transformation 3/3: Staff_Engagement_Adstock_90_Root7\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root7', 'Email_Engagement_Adstock_20_Root7', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for Staff_Engagement: Staff_Engagement_Adstock_90_Root8\n", "Score: 0.2308\n", "\n", "Tuning channel 3/10: Email_Engagement\n", "Testing 3 transformations for Email_Engagement...\n", "  Testing transformation 1/3: Email_Engagement_Adstock_20_Root7\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root7', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Email_Engagement! Score: 0.2308\n", "    R²: 0.7942, AIC: -121601.81\n", "  Testing transformation 2/3: Email_Engagement_Adstock_20_Root8\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Email_Engagement! Score: 0.2308\n", "    R²: 0.7942, AIC: -121604.18\n", "  Testing transformation 3/3: Email_Engagement_Adstock_20_Root6\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root6', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for Email_Engagement: Email_Engagement_Adstock_20_Root8\n", "Score: 0.2308\n", "\n", "Tuning channel 4/10: Digital_Engagement\n", "Testing 3 transformations for Digital_Engagement...\n", "  Testing transformation 1/3: Digital_Engagement_Adstock_90_Root9\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Digital_Engagement! Score: 0.2308\n", "    R²: 0.7942, AIC: -121604.18\n", "  Testing transformation 2/3: Digital_Engagement_Adstock_50_Root4\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_50_Root4', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "  Testing transformation 3/3: Digital_Engagement_Adstock_50_Root5\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_50_Root5', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for Digital_Engagement: Digital_Engagement_Adstock_90_Root9\n", "Score: 0.2308\n", "\n", "Tuning channel 5/10: Lead_Engagement\n", "Testing 3 transformations for Lead_Engagement...\n", "  Testing transformation 1/3: Lead_Engagement_Adstock_50_Root9\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Lead_Engagement! Score: 0.2308\n", "    R²: 0.7942, AIC: -121604.18\n", "  Testing transformation 2/3: Lead_Engagement_Adstock_90_Root9\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Lead_Engagement! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 3/3: Lead_Engagement_Adstock_50_Root8\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_50_Root8', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for Lead_Engagement: Lead_Engagement_Adstock_90_Root9\n", "Score: 0.2309\n", "\n", "Tuning channel 6/10: Speaker_programs\n", "Testing 3 transformations for Speaker_programs...\n", "  Testing transformation 1/3: Speaker_programs_Adstock_80_Log\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Speaker_programs! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 2/3: Speaker_programs_Adstock_70_Root5\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_70_Root5', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Speaker_programs! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 3/3: Speaker_programs_Ad<PERSON>_80_Root8\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Speaker_programs! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "\n", "Best transformation for Speaker_programs: Speaker_programs_Adstock_80_Root8\n", "Score: 0.2309\n", "\n", "Tuning channel 7/10: Doximity_views\n", "Testing 3 transformations for Doximity_views...\n", "  Testing transformation 1/3: Doximity_views_Adstock_50_Root4\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Doximity_views! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 2/3: Doximity_views_Adstock_40_Root4\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_40_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "  Testing transformation 3/3: Doximity_views_Adstock_30_Root4\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_30_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for Doximity_views: Doximity_views_Adstock_50_Root4\n", "Score: 0.2309\n", "\n", "Tuning channel 8/10: ReachMD\n", "Testing 3 transformations for ReachMD...\n", "  Testing transformation 1/3: ReachMD_Adstock_80_Root4\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel ReachMD! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 2/3: ReachMD_Adstock_80_Root5\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root5', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "  Testing transformation 3/3: ReachMD_Adstock_70_Root4\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_70_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for ReachMD: ReachMD_Adstock_80_Root4\n", "Score: 0.2309\n", "\n", "Tuning channel 9/10: CRM_Engagement_Count\n", "Testing 3 transformations for CRM_Engagement_Count...\n", "  Testing transformation 1/3: CRM_Engagement_Count_Adstock_80_Log\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel CRM_Engagement_Count! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 2/3: CRM_Engagement_Count_Adstock_70_Log\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_70_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel CRM_Engagement_Count! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 3/3: CRM_Engagement_Count_Adstock_70_Root5\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_70_Root5', 'Event_Attendance_Adstock_90_Root9']\n", "\n", "Best transformation for CRM_Engagement_Count: CRM_Engagement_Count_Adstock_70_Log\n", "Score: 0.2309\n", "\n", "Tuning channel 10/10: Event_Attendance\n", "Testing 3 transformations for Event_Attendance...\n", "  Testing transformation 1/3: Event_Attendance_Adstock_90_Root9\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_70_Log', 'Event_Attendance_Adstock_90_Root9']\n", "    New best for channel Event_Attendance! Score: 0.2309\n", "    R²: 0.7945, AIC: -121752.26\n", "  Testing transformation 2/3: Event_Attendance_Adstock_80_Log\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_70_Log', 'Event_Attendance_Adstock_80_Log']\n", "  Testing transformation 3/3: Event_Attendance_Adstock_80_Root7\n", "Feature set['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_70_Log', 'Event_Attendance_Adstock_80_Root7']\n", "\n", "Best transformation for Event_Attendance: Event_Attendance_Adstock_90_Root9\n", "Score: 0.2309\n", "Saved detailed results to optimal_channel_selection_results.csv\n", "Saved top 5 alternative models to top5_alternative_models.csv\n", "\n", "Final optimal channel combination:\n", "- Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Root8, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_70_Log, Event_Attendance_Adstock_90_Root9\n", "- R²: 0.7945\n", "- AIC: -121752.26\n", "- Avg. p-value: 0.1935\n", "- Composite score: 0.2309\n", "\n", "Top 5 alternative models:\n", "1. Score: 0.2309, R²: 0.7945\n", "   Channel: Doximity_views, Transform: Doximity_views_Adstock_50_Root4\n", "   Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Root8, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "2. Score: 0.2309, R²: 0.7945\n", "   Channel: Speaker_programs, Transform: Speaker_programs_Adstock_70_Root5\n", "   Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_70_Root5, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "3. Score: 0.2309, R²: 0.7945\n", "   Channel: Speaker_programs, Transform: Speaker_programs_Adstock_80_Log\n", "   Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Log, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "4. Score: 0.2309, R²: 0.7945\n", "   Channel: CRM_Engagement_Count, Transform: CRM_Engagement_Count_Adstock_70_Root5\n", "   Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Root8, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_70_Root5, Event_Attendance_Adstock_90_Root9\n", "5. Score: 0.2309, R²: 0.7945\n", "   Channel: Doximity_views, Transform: Doximity_views_Adstock_40_Root4\n", "   Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Root8, Doximity_views_Adstock_40_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "\n", "Checking for negative estimates in best model...\n", "Found 2 promotional channels with negative estimates:\n", "- Speaker_programs (via Speaker_programs_Adstock_80_Root8): -0.0015\n", "- CRM_Engagement_Count (via CRM_Engagement_Count_Adstock_70_Log): -0.0007\n", "\n", "Attempting to fix negative estimate for Speaker_programs...\n", "Could not find positive estimate for Speaker_programs. Will remove from model.\n", "\n", "Attempting to fix negative estimate for CRM_Engagement_Count...\n", "Could not find positive estimate for CRM_Engagement_Count. Will remove from model.\n", "\n", "Original model had 11 features.\n", "New model has 9 features.\n", "Fixed 0 channels with negative estimates.\n", "Removed 2 channels that couldn't be fixed.\n", "best features in run_regression_analyses['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'Event_Attendance_Adstock_90_Root9']\n", "all_feature_sets in run_regression_analyses[['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'Event_Attendance_Adstock_90_Root9'], ['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9'], ['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_70_Root5', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9'], ['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Log', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9'], ['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_70_Root5', 'Event_Attendance_Adstock_90_Root9'], ['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Speaker_programs_Adstock_80_Root8', 'Doximity_views_Adstock_40_Root4', 'ReachMD_Adstock_80_Root4', 'CRM_Engagement_Count_Adstock_80_Log', 'Event_Attendance_Adstock_90_Root9']]\n", "\n", "Running regression analysis for top 6 models...\n", "\n", "Running regression for Best Model...\n", "Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, Event_Attendance_Adstock_90_Root9\n", "R²: 0.7945, AIC: -121752.26\n", "\n", "Running regression for Alternative Model 1...\n", "Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Root8, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "R²: 0.7945, AIC: -121752.26\n", "\n", "Running regression for Alternative Model 2...\n", "Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_70_Root5, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "R²: 0.7945, AIC: -121752.26\n", "\n", "Running regression for Alternative Model 3...\n", "Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Log, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "R²: 0.7945, AIC: -121752.26\n", "\n", "Running regression for Alternative Model 4...\n", "Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Root8, Doximity_views_Adstock_50_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_70_Root5, Event_Attendance_Adstock_90_Root9\n", "R²: 0.7945, AIC: -121752.23\n", "\n", "Running regression for Alternative Model 5...\n", "Promotional channels: Organic_Engagement_Adstock_50_Root9, Staff_Engagement_Adstock_90_Root8, Email_Engagement_Adstock_20_Root8, Digital_Engagement_Adstock_90_Root9, Lead_Engagement_Adstock_90_Root9, Speaker_programs_Adstock_80_Root8, Doximity_views_Adstock_40_Root4, ReachMD_Adstock_80_Root4, CRM_Engagement_Count_Adstock_80_Log, Event_Attendance_Adstock_90_Root9\n", "R²: 0.7945, AIC: -121751.97\n", "\n", "Adding ROI calculation to regression results...\n", "\n", "Best model results with ROI:\n", "                            Channel  Estimate  Impact Percentage       ROI\n", "                        Units_lag_1  0.873995          85.989321       NaN\n", "Organic_Engagement_Adstock_50_Root9  0.011135           3.756210 10.786061\n", "  Staff_Engagement_Adstock_90_Root8  0.004421           1.342494  2.570006\n", "  Email_Engagement_Adstock_20_Root8  0.023064           0.198630  0.456297\n", "Digital_Engagement_Adstock_90_Root9  0.093342           8.863304 13.882486\n", "   Lead_Engagement_Adstock_90_Root9  0.038046           8.512157 20.951054\n", "    Doximity_views_Adstock_50_Root4  0.000519           0.475891  2.049801\n", "           ReachMD_Adstock_80_Root4  0.003016           2.650738  9.134001\n", "  Event_Attendance_Adstock_90_Root9  0.023903           0.443201  0.610879\n", "Channel_count:['Units_lag', 'Organic_Engagement', 'Staff_Engagement', 'Email_Engagement', 'Digital_Engagement', 'Lead_Engagement', 'Doximity_views', 'ReachMD', 'Event_Attendance']\n", "Successfully updated model run data for model_run_id: 00000000-0000-0000-0000-000000000001, status set to complete\n", "                               Channel  Estimate  Impact Percentage  \\\n", "0                          Units_lag_1  0.873995          85.989321   \n", "1  Organic_Engagement_Adstock_50_Root9  0.011135           3.756210   \n", "2    Staff_Engagement_Adstock_90_Root8  0.004421           1.342494   \n", "3    Email_Engagement_Adstock_20_Root8  0.023064           0.198630   \n", "4  Digital_Engagement_Adstock_90_Root9  0.093342           8.863304   \n", "5     Lead_Engagement_Adstock_90_Root9  0.038046           8.512157   \n", "6      Doximity_views_Adstock_50_Root4  0.000519           0.475891   \n", "7             ReachMD_Adstock_80_Root4  0.003016           2.650738   \n", "8    Event_Attendance_Adstock_90_Root9  0.023903           0.443201   \n", "9                            Intercept -0.003512         -12.231946   \n", "\n", "         P-Value  Effectiveness  Linear Activity        R²  Adjusted R²  \\\n", "0   0.000000e+00    3011.642826      3445.833333  0.794487     0.794472   \n", "1   1.569110e-14      61.190382      5495.400000  0.794487     0.794472   \n", "2   3.070124e-03       1.882828       425.900000  0.794487     0.794472   \n", "3   3.264619e-10       7.610964       330.000000  0.794487     0.794472   \n", "4  5.697398e-224      39.408960       422.200000  0.794487     0.794472   \n", "5   2.000856e-89      39.468970      1037.400000  0.794487     0.794472   \n", "6   4.264486e-01      22.034759     42426.000000  0.794487     0.794472   \n", "7   3.068965e-03      14.804566      4908.000000  0.794487     0.794472   \n", "8   2.340152e-04       3.728907       156.000000  0.794487     0.794472   \n", "9   1.578266e-09            NaN              NaN  0.794487     0.794472   \n", "\n", "            AIC      RMSE  Total Modeled Activity  Modeled Sales  \\\n", "0 -121756.17693  0.145681             3390.233333    3445.833333   \n", "1 -121756.17693  0.145681            11624.124820    3445.833333   \n", "2 -121756.17693  0.145681            10464.141766    3445.833333   \n", "3 -121756.17693  0.145681              296.765263    3445.833333   \n", "4 -121756.17693  0.145681             3271.999083    3445.833333   \n", "5 -121756.17693  0.145681             7709.467011    3445.833333   \n", "6 -121756.17693  0.145681            31573.702080    3445.833333   \n", "7 -121756.17693  0.145681            30280.980664    3445.833333   \n", "8 -121756.17693  0.145681              638.907892    3445.833333   \n", "9 -121756.17693  0.145681                     NaN    3445.833333   \n", "\n", "   Actual Sales  Channel_count significance       model  model_rank        ROI  \n", "0   3445.833333              9         True  Best Model           1        NaN  \n", "1   3445.833333              9         True  Best Model           1  10.786061  \n", "2   3445.833333              9         True  Best Model           1   2.570006  \n", "3   3445.833333              9         True  Best Model           1   0.456297  \n", "4   3445.833333              9         True  Best Model           1  13.882486  \n", "5   3445.833333              9         True  Best Model           1  20.951054  \n", "6   3445.833333              9        False  Best Model           1   2.049801  \n", "7   3445.833333              9         True  Best Model           1   9.134001  \n", "8   3445.833333              9         True  Best Model           1   0.610879  \n", "9   3445.833333              9            0  Best Model           1        NaN  \n", "After loading data 120000\n"]}], "source": ["# transformed_channels_by_promo={}\n", "# result_df=None\n", "# all_transformed_features=[]\n", "\n", "# control_variables=[]\n", "# date_column=None\n", "# params={}\n", "# best_result_table=None\n", "\n", "\n", "run_pipeline()"]}, {"cell_type": "markdown", "id": "4a35961f", "metadata": {}, "source": ["# Extract Model features from database"]}, {"cell_type": "code", "execution_count": 19, "id": "62caaf0f", "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import datetime\n", "import logging\n", "from psycopg2.extras import RealDictCursor\n", "from uuid import UUID\n", "\n", "def extract_model_data(conn, model_run_id):\n", "    \"\"\"\n", "    Extract ALL model data from the database for a specific model_run_id and convert it back\n", "    to its original Python data structures.\n", "    \n", "    Parameters:\n", "    conn: PostgreSQL connection object\n", "    model_run_id: UUID of the model run to extract\n", "    \n", "    Returns:\n", "    dict: Dictionary containing all the extracted model data in their original format\n", "    \"\"\"\n", "    if not conn or conn.closed:\n", "        logging.error(\"Invalid database connection\")\n", "        return None\n", "    \n", "    try:\n", "        # Create a cursor that returns rows as dictionaries\n", "        cursor = conn.cursor(cursor_factory=RealDictCursor)\n", "        \n", "        # SQL query to extract ALL data for a specific model_run_id\n", "        query = \"\"\"\n", "        SELECT *\n", "        FROM model_data\n", "        WHERE model_run_id = %s\n", "        \"\"\"\n", "        \n", "        # Execute the query\n", "        cursor.execute(query, (model_run_id,))\n", "        \n", "        # Fetch the result\n", "        result = cursor.fetchone()\n", "        \n", "        if not result:\n", "            logging.error(f\"No data found for model_run_id: {model_run_id}\")\n", "            return None\n", "            \n", "        # Convert result to Python data structures\n", "        model_data = {}\n", "        \n", "        # Process standard columns (non-JSONB types)\n", "        for column in result:\n", "            value = result[column]\n", "            \n", "            # Skip columns we'll process specially later\n", "            if column in ['all_transformed_features', 'best_result_table', 'transformed_channels_by_promo',\n", "                         'adstocked_channels_by_promo', 'adstock_range_channel', 'parms',\n", "                         'model_results', 'best_features', 'all_features']:\n", "                continue\n", "                \n", "            # Process special types\n", "            if isinstance(value, UUID):\n", "                model_data[column] = str(value)\n", "            elif isinstance(value, list) and column == 'promotional_columns':\n", "                # Keep array types as lists\n", "                model_data[column] = value\n", "            else:\n", "                # Copy other values directly\n", "                model_data[column] = value\n", "        \n", "        # Convert JSONB columns back to appropriate Python structures\n", "        \n", "        # Convert all_transformed_features back to list\n", "        if result['all_transformed_features']:\n", "            model_data['all_transformed_features'] = result['all_transformed_features']\n", "            # If it contains more complex data, additional conversion may be needed\n", "        \n", "        # Convert best_result_table back to DataFrame\n", "        if result['best_result_table']:\n", "            if isinstance(result['best_result_table'], list):\n", "                model_data['best_result_table'] = pd.DataFrame(result['best_result_table'])\n", "            else:\n", "                # Handle case where best_result_table is stored in a different format\n", "                model_data['best_result_table'] = pd.DataFrame.from_dict(result['best_result_table'])\n", "        \n", "        # Convert transformed_channels_by_promo back to dict\n", "        if result['transformed_channels_by_promo']:\n", "            model_data['transformed_channels_by_promo'] = result['transformed_channels_by_promo']\n", "            # Convert inner values to appropriate data structures if needed\n", "            for key, value in model_data['transformed_channels_by_promo'].items():\n", "                if isinstance(value, list):\n", "                    # Check if this looks like DataFrame data\n", "                    if value and isinstance(value[0], dict) and all(isinstance(item, dict) for item in value):\n", "                        model_data['transformed_channels_by_promo'][key] = pd.DataFrame(value)\n", "        \n", "        # Convert adstocked_channels_by_promo back to dict\n", "        if result['adstocked_channels_by_promo']:\n", "            model_data['adstocked_channels_by_promo'] = result['adstocked_channels_by_promo']\n", "            # Convert inner values to appropriate data structures if needed\n", "            for key, value in model_data['adstocked_channels_by_promo'].items():\n", "                if isinstance(value, list):\n", "                    # Check if this looks like DataFrame data\n", "                    if value and isinstance(value[0], dict) and all(isinstance(item, dict) for item in value):\n", "                        model_data['adstocked_channels_by_promo'][key] = pd.DataFrame(value)\n", "        \n", "        # Convert adstock_range_channel back to dict\n", "        if result['adstock_range_channel']:\n", "            model_data['adstock_range_channel'] = result['adstock_range_channel']\n", "        \n", "        # Convert parms back to dict\n", "        if result['parms']:\n", "            model_data['parms'] = result['parms']\n", "            \n", "            # Convert date strings back to datetime objects if needed\n", "            for key, value in model_data['parms'].items():\n", "                if isinstance(value, str) and key.lower().endswith('date'):\n", "                    try:\n", "                        model_data['parms'][key] = pd.to_datetime(value)\n", "                    except:\n", "                        # Keep as string if conversion fails\n", "                        pass\n", "        \n", "        # Convert model_results back to list of DataFrames\n", "        if result['model_results']:\n", "            if isinstance(result['model_results'], list):\n", "                # If it's a list of records that look like DataFrames\n", "                model_results = []\n", "                for item in result['model_results']:\n", "                    if isinstance(item, list):\n", "                        model_results.append(pd.DataFrame(item))\n", "                    elif isinstance(item, dict):\n", "                        # Handle different storage formats\n", "                        model_results.append(pd.DataFrame.from_dict(item))\n", "                model_data['model_results'] = model_results\n", "            else:\n", "                # If it's stored in a different format\n", "                model_data['model_results'] = result['model_results']\n", "        \n", "        # Convert best_features back to list\n", "        if result['best_features']:\n", "            model_data['best_features'] = result['best_features']\n", "            # Convert to appropriate format if needed\n", "            if isinstance(model_data['best_features'], dict) and 'series' in model_data['best_features']:\n", "                try:\n", "                    model_data['best_features'] = pd.Series(model_data['best_features']['series'])\n", "                except:\n", "                    pass\n", "        \n", "        # Convert all_features back to list\n", "        if result['all_features']:\n", "            model_data['all_features'] = result['all_features']\n", "            # Convert nested structures if needed\n", "            if isinstance(model_data['all_features'], list):\n", "                # If it's a list of feature sets, each potentially being a DataFrame\n", "                for i, feature_set in enumerate(model_data['all_features']):\n", "                    if isinstance(feature_set, list) and feature_set and isinstance(feature_set[0], dict):\n", "                        model_data['all_features'][i] = pd.DataFrame(feature_set)\n", "        \n", "        return model_data\n", "    \n", "    except Exception as e:\n", "        logging.error(f\"Error extracting data from database: {e}\")\n", "        return None\n", "    finally:\n", "        # Close the cursor if it exists\n", "        if 'cursor' in locals() and cursor is not None:\n", "            cursor.close()\n", "\n", "# Example usage:\n", "# conn = psycopg2.connect(\"dbname=mydb user=user password=pass host=localhost\")\n", "# model_data = extract_model_data(conn, \"123e4567-e89b-12d3-a456-426614174000\")\n", "# \n", "# # Access all data including standard columns\n", "# date_column = model_data['date_column']\n", "# id_column = model_data['id_column']\n", "# target_column = model_data['target_column']\n", "# promotional_columns = model_data['promotional_columns']\n", "# start_date = model_data['start_date']\n", "# end_date = model_data['end_date']\n", "# \n", "# # Access complex columns\n", "# best_result_table = model_data['best_result_table']\n", "# best_features = model_data['best_features']\n", "# all_features = model_data['all_features']"]}, {"cell_type": "markdown", "id": "17943e80", "metadata": {}, "source": ["# Updating channels"]}, {"cell_type": "code", "execution_count": 20, "id": "f20b9eea", "metadata": {}, "outputs": [], "source": ["def update_multiple_channel_transformations(df,id_column,best_features, channel_updates):\n", "    \"\"\"\n", "    Update multiple channel transformations and return updated feature list for regression.\n", "    \n", "    Args:\n", "        best_result_table (pd.DataFrame): The current best result table\n", "        channel_updates (list): List of (base_channel, new_adstock_rate, new_transform_name) tuples\n", "    \n", "    Returns:\n", "        list: List of features for perform_regression, with updated transformed features\n", "    \"\"\"\n", "    \n", "    # Create a copy of the best_features to update\n", "    updated_features = best_features.copy()\n", "    \n", "    print(f\"Starting multiple channel updates. Initial features: {len(best_features)}\")\n", "    \n", "    for base_channel, new_adstock_rate, new_transform_name in channel_updates:\n", "        # Identify the current feature used for this base channel in updated_features\n", "        current_feature = None\n", "        for feature in updated_features:\n", "            if feature.startswith(f\"{base_channel}_Adstock_\"):\n", "                current_feature = feature\n", "                break\n", "        \n", "        if current_feature is None:\n", "            print(f\"Warning: No feature for {base_channel} found in features list. Skipping.\")\n", "            continue\n", "        \n", "        print(f\"\\nUpdating channel: {base_channel}\")\n", "        print(f\"Current feature: {current_feature}\")\n", "        \n", "        # Create the new channel name\n", "        new_feature_name = f\"{base_channel}_Adstock_{new_adstock_rate}_{new_transform_name}\"\n", "        print(f\"New feature name will be: {new_feature_name}\")\n", "        \n", "        # Check if the base channel exists in the dataframe\n", "        if base_channel not in df.columns:\n", "            print(f\"Warning: Base channel {base_channel} not found in dataframe. Skipping.\")\n", "            continue\n", "        \n", "        # Apply new adstock\n", "        print(f\"Applying adstock with rate {new_adstock_rate}...\")\n", "        try:\n", "            adstock_col, adstocked_series = apply_adstock(\n", "                df, \n", "                base_channel, \n", "                id_column,  # Assuming id_column is globally available\n", "                new_adstock_rate\n", "            )\n", "            print(f\"Created adstock column: {adstock_col}\")\n", "            \n", "            # Apply new transformation\n", "            transform_funcs = get_transformation_functions()\n", "            if new_transform_name not in transform_funcs:\n", "                print(f\"Warning: Transformation {new_transform_name} not found. Skipping.\")\n", "                continue\n", "            \n", "            print(f\"Applying transformation: {new_transform_name}...\")\n", "            transform_func = transform_funcs[new_transform_name]\n", "            transformed_col = new_feature_name\n", "            _, transformed_series = apply_transformation(\n", "                adstocked_series,\n", "                new_transform_name,\n", "                transform_func,\n", "                adstock_col\n", "            )\n", "            print(f\"Created transformed column: {transformed_col}\")\n", "            \n", "            # Add the transformed data to the dataframe\n", "            df[transformed_col] = transformed_series\n", "            print(f\"Added {transformed_col} to dataframe\")\n", "            \n", "            # Update feature list by replacing the current feature with the new one\n", "            feature_index = updated_features.index(current_feature)\n", "            updated_features[feature_index] = new_feature_name\n", "            print(f\"Updated features: Replaced {current_feature} with {new_feature_name}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error updating {base_channel}: {e}\")\n", "            print(f\"Skipping this channel and continuing with others.\")\n", "    \n", "    print(f\"\\nMultiple channel updates complete. Total features in updated list: {len(updated_features)}\")\n", "    return df,updated_features"]}, {"cell_type": "code", "execution_count": null, "id": "467fc259", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b8c67f8a", "metadata": {}, "source": ["# process main file"]}, {"cell_type": "code", "execution_count": 21, "id": "ed4d0f63", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# def process_and_filter_main_data(file_path, promo_channels, date_column, id_column, target_column, start_date, end_date, date_format):\n", "#     \"\"\"\n", "#     Clean and process the main data and update the PostgreSQL database directly.\n", "    \n", "#     Args:\n", "#         file_path (str): Path to the input file\n", "#         promo_channels (list): List of promotional channel columns\n", "#         date_column (str): Name of the date column\n", "#         id_column (str): Name of the ID column\n", "#         target_column (str): Name of the target column\n", "#         start_date (datetime.date): Start date for filtering\n", "#         end_date (datetime.date): End date for filtering\n", "#         date_format (str): Format of dates in the original data\n", "        \n", "#     Returns:\n", "#         pandas.DataFrame: Processed main dataframe\n", "#     \"\"\"\n", "\n", "#     import pandas as pd\n", "#     import numpy as np\n", "#     import logging\n", "    \n", "#     try:\n", "#         # Load the data\n", "#         df = load_file(file_path)\n", "#         df['original_date'] = df[date_column].copy()\n", "        \n", "#         # Convert dates\n", "#         df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))\n", "#         print(type(date_column))  # This will print <class 'str'>\n", "\n", "#         # Print debugging info about the conversion success\n", "#         na_count = df[date_column].isna().sum()\n", "#         total_rows = len(df)\n", "#         print(f\"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted\")\n", "\n", "#         if na_count > 0:\n", "#             # Show examples of problematic values\n", "#             problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]\n", "#             print(f\"Examples of problematic date values: {problem_examples}\")\n", "\n", "#         # Drop the empty dates\n", "#         orig_len = len(df)\n", "#         df = df.dropna(subset=[date_column])\n", "#         print(f\"Dropped {orig_len - len(df)} rows with invalid dates\")\n", "\n", "#         # Drop the debug column\n", "#         df = df.drop(columns=['original_date'])\n", "        \n", "#         # Ensure numeric columns\n", "#         numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()\n", "#         for col in numeric_columns:\n", "#             df[col] = pd.to_numeric(df[col], errors='coerce')\n", "#         df = df.drop_duplicates()\n", "#         logging.info(\"Data successfully loaded and cleaned.\")\n", "        \n", "#         # Create lag variable\n", "#         df = df.sort_values(by=[id_column, date_column])\n", "#         df[f\"{target_column}_lag_1\"] = df.groupby(id_column)[target_column].shift(1).fillna(0)\n", "        \n", "#         # Handle outliers\n", "#         df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)\n", "\n", "#         # Convert start_date and end_date to pandas datetime64 for correct comparison\n", "        \n", "        \n", "#         # Filter the dataframe by date range\n", "#         df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "        \n", "#         print(f\"Type of df[date_column]: {type(df[date_column].iloc[0]) if not df.empty else 'No data'}\")\n", "        \n", "        \n", "#         return df\n", "    \n", "#     except Exception as e:\n", "#         logging.error(f\"Error processing main data: {str(e)}\")\n", "#         raise\n", "\n", "\n", "def process_and_filter_main_data(file_path, promo_channels, date_column, id_column, target_column, \n", "                        start_date, end_date, date_format, best_features=None):\n", "    \"\"\"\n", "    Clean and process the main data and update the PostgreSQL database directly.\n", "    Also creates all necessary transformed features specified in best_features.\n", "    \n", "    Args:\n", "        file_path (str): Path to the input file\n", "        promo_channels (list): List of promotional channel columns\n", "        date_column (str): Name of the date column\n", "        id_column (str): Name of the ID column\n", "        target_column (str): Name of the target column\n", "        start_date (datetime.date): Start date for filtering\n", "        end_date (datetime.date): End date for filtering\n", "        date_format (str): Format of dates in the original data\n", "        best_features (list, optional): List of features needed for the model. \n", "                                        If provided, ensures all these features are created.\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed main dataframe with all required features\n", "    \"\"\"\n", "    import pandas as pd\n", "    import numpy as np\n", "    import logging\n", "    import re\n", "    \n", "    try:\n", "        # Load the data\n", "        df = load_file(file_path)\n", "        df['original_date'] = df[date_column].copy()\n", "        \n", "        # Convert dates\n", "        df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))\n", "        print(type(date_column))  # This will print <class 'str'>\n", "\n", "        # Print debugging info about the conversion success\n", "        na_count = df[date_column].isna().sum()\n", "        total_rows = len(df)\n", "        print(f\"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted\")\n", "\n", "        if na_count > 0:\n", "            # Show examples of problematic values\n", "            problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]\n", "            print(f\"Examples of problematic date values: {problem_examples}\")\n", "\n", "        # Drop the empty dates\n", "        orig_len = len(df)\n", "        df = df.dropna(subset=[date_column])\n", "        print(f\"Dropped {orig_len - len(df)} rows with invalid dates\")\n", "\n", "        # Drop the debug column\n", "        df = df.drop(columns=['original_date'])\n", "        \n", "        # Ensure numeric columns\n", "        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()\n", "        for col in numeric_columns:\n", "            df[col] = pd.to_numeric(df[col], errors='coerce')\n", "        df = df.drop_duplicates()\n", "        logging.info(\"Data successfully loaded and cleaned.\")\n", "        \n", "        # Create lag variable\n", "        df = df.sort_values(by=[id_column, date_column])\n", "        df[f\"{target_column}_lag_1\"] = df.groupby(id_column)[target_column].shift(1).fillna(0)\n", "        \n", "        # Handle outliers\n", "        df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)\n", "        \n", "        # Filter the dataframe by date range\n", "        df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "        \n", "        print(f\"Type of df[date_column]: {type(df[date_column].iloc[0]) if not df.empty else 'No data'}\")\n", "        \n", "        # Create all necessary transformed features if best_features is provided\n", "        if best_features:\n", "            print(f\"Ensuring all required features are created: {best_features}\")\n", "            # Regular expression pattern to extract base channel, adstock rate, and transformation\n", "            pattern = r'([A-Za-z0-9_]+)_Adstock_(\\d+)_([A-Za-z0-9]+)'\n", "            \n", "            for feature in best_features:\n", "                # Skip features that are already there or don't match the pattern\n", "                if feature in df.columns or not re.match(pattern, feature):\n", "                    continue\n", "                \n", "                # Extract components\n", "                match = re.match(pattern, feature)\n", "                if match:\n", "                    base_channel, adstock_rate, transform_name = match.groups()\n", "                    adstock_rate = int(adstock_rate)\n", "                    \n", "                    print(f\"Creating feature: {feature} from base channel {base_channel}\")\n", "                    \n", "                    # Make sure base channel exists\n", "                    if base_channel not in df.columns:\n", "                        print(f\"Warning: Base channel {base_channel} not found in dataframe. Skipping.\")\n", "                        continue\n", "                    \n", "                    # Apply adstock\n", "                    try:\n", "                        adstock_col, adstocked_series = apply_adstock(\n", "                            df, \n", "                            base_channel, \n", "                            id_column,\n", "                            adstock_rate\n", "                        )\n", "                        \n", "                        # Apply transformation\n", "                        transform_funcs = get_transformation_functions()\n", "                        if transform_name not in transform_funcs:\n", "                            print(f\"Warning: Transformation {transform_name} not found. Skipping.\")\n", "                            continue\n", "                        \n", "                        transform_func = transform_funcs[transform_name]\n", "                        _, transformed_series = apply_transformation(\n", "                            adstocked_series,\n", "                            transform_name,\n", "                            transform_func,\n", "                            adstock_col\n", "                        )\n", "                        \n", "                        # Add the transformed data to the dataframe\n", "                        df[feature] = transformed_series\n", "                        print(f\"Successfully created feature: {feature}\")\n", "                        \n", "                    except Exception as e:\n", "                        print(f\"Error creating {feature}: {e}\")\n", "            \n", "        return df\n", "    \n", "    except Exception as e:\n", "        logging.error(f\"Error processing main data: {str(e)}\")\n", "        raise"]}, {"cell_type": "markdown", "id": "c4721e58", "metadata": {}, "source": ["# update change sto database after the user satisfaction"]}, {"cell_type": "code", "execution_count": 22, "id": "70b65a5a", "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import json\n", "from psycopg2.extras import <PERSON>son\n", "import pandas as pd\n", "import datetime\n", "import logging\n", "\n", "def update_model_specific_fields(conn, model_run_id, best_result_table=None, all_regression_results=None, \n", "                                best_features=None, all_feature_sets=None):\n", "    \"\"\"\n", "    Update specific JSONB fields in the model_data table for a specific model_run_id.\n", "    Only updates the fields that are provided (not None).\n", "    \n", "    Parameters:\n", "    conn: PostgreSQL connection object\n", "    model_run_id: UUID of the model run to update\n", "    best_result_table: DataFrame (will be converted to JSON)\n", "    all_regression_results: List of regression results\n", "    best_features: List of best features\n", "    all_feature_sets: List of all feature sets (will be stored as all_features in database)\n", "    \n", "    Returns:\n", "    bool: True if update was successful, False otherwise\n", "    \"\"\"\n", "    if not conn or conn.closed:\n", "        logging.error(\"Invalid database connection\")\n", "        return False\n", "    \n", "    # Helper function to safely convert pandas DataFrames to serializable dictionaries\n", "    def convert_pandas_to_dict(obj):\n", "        if isinstance(obj, pd.DataFrame):\n", "            # Convert DataFrame to dict with records orientation and convert datetime columns to ISO format\n", "            for col in obj.select_dtypes(include=['datetime64']).columns:\n", "                obj[col] = obj[col].astype(str)\n", "            return json.loads(obj.to_json(orient='records', date_format='iso'))\n", "        elif isinstance(obj, pd.Series):\n", "            # Handle datetime Series\n", "            if pd.api.types.is_datetime64_any_dtype(obj):\n", "                obj = obj.astype(str)\n", "            return json.loads(obj.to_json())\n", "        elif isinstance(obj, dict):\n", "            # Handle dictionaries - recursively process all values\n", "            return {k: convert_pandas_to_dict(v) for k, v in obj.items()}\n", "        elif isinstance(obj, list):\n", "            # Handle lists - recursively process all items\n", "            return [convert_pandas_to_dict(item) for item in obj]\n", "        elif isinstance(obj, (datetime.datetime, datetime.date)):\n", "            # Convert datetime objects to ISO format string\n", "            return obj.isoformat()\n", "        else:\n", "            return obj\n", "    \n", "    try:\n", "        # Build dynamic SQL query based on provided parameters\n", "        update_fields = []\n", "        params = []\n", "        \n", "        # Process best_result_table if provided\n", "        if best_result_table is not None:\n", "            serialized_best_result_table = convert_pandas_to_dict(best_result_table)\n", "            update_fields.append(\"best_result_table = %s\")\n", "            params.append(Json(serialized_best_result_table))\n", "        \n", "        # Process all_regression_results if provided\n", "        if all_regression_results is not None:\n", "            serialized_regression_results = convert_pandas_to_dict(all_regression_results)\n", "            update_fields.append(\"model_results = %s\")\n", "            params.append(Json(serialized_regression_results))\n", "        \n", "        # Process best_features if provided\n", "        if best_features is not None:\n", "            serialized_best_features = convert_pandas_to_dict(best_features)\n", "            update_fields.append(\"best_features = %s\")\n", "            params.append(Json(serialized_best_features))\n", "        \n", "        # Process all_feature_sets if provided (stored as all_features in database)\n", "        if all_feature_sets is not None:\n", "            serialized_all_feature_sets = convert_pandas_to_dict(all_feature_sets)\n", "            update_fields.append(\"all_features = %s\")\n", "            params.append(Json(serialized_all_feature_sets))\n", "        \n", "        # Add updated_at timestamp\n", "        update_fields.append(\"updated_at = CURRENT_TIMESTAMP\")\n", "        \n", "        # If no fields to update, return early\n", "        if not update_fields:\n", "            logging.warning(\"No fields provided for update\")\n", "            return False\n", "        \n", "        # Create a cursor\n", "        cursor = conn.cursor()\n", "        \n", "        # Build the SQL query\n", "        update_query = f\"\"\"\n", "        UPDATE model_data\n", "        SET {', '.join(update_fields)}\n", "        WHERE model_run_id = %s\n", "        \"\"\"\n", "        \n", "        # Add the model_run_id to params\n", "        params.append(model_run_id)\n", "        \n", "        # Execute the query with parameters\n", "        cursor.execute(update_query, params)\n", "        \n", "        # Commit the transaction\n", "        conn.commit()\n", "        \n", "        # Check if any rows were updated\n", "        rows_updated = cursor.rowcount\n", "        if rows_updated > 0:\n", "            print(f\"Successfully updated {rows_updated} row(s) for model_run_id: {model_run_id}\")\n", "            return True\n", "        else:\n", "            print(f\"No rows were updated for model_run_id: {model_run_id}\")\n", "            return False\n", "    \n", "    except Exception as e:\n", "        print(f\"Error updating database: {e}\")\n", "        if conn:\n", "            conn.rollback()\n", "        return False\n", "    \n", "    finally:\n", "        # Close the cursor if it exists\n", "        if 'cursor' in locals() and cursor is not None:\n", "            cursor.close()\n", "\n"]}, {"cell_type": "markdown", "id": "8469ff1e", "metadata": {}, "source": ["# main function to  update best result table"]}, {"cell_type": "code", "execution_count": 23, "id": "6fc33dfe", "metadata": {}, "outputs": [], "source": ["# def update_channels():\n", "    \n", "#     db_conn = initialize_db_connection()\n", "#     model_run_id=\"00000000-0000-0000-0000-000000000001\"\n", "\n", "#     model_data = extract_model_data(conn, model_run_id)\n", "    \n", "#     all_features=model_data['all_features']\n", "#     best_result_table = model_data['best_result_table']\n", "#     best_features = model_data['best_features']\n", "#     all_regression_results=model_data['model_results']\n", "#     date_column = model_data['date_column']\n", "#     id_column = model_data['id_column']\n", "#     target_column = model_data['target_column']\n", "#     promotional_columns = model_data['promotional_columns']\n", "#     start_date = model_data['start_date']\n", "#     end_date = model_data['end_date']\n", "#     date_format = model_data['date_format']\n", "\n", "#     start_date = pd.to_datetime(start_date)\n", "#     end_date = pd.to_datetime(end_date)\n", "#     file_path='../dummy1.xlsx'\n", "\n", "#     df=process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date,date_format)\n", "    \n", "#     channel_updates = [\n", "#         ('PDE', 10, 'Log'),\n", "#         ('<PERSON><PERSON>', 30, 'Root8')\n", "#     ]\n", "\n", "#     df,updated_features = update_multiple_channel_transformations(\n", "#                         df,id_column,best_features,channel_updates\n", "#                     )\n", "                    \n", "#                     # Run regression again with the updated features\n", "#     print(\"\\nRunning regression with updated channel transformations...\")\n", "                    \n", "#                     # Update best_features with the new feature list\n", "#     best_features = updated_features\n", "#     print(f\"best features: {best_features}\")\n", "    \n", "#     all_features[0]=best_features\n", "                    \n", "#                     # Perform regression with updated feature set\n", "#     updated_result_table = perform_regression(\n", "#                         df, \n", "#                         updated_features, \n", "#                         target_column, \n", "#                         date_column, \n", "#                         start_date, \n", "#                         end_date\n", "#                     )\n", "                    \n", "#                     # Update best_result_table with the new results\n", "#     if isinstance(updated_result_table, pd.DataFrame) and not updated_result_table.empty:\n", "#         best_result_table = updated_result_table\n", "                        \n", "#                         # Also update in all_regression_results if it exists\n", "#     if all_regression_results and len(all_regression_results) > 0:\n", "#         all_regression_results[0] = best_result_table\n", "    \n", "#     print(f\"best result table: {best_result_table}\")\n", "    \n", "#     update_successful = update_model_specific_fields(\n", "#         db_conn,\n", "#         model_run_id,\n", "#         best_result_table=best_result_table,\n", "#         all_regression_results=all_regression_results,\n", "#         best_features=best_features,\n", "#         all_feature_sets=all_features\n", "#     )\n", "    \n", "#     close_db_connection(db_conn)"]}, {"cell_type": "code", "execution_count": 24, "id": "3925b7b8", "metadata": {}, "outputs": [], "source": ["def update_channels():\n", "    \n", "    db_conn = initialize_db_connection()\n", "    if db_conn is None:\n", "        print(\"Error: Invalid database connection\")\n", "        return False\n", "        \n", "    model_run_id = \"00000000-0000-0000-0000-000000000001\"\n", "\n", "    # Changed conn to db_conn here\n", "    model_data = extract_model_data(db_conn, model_run_id)\n", "    \n", "    all_features = model_data['all_features']\n", "    best_result_table = model_data['best_result_table']\n", "    best_features = model_data['best_features']\n", "    all_regression_results = model_data['model_results']\n", "    date_column = model_data['date_column']\n", "    id_column = model_data['id_column']\n", "    target_column = model_data['target_column']\n", "    promotional_columns = model_data['promotional_columns']\n", "    start_date = model_data['start_date']\n", "    end_date = model_data['end_date']\n", "    date_format = model_data['date_format']\n", "    unit_price=float(model_data['unit_price'])\n", "    all_transformed_features=model_data['all_transformed_features']\n", "    spend_column=model_data['spend_column']\n", "    spend_channel_column=model_data['spend_channel_column']\n", "\n", "    start_date = pd.to_datetime(start_date)\n", "    end_date = pd.to_datetime(end_date)\n", "\n", "    # file_path = '../dummy1.xlsx'\n", "    file_path=\"MMix_data_for_testing.csv\"\n", "\n", "    spend_file_path=\"Spends_test.xlsx\"\n", "    spends_df=load_file(spend_file_path)\n", "\n", "\n", "\n", "    df = process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date, date_format,best_features)\n", "    \n", "    channel_updates = [\n", "        ('Organic_Engagement', 10, 'Log')\n", "    ]\n", "\n", "    df, updated_features = update_multiple_channel_transformations(\n", "        df, id_column, best_features, channel_updates\n", "    )\n", "                    \n", "    # Run regression again with the updated features\n", "    print(\"\\nRunning regression with updated channel transformations...\")\n", "                    \n", "    # Update best_features with the new feature list\n", "    best_features = updated_features\n", "    print(f\"best features: {best_features}\")\n", "    \n", "    all_features[0] = best_features\n", "                    \n", "    # Perform regression with updated feature set\n", "    updated_result_table = perform_regression(\n", "        df, \n", "        updated_features, \n", "        target_column, \n", "        date_column, \n", "        start_date, \n", "        end_date\n", "    )\n", "    updated_result_table['model'] = 'Best Model'\n", "    updated_result_table['model_rank'] = 1\n", "    \n", "    updated_result_table=add_roi_to_result_tables(\n", "                updated_result_table,\n", "                promotional_columns,\n", "                all_transformed_features,\n", "                spends_df,\n", "                spend_column,\n", "                spend_channel_column,\n", "                target_column,\n", "                unit_price\n", "            )\n", "    \n", "                    \n", "    # Update best_result_table with the new results\n", "    if isinstance(updated_result_table, pd.DataFrame) and not updated_result_table.empty:\n", "        best_result_table = updated_result_table\n", "                        \n", "        # Also update in all_regression_results if it exists\n", "        if all_regression_results and len(all_regression_results) > 0:\n", "            all_regression_results[0] = best_result_table\n", "    \n", "    print(f\"best result table: {best_result_table}\")\n", "    \n", "    update_successful = update_model_specific_fields(\n", "        db_conn,\n", "        model_run_id,\n", "        best_result_table=best_result_table,\n", "        all_regression_results=all_regression_results,\n", "        best_features=best_features,\n", "        all_feature_sets=all_features\n", "    )\n", "    \n", "    close_db_connection(db_conn)\n", "    return update_successful"]}, {"cell_type": "code", "execution_count": 140, "id": "5616c2cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'>\n", "Date conversion: 180000/180000 dates successfully converted\n", "Dropped 0 rows with invalid dates\n", "[Organic_Engagement] Skipping 6397 outliers - would cause 84.95% drop (>10%)\n", "[Staff_Engagement] Skipping 1859 outliers - would cause 97.95% drop (>10%)\n", "[Email_Engagement] Skipping 136 outliers - would cause 100.00% drop (>10%)\n", "[Digital_Engagement] Skipping 696 outliers - would cause 100.00% drop (>10%)\n", "[Lead_Engagement] Skipping 1820 outliers - would cause 100.00% drop (>10%)\n", "[Speaker_programs] Skipping 69 outliers - would cause 100.00% drop (>10%)\n", "[Doximity_views] Skipping 2848 outliers - would cause 62.20% drop (>10%)\n", "[ReachMD] Skipping 8472 outliers - would cause 100.00% drop (>10%)\n", "[CRM_Engagement_Count] Skipping 476 outliers - would cause 93.68% drop (>10%)\n", "[Event_Attendance] Skipping 156 outliers - would cause 100.00% drop (>10%)\n", "Total outliers removed: 0\n", "Remaining rows: 180000\n", "Outlier summary:\n", "                 channel        before         after  Percentage drop\n", "0     Organic_Engagement   8530.000000   8530.000000              0.0\n", "1       Staff_Engagement   2091.100000   2091.100000              0.0\n", "2       Email_Engagement    514.000000    514.000000              0.0\n", "3     Digital_Engagement    631.500000    631.500000              0.0\n", "4        Lead_Engagement   1529.900000   1529.900000              0.0\n", "5       Speaker_programs     69.000000     69.000000              0.0\n", "6         Doximity_views  61456.000000  61456.000000              0.0\n", "7                ReachMD   8472.000000   8472.000000              0.0\n", "8   CRM_Engagement_Count   3086.000000   3086.000000              0.0\n", "9       Event_Attendance    156.000000    156.000000              0.0\n", "10                 Units   5127.533333   5127.533333              0.0\n", "Type of df[date_column]: <class 'pandas._libs.tslibs.timestamps.Timestamp'>\n", "Ensuring all required features are created: ['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root9', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_80_Root9', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_90_Root4', 'Event_Attendance_Adstock_90_Root9']\n", "Creating feature: Organic_Engagement_Adstock_50_Root9 from base channel Organic_Engagement\n", "Successfully created feature: Organic_Engagement_Adstock_50_Root9\n", "Creating feature: Staff_Engagement_Adstock_90_Root9 from base channel Staff_Engagement\n", "Successfully created feature: Staff_Engagement_Adstock_90_Root9\n", "Creating feature: Email_Engagement_Adstock_20_Root8 from base channel Email_Engagement\n", "Successfully created feature: Email_Engagement_Adstock_20_Root8\n", "Creating feature: Digital_Engagement_Adstock_90_Root9 from base channel Digital_Engagement\n", "Successfully created feature: Digital_Engagement_Adstock_90_Root9\n", "Creating feature: Lead_Engagement_Adstock_80_Root9 from base channel Lead_Engagement\n", "Successfully created feature: Lead_Engagement_Adstock_80_Root9\n", "Creating feature: Doximity_views_Adstock_50_Root4 from base channel Doximity_views\n", "Successfully created feature: Doximity_views_Adstock_50_Root4\n", "Creating feature: ReachMD_Adstock_90_Root4 from base channel ReachMD\n", "Successfully created feature: ReachMD_Adstock_90_Root4\n", "Creating feature: Event_Attendance_Adstock_90_Root9 from base channel Event_Attendance\n", "Successfully created feature: Event_Attendance_Adstock_90_Root9\n", "Starting multiple channel updates. Initial features: 9\n", "\n", "Updating channel: Organic_Engagement\n", "Current feature: Organic_Engagement_Adstock_50_Root9\n", "New feature name will be: Organic_Engagement_Adstock_10_Log\n", "Applying adstock with rate 10...\n", "Created adstock column: Organic_Engagement_Adstock_10\n", "Applying transformation: Log...\n", "Created transformed column: Organic_Engagement_Adstock_10_Log\n", "Added Organic_Engagement_Adstock_10_Log to dataframe\n", "Updated features: Replaced Organic_Engagement_Adstock_50_Root9 with Organic_Engagement_Adstock_10_Log\n", "\n", "Multiple channel updates complete. Total features in updated list: 9\n", "\n", "Running regression with updated channel transformations...\n", "best features: ['Units_lag_1', 'Organic_Engagement_Adstock_10_Log', 'Staff_Engagement_Adstock_90_Root9', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_80_Root9', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_90_Root4', 'Event_Attendance_Adstock_90_Root9']\n", "best result table:                                Channel  Estimate  Impact Percentage  \\\n", "0                          Units_lag_1  0.882063          86.783058   \n", "1    Organic_Engagement_Adstock_10_Log  0.026441           3.330037   \n", "2    Staff_Engagement_Adstock_90_Root9  0.010652           0.725258   \n", "3    Email_Engagement_Adstock_20_Root8  0.022135           0.187875   \n", "4  Digital_Engagement_Adstock_90_Root9  0.104468           6.593438   \n", "5     Lead_Engagement_Adstock_80_Root9  0.051445           5.680399   \n", "6      Doximity_views_Adstock_50_Root4  0.001645           1.403315   \n", "7             ReachMD_Adstock_90_Root4  0.001395           0.733356   \n", "8    Event_Attendance_Adstock_90_Root9  0.022115           0.410052   \n", "9                            Intercept -0.001679          -5.846788   \n", "\n", "         P-Value  Effectiveness  Linear Activity        R²  Adjusted R²  \\\n", "0   0.000000e+00    3039.442233      3445.833333  0.793921     0.793906   \n", "1   4.909730e-19     145.302437      5495.400000  0.793921     0.793906   \n", "2   3.653100e-04       4.536593       425.900000  0.793921     0.793906   \n", "3   1.735598e-09       7.304432       330.000000  0.793921     0.793906   \n", "4  7.065465e-193      44.106238       422.200000  0.793921     0.793906   \n", "5   2.826919e-75      53.368862      1037.400000  0.793921     0.793906   \n", "6   1.670771e-02      69.773206     42426.000000  0.793921     0.793906   \n", "7   1.777874e-01       6.846009      4908.000000  0.793921     0.793906   \n", "8   6.676124e-04       3.450001       156.000000  0.793921     0.793906   \n", "9   8.443012e-04            NaN              NaN  0.793921     0.793906   \n", "\n", "             AIC      RMSE  Total Modeled Activity  Modeled Sales  \\\n", "0 -121426.013621  0.145881             3390.233333    3445.833333   \n", "1 -121426.013621  0.145881             4339.800244    3445.833333   \n", "2 -121426.013621  0.145881             2346.198924    3445.833333   \n", "3 -121426.013621  0.145881              292.476815    3445.833333   \n", "4 -121426.013621  0.145881             2174.825472    3445.833333   \n", "5 -121426.013621  0.145881             3804.795965    3445.833333   \n", "6 -121426.013621  0.145881            29403.090313    3445.833333   \n", "7 -121426.013621  0.145881            18116.579783    3445.833333   \n", "8 -121426.013621  0.145881              638.907892    3445.833333   \n", "9 -121426.013621  0.145881                     NaN    3445.833333   \n", "\n", "   Actual Sales  Channel_count significance       model  model_rank        ROI  \n", "0   3445.833333              9         True  Best Model           1        NaN  \n", "1   3445.833333              9         True  Best Model           1   9.562294  \n", "2   3445.833333              9         True  Best Model           1   1.388400  \n", "3   3445.833333              9         True  Best Model           1   0.431591  \n", "4   3445.833333              9         True  Best Model           1  10.327222  \n", "5   3445.833333              9         True  Best Model           1  13.981219  \n", "6   3445.833333              9         True  Best Model           1   6.044489  \n", "7   3445.833333              9        False  Best Model           1   2.527023  \n", "8   3445.833333              9         True  Best Model           1   0.565188  \n", "9   3445.833333              9            0  Best Model           1        NaN  \n", "Successfully updated 1 row(s) for model_run_id: 00000000-0000-0000-0000-000000000001\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["update_channels()"]}, {"cell_type": "code", "execution_count": 25, "id": "b386b3f1", "metadata": {}, "outputs": [], "source": ["def result_extraction():\n", "    db_conn=initialize_db_connection()\n", "    model_run_id=\"00000000-0000-0000-0000-000000000001\"\n", "\n", "\n", "    # load features from the database\n", "    model_data = extract_model_data(db_conn, model_run_id)\n", "    all_transformed_features=model_data['all_transformed_features']\n", "    transformed_channels_by_promo=model_data['transformed_channels_by_promo']\n", "    adstocked_channels_by_promo=model_data['adstocked_channels_by_promo']\n", "    adstock_range_channel=model_data['adstock_range_channel']\n", "    all_features=model_data['all_features']\n", "    best_result_table = model_data['best_result_table']\n", "    best_features = model_data['best_features']\n", "    all_regression_results=model_data['model_results']\n", "    date_column = model_data['date_column']\n", "    id_column = model_data['id_column']\n", "    target_column = model_data['target_column']\n", "    promotional_columns = model_data['promotional_columns']\n", "    start_date = model_data['start_date']\n", "    end_date = model_data['end_date']\n", "    date_format = model_data['date_format']\n", "    spend_channel_column = model_data['spend_channel_column']\n", "    spend_column = model_data['spend_column']\n", "    historical_channel_column = model_data['historical_channel_column']\n", "    historical_impact = model_data['historical_impact']\n", "    unit_price = model_data['unit_price']\n", "    params=model_data['parms']\n", "\n", "    start_date = pd.to_datetime(start_date)\n", "    end_date = pd.to_datetime(end_date)\n", "    \n", "    \n", "    \n", "    return target_column, date_column, id_column, start_date, end_date, spend_channel_column,spend_column,historical_channel_column,historical_impact,unit_price,best_result_table,all_regression_results,transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_features,all_transformed_features,promotional_columns,best_features,date_format,params\n", "   "]}, {"cell_type": "code", "execution_count": null, "id": "57c84a70", "metadata": {}, "outputs": [], "source": ["def execute_code_with_retries(code_to_execute, local_vars, max_attempts=6):\n", "    \"\"\"\n", "    Attempts to execute the given code multiple times until success or max attempts reached.\n", "    Now properly handles matplotlib figures.\n", "    \n", "    Args:\n", "        code_to_execute: String containing Python code to execute\n", "        local_vars: Dictionary of local variables for execution  =\n", "        \n", "        max_attempts: Maximum number of attempts to try executing the code\n", "        \n", "    Returns:\n", "        tuple: (success flag, output text, matplotlib figure or None, error message if any)\n", "    \"\"\"\n", "    import matplotlib.pyplot as plt\n", "    import io\n", "    from contextlib import redirect_stdout\n", "    import traceback\n", "    \n", "    for attempt in range(1, max_attempts + 1):\n", "        print(f\"\\nAttempt {attempt}/{max_attempts}...\")\n", "        \n", "        # Clear any existing figures before execution\n", "        plt.close('all')\n", "        \n", "        # Create a string buffer to capture output\n", "        output_buffer = io.StringIO()\n", "        error_message = None\n", "        success = False\n", "        figure = None\n", "        \n", "        # Make a copy of the local variables to prevent pollution between attempts\n", "        local_vars_copy = {k: v for k, v in local_vars.items()}\n", "        \n", "        try:\n", "            # Redirect stdout to capture print statements\n", "            with redirect_stdout(output_buffer):\n", "                # Execute the code\n", "                exec(code_to_execute, globals(), local_vars_copy)\n", "            \n", "            # CRITICAL FIX: Check for figures AFTER execution\n", "            if plt.get_fignums():\n", "                # Get the current figure (last created figure)\n", "                figure = plt.gcf()\n", "                print(f\"Figure created with {len(plt.get_fignums())} total figures\")\n", "                \n", "                # IMPORTANT: Don't close the figure here - we need to return it\n", "                # The figure will be closed by the calling function after saving\n", "            else:\n", "                print(\"No matplotlib figures were created\")\n", "            \n", "            # Check if there's any output and if it contains error messages\n", "            output_text = output_buffer.getvalue()\n", "            if \"An error occurred:\" in output_text or \"Error:\" in output_text:\n", "                error_message = output_text\n", "                raise Exception(f\"Execution produced an error: {output_text}\")\n", "            \n", "            # If we get here, execution was successful\n", "            success = True\n", "            return success, output_text, figure, None\n", "                \n", "        except Exception as e:\n", "            error_message = f\"Error: {str(e)}\\n{traceback.format_exc()}\"\n", "            # Clear any figures that might have been created during failed execution\n", "            plt.close('all')\n", "        \n", "        # If we're here, this attempt failed\n", "        if attempt < max_attempts:\n", "            print(f\"Attempt {attempt} failed: {error_message}\")\n", "            print(\"Trying again with a slightly modified approach...\")\n", "            \n", "            # For next attempt, you would regenerate code here (assuming model is available)\n", "            # This part would need to be implemented based on your Gemini model setup\n", "        else:\n", "            print(f\"All {max_attempts} attempts failed.\")\n", "            return False, \"\", None, error_message\n", "    \n", "    # If we reach here, all attempts failed\n", "    return False, \"\", None, \"Maximum attempts reached without success.\"\n", "\n", "\n", "def interactive_mmm_chatbot(user_question, df, target_column, date_column, promo_channels, all_transformed_features, best_result_table, params, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel, spends_df, spend_channel_column, spend_column, historical_df, historical_channel_column, historical_impact, unit_price):\n", "    \"\"\"\n", "    A non-interactive version of the MMM chatbot that processes a single user question\n", "    and returns the appropriate response (text, image, or both).\n", "    \"\"\"\n", "    import re\n", "    import pandas as pd\n", "    import matplotlib.pyplot as plt\n", "    import seaborn as sns\n", "    import numpy as np\n", "    import json\n", "    import logging\n", "    from datetime import datetime\n", "    import matplotlib.dates as mdates\n", "    from statsmodels.stats.outliers_influence import variance_inflation_factor\n", "    from matplotlib_venn import venn2\n", "    import sys\n", "    import io\n", "    from contextlib import redirect_stdout\n", "    import traceback\n", "    import base64\n", "    from io import BytesIO\n", "    \n", "    # Clear any existing figures at the start\n", "    plt.close('all')\n", "    \n", "    # Set matplotlib backend to Agg for non-interactive environments\n", "    plt.switch_backend('Agg')\n", "    \n", "    knowledge_base = \"\"\"\n", "      These are some of the functions that are commonly used in marketing mix to answer user questions\n", "    def month_on_month_summary(df: pd.DataFrame, date_column: str, target_column: str, promo_columns: list):\n", "\n", "        logging.info(\"Generating month-on-month summary visualization.\")\n", "        df_filtered=df.copy(deep=True)\n", "        # Convert date column to datetime format\n", "        df_filtered[date_column] = pd.to_datetime(df_filtered[date_column])\n", "\n", "        # Filter the DataFrame based on the date range\n", "        # df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "        df_grouped = df_filtered.groupby(date_column).sum()\n", "\n", "        plt.figure(figsize=(12, 6))\n", "\n", "        # Plot promotional columns as line plots\n", "        sns.lineplot(data=df_grouped[promo_columns], palette='tab10', linewidth=2)\n", "\n", "        # Plot target column as a filled area plot\n", "        plt.fill_between(df_grouped.index, df_grouped[target_column], color='gray', alpha=0.3, label=target_column)\n", "\n", "        # Format the x-axis with readable date formatting\n", "        plt.gca().xaxis.set_major_locator(mdates.MonthLocator())  # Show every month\n", "        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))  # Format as \"Month Year\"\n", "        plt.xticks(rotation=45)  # Rotate the labels for better readability\n", "\n", "        plt.title(\"Month-on-Month Summary\")\n", "        plt.xlabel(\"Time Period\")\n", "        plt.ylabel(\"Value\")\n", "        plt.legend()\n", "        plt.tight_layout()  # Adjust layout to fit rotated labels\n", "        plt.show(block=False)\n", "        plt.pause(0.1)\n", "\n", "    Correlation summary\n", "    def correlation_summary(df, promo_channels, target_column, date_column=None, start_date=None, end_date=None):\n", "  \n", "    import pandas as pd\n", "    import seaborn as sns\n", "    import matplotlib.pyplot as plt\n", "    import traceback\n", "    \n", "    try:\n", "        # Find which promo channels exist in the dataframe\n", "        available_promo_cols = []\n", "        for promo in promo_channels:\n", "            if promo in df.columns:\n", "                available_promo_cols.append(promo)\n", "        \n", "        print(f\"Found {len(available_promo_cols)} promotional channels in the dataframe: {available_promo_cols}\")\n", "        \n", "        if not available_promo_cols:\n", "            print(\"No promotional channels found in the DataFrame.\")\n", "            print(\"Available columns: \", df.columns.tolist())\n", "            return\n", "        \n", "        # Calculate correlation matrix with available promo columns and target\n", "        correlation_data = df[available_promo_cols + [target_column]].corr(method='pearson')\n", "        \n", "        # Plot the heatmap for correlations\n", "        plt.figure(figsize=(10, 6))\n", "        sns.heatmap(correlation_data, annot=True, cmap=\"coolwarm\", fmt=\".2f\", linewidths=0.5)\n", "        plt.title(f\"Correlation of Marketing Channels with {target_column}\")\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        print(\"Correlation heatmap displayed. Check for strong positive or negative correlations with the target variable.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"An error occurred during correlation analysis: {str(e)}\")\n", "        traceback.print_exc()\n", "    Aggregate channel summary\n", "    def aggregate_channel_summary(df: pd.DataFrame, id_column: str, promo_columns: list, target_column: str):\n", "\n", "        Compute total, min, max, avg values for each channel, along with reach and frequency.\n", "\n", "        logging.info(\"Computing aggregated channel summary.\")\n", "        summary = df[promo_columns + [target_column]].agg(['sum', 'min', 'max', 'mean'])\n", "\n", "        reach = {col: df[df[col] > 0][id_column].nunique() for col in promo_columns}\n", "        frequency = {col: summary.loc['sum', col] / reach[col] if reach[col] > 0 else 0 for col in promo_columns}\n", "\n", "        reach_df = pd.DataFrame.from_dict(reach, orient='index', columns=['Reach'])\n", "        frequency_df = pd.DataFrame.from_dict(frequency, orient='index', columns=['Frequency'])\n", "\n", "        summary = pd.concat([summary, reach_df.T, frequency_df.T])\n", "        summary= summary.reset_index()\n", "        summary.set_index('index', inplace=True)\n", "        print(\"Aggregated Channel Summary with Reach and Frequency:\")\n", "        print(summary)\n", "    \n", "    Tier Based summary\n", "    def tier_based_summary(df: pd.DataFrame, tier_column: str, promo_columns: list, target_column: str):\n", "\n", "        Compute total, min, max, avg values for each channel at the tier level, along with reach and frequency.\n", "\n", "        logging.info(\"Computing tier-based aggregated summary.\")\n", "        tier_summary = df.groupby(tier_column).agg({col: ['sum', 'min', 'max', 'mean'] for col in promo_columns + [target_column]})\n", "        tier_reach = df.groupby(tier_column)[promo_columns].apply(lambda x: x.gt(0).sum())\n", "\n", "        tier_reach.index = tier_reach.index.astype(str)  # Convert to string for consistency\n", "        tier_summary.index = tier_summary.index.astype(str)\n", "\n", "        tier_frequency = tier_summary.loc[:, (slice(None), 'sum')].div(tier_reach.replace(0, 1))\n", "        tier_frequency.columns = pd.MultiIndex.from_tuples([(col, 'frequency') for col in promo_columns])\n", "\n", "        tier_summary = pd.concat([tier_summary, tier_reach.add_suffix('_reach'), tier_frequency], axis=1)\n", "\n", "        print(\"Tier-Based Aggregated Channel Summary with Reach and Frequency:\")\n", "        print(tier_summary.reset_index())\n", "\n", "    Overlap_summary\n", "    def overlap_summary(df: pd.DataFrame, id_column: str, target_column: str, promo_columns: str, date_column: str, start_date: str, end_date: str):\n", "\n", "        Generate a Venn diagram and summary table showing overlap between sales and selected promotion at ID level.\n", "\n", "        logging.info(\"Generating overlap summary.\")\n", "        df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "        for promo_column in promo_columns:\n", "            df_filtered[promo_column] = df_filtered[promo_column].fillna(0)\n", "            df_grouped = df_filtered.groupby(id_column).agg({target_column: 'sum', promo_column: 'sum'}).reset_index()\n", "            df_grouped['promo_flag'] = df_grouped[promo_column] > 0\n", "            df_grouped['sales_flag'] = df_grouped[target_column] > 0\n", "\n", "            both = df_grouped[df_grouped['promo_flag'] & df_grouped['sales_flag']][id_column].nunique()\n", "            only_sales = df_grouped[~df_grouped['promo_flag'] & df_grouped['sales_flag']][id_column].nunique()\n", "            only_promo = df_grouped[df_grouped['promo_flag'] & ~df_grouped['sales_flag']][id_column].nunique()\n", "            neither = df_grouped[~df_grouped['promo_flag'] & ~df_grouped['sales_flag']][id_column].nunique()\n", "\n", "            plt.figure(figsize=(6, 6))\n", "            venn2(subsets=(only_sales, only_promo, both), set_labels=(\"Sales\", promo_column))\n", "            plt.title(\"Overlap Summary\")\n", "            plt.show(block=False)\n", "            plt.pause(0.1)\n", "\n", "\n", "            summary_table = pd.DataFrame({\n", "                \"Category\": [\"Sales & Promotion\", \"Sales Only\", \"Promotion Only\", \"Neither\"],\n", "                \"Unique IDs\": [both, only_sales, only_promo, neither],\n", "                \"Total Sales\": [df_grouped.loc[df_grouped['sales_flag'], target_column].sum()] * 4,\n", "                \"Total Promotional Volume\": [df_grouped.loc[df_grouped['promo_flag'], promo_column].sum()] * 4\n", "            })\n", "            print(\"Overlap Summary Table:\")\n", "            print(summary_table)\n", "\n", "    Box plot summaru generation\n", "    def box_plot_summary(df: pd.DataFrame, target_column: str, promo_columns: list, date_column: str, start_date: str, end_date: str):\n", "\n", "        Generate box plots for the target variable and promotional variables over time.\n", "\n", "        logging.info(\"Generating box plot summaries.\")\n", "        df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "\n", "        # selected_promo_columns = input(\"Enter promotional variable columns to include in box plots (comma-separated): \").split(',')\n", "\n", "        plt.figure(figsize=(12, 6))\n", "        sns.boxplot(x=date_column, y=target_column, data=df_filtered)\n", "        plt.xticks(rotation=45)\n", "        plt.title(f\"Box Plot of {target_column} Over Time\")\n", "        plt.show(block=False)\n", "        plt.pause(0.1)\n", "\n", "\n", "        for promo in promo_columns:\n", "            if promo in df_filtered.columns:\n", "                plt.figure(figsize=(12, 6))\n", "                sns.boxplot(x=date_column, y=promo, data=df_filtered)\n", "                plt.xticks(rotation=45)\n", "                plt.title(f\"Box Plot of {promo} Over Time\")\n", "                plt.show(block=False)\n", "                plt.pause(0.1)\n", "    \n", "                \n", "\n", "\n", "EXAMPLE code pattern for filtering out non-marketing variables\n", "# Example code pattern for filtering out non-marketing variables\n", "def get_marketing_variables_only(best_result_table, promo_channels):\n", "     marketing_rows = []\n", "    \n", "    for _, row in best_result_table.iterrows():\n", "        var_name = row.get('Channel', '')\n", "        # Check if this is a legitimate marketing variable\n", "        is_marketing_var = False\n", "        \n", "        # Check if the variable is derived from any promo channel\n", "        for channel in promo_channels:\n", "            if var_name.startswith(channel + '_') or var_name == channel:\n", "                is_marketing_var = True\n", "                break\n", "        \n", "        # Explicitly exclude non-marketing variables\n", "        if (var_name == 'Intercept' or \n", "            var_name.startswith('T') or \n", "            'time' in var_name.lower() or \n", "            'trend' in var_name.lower() or \n", "            'Rtime' in var_name or\n", "            'season' in var_name.lower() or\n", "            'month' in var_name.lower() or\n", "            'week' in var_name.lower() or\n", "            'quarter' in var_name.lower() or\n", "            '_lag_' in var_name.lower() or  # Exclude variables with '_lag_' pattern\n", "            (var_name.lower().endswith('lag') or  # Ends with 'lag'\n", "             any(var_name.lower().endswith(f'lag_{i}') for i in range(10)))):\n", "            is_marketing_var = False\n", "            \n", "        if is_marketing_var:\n", "            marketing_rows.append(row)\n", "    \n", "    return pd.DataFrame(marketing_rows)\n", "\n", "# Then use this when answering questions about top marketing drivers\n", "marketing_only_table = get_marketing_variables_only(best_result_table, promo_channels)\n", "top_n_drivers = marketing_only_table.sort_values('Impact Percentage', ascending=False).head(3)\n", "    \"\"\"\n", "\n", "    # Verify that Gemini API is available\n", "    try:\n", "        # import genai  # Assuming this is your Gemini import\n", "        model = genai.GenerativeModel(\"gemini-2.0-flash\")\n", "        print(\"Gemini API initialized successfully.\")\n", "    except Exception as e:\n", "        return {\n", "            'text_output': f\"Error: Could not initialize Gemini API: {e}. Please check your API key configuration.\",\n", "            'image': None,\n", "            'output_type': 'text'\n", "        }\n", "\n", "    # Ensure the date column is in datetime format\n", "    if df[date_column].dtype != 'datetime64[ns]':\n", "        try:\n", "            df[date_column] = pd.to_datetime(df[date_column])\n", "            print(f\"Converted {date_column} to datetime format.\")\n", "        except Exception as e:\n", "            print(f\"Warning: Could not convert {date_column} to datetime format: {e}\")\n", "\n", "    # Generate column summaries for Gemini context\n", "    column_summaries = {}\n", "    for col in df.columns:\n", "        if col in [date_column, target_column] + promo_channels:\n", "            try:\n", "                summary = {\n", "                    \"min\": float(df[col].min()),\n", "                    \"max\": float(df[col].max()),\n", "                    \"mean\": float(df[col].mean()),\n", "                    \"median\": float(df[col].median())\n", "                }\n", "                column_summaries[col] = summary\n", "            except:\n", "                pass\n", "\n", "    # Extract model coefficients and other important info\n", "    model_coefficients = {}\n", "    channel_impacts = {}\n", "    channel_effectiveness = {}\n", "    best_result_columns = []\n", "    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:\n", "        best_result_columns = list(best_result_table.columns)\n", "\n", "    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:\n", "        try:\n", "            for _, row in best_result_table.iterrows():\n", "                channel = row.get('Channel', '')\n", "                if channel:\n", "                    model_coefficients[channel] = row.get('Estimate', 0)\n", "                    channel_impacts[channel] = row.get('Impact Percentage', 0)\n", "                    channel_effectiveness[channel] = row.get('Effectiveness', 0)\n", "        except Exception as e:\n", "            print(f\"Warning: Could not extract model details: {e}\")\n", "\n", "    # Analyze question type\n", "    question_needs_viz = any(kw in user_question.lower() for kw in [\n", "        'show', 'graph', 'plot', 'chart', 'visualize', 'trend', 'compare', 'timeline',\n", "        'over time', 'pattern', 'correlation'\n", "    ])\n", "\n", "    # Create context for Gemini\n", "    context = f\"\"\"\n", "\n", "I'm a marketing mix modeling chatbot generating Python code to answer questions about a marketing mix model.\n", "\n", "USER QUESTION: \"{user_question}\"\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> BASE OF PRE-DEFINED FUNCTIONS:\n", "IMPORTANT IMPLEMENTATION NOTES:\n", "1. The knowledge base contains EXAMPLE functions only - they are NOT actually implemented in the runtime environment.\n", "2. You MUST generate COMPLETE, SELF-CONTAINED code that includes ALL necessary imports and function definitions.\n", "3. Even if a similar function exists in the knowledge base, you must re-implement it fully in your response.\n", "4. DO NOT reference or call any functions from the knowledge base directly - they don't exist at runtime.\n", "5. Include explicit imports for all libraries used (matplotlib.pyplot as plt, seaborn as sns, etc.) at the beginning of your code.\n", "6. All visualization code must include proper exception handling and end with plt.show() to display results.\n", "7. Ensure your code uses ONLY the variables provided in the execution context:\n", "  - df, target_column, date_column, promo_channels, all_transformed_features, best_result_table,\n", "  - params, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel,\n", "  - model_coefficients, channel_impacts, channel_effectiveness, spends_df\n", "8. You can use any standard Python libraries that are already imported in the main function.\n", "9. The knowledge base functions are for REFERENCE ONLY to understand the expected style and approach.\n", "{knowledge_base}\n", "## CRITICAL: VA<PERSON><PERSON>LE ACCESS INSTRUCTIONS\n", "Your code will be executed in an environment where all variables are available, but you MUST:\n", "1. First verify the existence of ALL variables before using them\n", "2. Use ONLY the local variables already defined in the execution environment\n", "3. DO NOT try to import or redefine these variables\n", "4. ALWAYS check if dictionaries like channel_impacts exist before accessing them\n", "\n", "## CRITICAL: VARIABLE VERIFICATION CODE PATTERN\n", "ALWAYS include this pattern at the beginning of your code:\n", "\n", "```python\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df', 'spend_column', 'spend_channel_column']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {{', '.join(missing_vars)}}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{{var_name}} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{{var_name}} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{{var_name}} = []\")\n", "        else:\n", "            exec(f\"{{var_name}} = None\")\n", "\n", "For Correlation Question:\n", "If pharses like final transformed activity/ final features / final activity or similar are used then refer to the {best_result_table} to get the final heatmap with only the 'marketing activities'\n", "\n", "For questions related to sales perspective/profot/loss:\n", "After calculations and final output give a small sentance as well describing the output always\n", "\n", "## CRITICAL: ROI CALCULATION \n", "For ANY question related to ROI calculations:\n", "the {best_result_table} contains the ROI column for the respective channels. Use this column to print ROI but the base channel will be stored as base_channel_Adstock in the {best_result_table}. Use this column to calculate the ROI..\n", "eg. PDE will be PDE_Adstock etc\n", "\n", "## CRITICAL: CORRELATION ANALYSIS\n", "For questions related to correlations:\n", "- Always use the BASE promo_channels list (not transformed channels)\n", "- Before calculating correlations, PRINT the available columns in df to verify\n", "- Check if each promo channel exists in df and print which ones are found\n", "- If no channels are found, print a helpful debug message with the available columns\n", "- If asked about \"transformed\" correlations, explain that raw data is being used \n", "  instead because transformed columns are not available in the dataframe\n", "  \n", "VARIABLE INITIALIZATION REQUIREMENTS\n", "\n", "CRITICAL: You MUST initialize ALL your own variables before use\n", "Initialize channel_name = None at the beginning of your code\n", "Initialize all variables that might be referenced later with appropriate defaults\n", "For loops, define iterator variables explicitly before usage\n", "For dictionary access, check if keys exist before accessing them\n", "\n", "CODE SAFETY REQUIREMENTS\n", "\n", "Use descriptive, unique variable names (e.g., impact_ch_name, base_ch_name)\n", "NEVER use generic variable names like 'channel', 'ch', or 'ch_name' in loops\n", "For dictionary iteration, use clear naming: for base_ch_name, values in dict_name.items():\n", "Before accessing dictionaries: ALWAYS use .get() with default values\n", "Before accessing DataFrame columns: ALWAYS check if columns exist\n", "Before any list or DataFrame index access: ALWAYS check length\n", "Initialize result containers (lists, dicts) BEFORE any loop\n", "Handle potential errors with try/except blocks\n", "\n", "AVAILABLE VARIABLES\n", "This code will be executed directly in the Python environment with access to these variables:\n", "\n", "df: The pandas DataFrame containing all data\n", "target_column: Name of the target variable (string)\n", "date_column: Name of the date column (string)\n", "promo_channels: List of promotional channel names (list of strings)\n", "all_transformed_features: List of all transformed feature names (list of strings)\n", "best_result_table: DataFrame with results from the best regression model\n", "params: Dictionary with all analysis parameters\n", "transformed_channels_by_promo: Dictionary with base channel as key and its top 3 transformations\n", "adstocked_channels_by_promo: Dictionary with base channel as key and its top 3 adstocks used\n", "adstock_range_channel: Dictionary of adstock range applied for each promo_channel\n", "model_coefficients: Dictionary mapping variable names to their coefficients\n", "channel_impacts: Dictionary of channel impact percentages (contributions)\n", "channel_effectiveness: Dictionary of channel effectiveness metrics\n", "spends_df: DataFrame containing channel names and their corresponding spend amounts\n", "spend_column: Spends column name of spends_df\n", "spend_channel_column: Channel name of spends_df\n", "historical_df: DataFrame containing channel names and their corresponding contributions of the history\n", "historical_impact: contribution column name of historical_df\n", "historical_channel_column: Channel name of historical_df\n", "unit_price: The price per unit of the target variable (obtain from user or use standard value)\n", "\n", "IMPORTS\n", "Always include these imports at the beginning of your code:\n", "pythonimport pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "DATASET CONTEXT\n", "\n", "Target variable: {target_column}\n", "Date column: {date_column}\n", "Promotional channels: {', '.join(promo_channels)}\n", "Time period: {df[date_column].min().strftime('%Y-%m-%d') if hasattr(df[date_column].min(), 'strftime') else df[date_column].min()} to {df[date_column].max().strftime('%Y-%m-%d') if hasattr(df[date_column].max(), 'strftime') else df[date_column].max()}\n", "Dataset shape: {df.shape}\n", "unit price: {unit_price}\n", "Spend information available in spends_df with columns {spend_channel_column} and {spend_column}\n", "Historical data in historical_df with columns {historical_channel_column} and {historical_impact}\n", "\n", "MODEL CONTEXT:\n", "- Model coefficients: {json.dumps(model_coefficients)}\n", "- Channel impacts (contributions): {json.dumps(channel_impacts)}\n", "- Channel effectiveness: {json.dumps(channel_effectiveness)}\n", "- Transformed channels by promo: Dictionary with base channel as key and its top 3 transformations\n", "- Adstocked channels by promo: Dictionary with base channel as key and its top 3 adstocks used\n", "- Adstock range by channel: Dictionary of adstock range applied for each promo_channel\n", "- Best result table columns: {best_result_columns}\n", "- T1, T2, T3, etc. are trend variables used to understand trends in the data and NOT considered as promotions\n", "- NOTE: When calculating ROI, you'll need to map between base channel names in spends_df (like 'PDE') and transformed channel names in best_result_table (like 'PDE_Adstock_10_Log')\n", "\n", "VARIABLE CLASSIFICATION:\n", "- CRITICAL: When asked about \"drivers\", \"channels\", \"marketing factors\", \"top N marketing channels\", or similar terms:\n", "  * STRICTLY INCLUDE ONLY actual marketing variables (promo_channels and their transformations)\n", "  * STRICTLY EXCLUDE ALL of the following, which are NOT marketing drivers:\n", "    - Intercept/constant term (represents baseline/base sales)\n", "    - ANY trend variables (variables starting with 'T' followed by a number like T1, T2, or containing 'trend', 'time', or 'Rtime')\n", "    - ANY seasonal variables (containing 'season', 'month', 'week', 'quarter')\n", "    - ANY lag variables (containing '_lag_' or ending with 'lag' followed by a number like 'target_lag_1')\n", "    - ANY other non-promotional variables\n", "\n", "LAG VARIABLES CLASSIFICATION - CRITICAL:\n", "- Lag variables (e.g., 'target_lag_1', 'sales_lag_2', or any variable containing '_lag_') are ALWAYS considered NON-MARKETING variables\n", "- They represent the influence of past target values and MUST NEVER be included in marketing contribution analyses\n", "- When filtering variables for marketing analysis, ALWAYS exclude lag variables\n", "- Common lag variable patterns include:\n", "  * Variables containing '_lag_' anywhere in the name\n", "  * Variables ending with 'lag' followed by a number (e.g., 'revlag1')\n", "  * Any variable containing 'lag' that isn't clearly a marketing channel name\n", "- When categorizing variables, lag variables should ALWAYS be grouped with other non-marketing factors like intercept and trend\n", "\n", "\n", "CODE REQUIREMENTS\n", "\n", "Include ALL necessary imports at the beginning\n", "Use matplotlib/seaborn for visualizations with proper titles, labels, and legends\n", "End visualizations with plt.show()\n", "Include print statements with brief interpretations\n", "Format text-based results as clear, readable output\n", "Handle all potential errors gracefully\n", "Use try/except blocks liberally to catch any possible errors\n", "\n", "EXAMPLE OF PROPER VARIABLE HANDLING\n", "python# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import traceback\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients']\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "if missing_vars:\n", "    print(f\"Error: Missing required variables:  {{', '.join(missing_vars)}}\")\n", "\n", "# Initialize ALL variables\n", "channel_name = None\n", "impact_value = 0\n", "filtered_channels = []\n", "result_dict = {{}}\n", "\n", "try:\n", "    # Check if dictionary exists before using it\n", "    if 'channel_impacts' in locals() or 'channel_impacts' in globals():\n", "        # Safe dictionary access with get()\n", "        for impact_ch_name, impact_val in channel_impacts.items():\n", "            if impact_ch_name in promo_channels:\n", "                filtered_channels.append(impact_ch_name)\n", "        \n", "        # Handle edge cases\n", "        if not filtered_channels:\n", "            print(\"No matching channels found\")\n", "        else:\n", "            print(f\"Found {{len(filtered_channels)}} channels\")\n", "    else:\n", "        print(\"channel_impacts dictionary not available\")\n", "        \n", "except Exception as e:\n", "    print(f\"Error during analysis: {{str(e)}}\")\n", "    traceback.print_exc()\n", "\n", "CRITICAL_ FOR VISULIZATION\n", "When analyzing time series data with dates in YYYYMM format (e.g., 202411), please:\n", "\n", "1. Convert dates to proper datetime objects using:\n", "   `df['Date'] = pd.to_datetime(df['Date'].astype(str).str[:4] + '-' + df['Date'].astype(str).str[4:6] + '-01')`\n", "\n", "2. For questions like \"Show the average [target] for each month or quarter across the years\":\n", "   - Extract year and month components from the converted dates\n", "   - Group by month or quarter to aggregate across all years\n", "   - Calculate averages for each time period (month/quarter)\n", "   - Create visualizations showing the average values by time period with proper labels\n", "   - Include an \"Overall Average\" line or reference\n", "\n", "3. Ensure the code handles:\n", "   - Both monthly and quarterly aggregation options\n", "   - Proper sorting of months/quarters in chronological order\n", "   - Clear labels for time periods (e.g., \"<PERSON>\", \"Feb\", \"Mar\" instead of 1, 2, 3)\n", "   - Visualization of results with appropriate title, axis labels, and legend\n", "   - LABEL the points for the visulaizations\n", "\n", "The resulting analysis should make it easy to identify seasonal patterns regardless of year.\n", "\n", "I NEED COMPLETE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PYTHON CODE ONLY.\n", "\"\"\"\n", "\n", "    try:\n", "        print(\"\\nAnalyzing your question...\")\n", "        response = model.generate_content(context)\n", "        code_to_execute = response.text.strip()\n", "\n", "        # Clean up the code\n", "        if code_to_execute.startswith(\"```python\"):\n", "            code_to_execute = code_to_execute[9:]\n", "        if code_to_execute.startswith(\"```\"):\n", "            code_to_execute = code_to_execute[3:]\n", "        if code_to_execute.endswith(\"```\"):\n", "            code_to_execute = code_to_execute[:-3]\n", "\n", "        code_to_execute = code_to_execute.strip()\n", "        print(\"\\nGenerating analysis...\")\n", "        print(\"=\"*50)\n", "        print(\"GENERATED CODE:\")\n", "        print(\"=\"*50)\n", "        print(code_to_execute)\n", "        print(\"=\"*50)\n", "\n", "        # Set up the execution environment\n", "        local_vars = {\n", "            'df': df.copy(),\n", "            'target_column': target_column,\n", "            'date_column': date_column,\n", "            'promo_channels': promo_channels,\n", "            'all_transformed_features': all_transformed_features,\n", "            'best_result_table': best_result_table,\n", "            'params': params,\n", "            'model_coefficients': model_coefficients,\n", "            'channel_impacts': channel_impacts,\n", "            'channel_effectiveness': channel_effectiveness,\n", "            'transformed_channels_by_promo': transformed_channels_by_promo,\n", "            'adstocked_channels_by_promo': adstocked_channels_by_promo,\n", "            'adstock_range_channel': adstock_range_channel,\n", "            'spends_df': spends_df.copy() if spends_df is not None else pd.DataFrame(),\n", "            'spend_channel_column': spend_channel_column,\n", "            'spend_column': spend_column,\n", "            'historical_df': historical_df.copy() if historical_df is not None else pd.DataFrame(),\n", "            'historical_channel_column': historical_channel_column,\n", "            'historical_impact': historical_impact,\n", "            'unit_price': unit_price,\n", "            'pd': pd,\n", "            'plt': plt,\n", "            'sns': sns,\n", "            'np': np,\n", "            'datetime': datetime,\n", "            'mdates': mdates,\n", "            'variance_inflation_factor': variance_inflation_factor,\n", "            'venn2': venn2,\n", "            'logging': logging,\n", "            'user_question': user_question,\n", "            're' : re\n", "        }\n", "\n", "        # Execute the code with retries - now returns figure as well\n", "        success, output_text, figure, error_message = execute_code_with_retries(code_to_execute, local_vars)\n", "        \n", "        # Initialize result dictionary\n", "        result = {\n", "            'text_output': \"\",\n", "            'image': None,\n", "            'output_type': 'text'\n", "        }\n", "        \n", "        if success:\n", "            print(\"\\nAnalysis completed successfully!\")\n", "            result['text_output'] = output_text if output_text else \"Analysis completed successfully!\"\n", "            \n", "            # Check if we have a figure\n", "            if figure is not None:\n", "                result['image'] = figure\n", "                \n", "                if output_text:\n", "                    result['output_type'] = 'both'\n", "                else:\n", "                    result['output_type'] = 'image'\n", "                    \n", "                print(\"Figure captured successfully!\")\n", "                print(f\"Figure object type: {type(figure)}\")\n", "                print(f\"Figure size: {figure.get_size_inches()}\")\n", "            else:\n", "                result['output_type'] = 'text'\n", "                print(\"No figure was created.\")\n", "                \n", "        else:\n", "            error_msg = f\"Failed to generate analysis after multiple attempts.\\n\"\n", "            if error_message:\n", "                error_msg += f\"Last error: {error_message}\\n\"\n", "            error_msg += \"Please try rephrasing your question or ask something different.\"\n", "            result['text_output'] = error_msg\n", "            result['output_type'] = 'text'\n", "\n", "        return result\n", "\n", "    except Exception as e:\n", "        error_msg = f\"Error generating analysis: {e}\\nPlease try again with a different question.\"\n", "        print(f\"Exception in interactive_mmm_chatbot: {e}\")\n", "        traceback.print_exc()\n", "        return {\n", "            'text_output': error_msg,\n", "            'image': None,\n", "            'output_type': 'text'\n", "        }\n", "\n", "\n", "# Updated chatbot function\n", "def chatbot(user_question):\n", "    import os\n", "    from datetime import datetime\n", "    import matplotlib.pyplot as plt\n", "    \n", "    # Your existing setup code here...\n", "    target_column, date_column, id_column, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact, unit_price, best_result_table, all_regression_results, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel, all_features, all_transformed_features, promotional_columns, best_features, date_format,params = result_extraction()\n", "    \n", "    # Load dataframes\n", "    file_path = \"MMix_data_for_testing.csv\"\n", "    spends_file_path = \"Spends_test.xlsx\"\n", "    historical_file_path = \"Historical_test.xlsx\"\n", "\n", "    df = load_file(file_path)\n", "    spends_df = load_file(spends_file_path)\n", "    historical_df = load_file(historical_file_path)\n", "    \n", "    df = process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date, date_format, best_features)\n", "    df, control_variables = add_control_variables(df, id_column, date_column)\n", "    \n", "    params = setup_parameters(date_column, id_column, target_column, promotional_columns, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact)\n", "           \n", "    user_question = \"Correlation between promo channel and target\"\n", "    \n", "    result = interactive_mmm_chatbot(\n", "        user_question=user_question,\n", "        df=df,\n", "        target_column=target_column,\n", "        date_column=date_column,\n", "        promo_channels=promotional_columns,\n", "        all_transformed_features=all_transformed_features,\n", "        best_result_table=best_result_table,\n", "        params=params,\n", "        transformed_channels_by_promo=transformed_channels_by_promo,\n", "        adstocked_channels_by_promo=adstocked_channels_by_promo,\n", "        adstock_range_channel=adstock_range_channel,\n", "        spends_df=spends_df,\n", "        spend_channel_column=spend_channel_column,\n", "        spend_column=spend_column,\n", "        historical_df=historical_df,\n", "        historical_channel_column=historical_channel_column,\n", "        historical_impact=historical_impact,\n", "        unit_price=unit_price\n", "    )\n", "    \n", "    # Handle the result\n", "    output_type = result['output_type']\n", "    print(f\"output_type: {output_type}\")\n", "    \n", "    # Print text output if available\n", "    if 'text_output' in result and result['text_output']:\n", "        print(\"\\nAnalysis Results:\")\n", "        print(result['text_output'])\n", "    \n", "    # Handle image if available\n", "    if output_type in ['image', 'both'] and result['image'] is not None:\n", "        # Create images directory if it doesn't exist\n", "        os.makedirs('images', exist_ok=True)\n", "        \n", "        # Generate filename with timestamp\n", "        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "        filename = f\"mmm_analysis_{timestamp}.png\"\n", "        filepath = os.path.join('images', filename)\n", "        \n", "        try:\n", "            # Save the figure with high DPI for better quality\n", "            result['image'].savefig(filepath, dpi=300, bbox_inches='tight', \n", "                                  facecolor='white', edgecolor='none')\n", "            \n", "            print(f\"\\nImage successfully saved to: {filepath}\")\n", "            \n", "            # Verify the file was actually created\n", "            if os.path.exists(filepath):\n", "                file_size = os.path.getsize(filepath)\n", "                print(f\"File size: {file_size} bytes\")\n", "            else:\n", "                print(\"Warning: File was not created despite no errors\")\n", "                \n", "        except Exception as save_error:\n", "            print(f\"Error saving figure: {save_error}\")\n", "            traceback.print_exc()\n", "        \n", "        finally:\n", "            # Close the figure to free memory\n", "            plt.close(result['image'])\n", "        \n", "        # Optional: Display the image if you're in a Jupyter notebook\n", "        from IPython.display import Image, display\n", "        display(Image(filepath))\n", "    \n", "    elif output_type in ['image', 'both']:\n", "        print(\"\\nExpected an image but none was generated.\")\n", "        print(\"This might be because:\")\n", "        print(\"1. The generated code didn't create any plots\")\n", "        print(\"2. The code had errors during execution\")\n", "        print(\"3. plt.show() was not called properly in the generated code\")\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 56, "id": "a10ab012", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'>\n", "Date conversion: 180000/180000 dates successfully converted\n", "Dropped 0 rows with invalid dates\n", "[Organic_Engagement] Skipping 6397 outliers - would cause 84.95% drop (>10%)\n", "[Staff_Engagement] Skipping 1859 outliers - would cause 97.95% drop (>10%)\n", "[Email_Engagement] Skipping 136 outliers - would cause 100.00% drop (>10%)\n", "[Digital_Engagement] Skipping 696 outliers - would cause 100.00% drop (>10%)\n", "[Lead_Engagement] Skipping 1820 outliers - would cause 100.00% drop (>10%)\n", "[Speaker_programs] Skipping 69 outliers - would cause 100.00% drop (>10%)\n", "[Doximity_views] Skipping 2848 outliers - would cause 62.20% drop (>10%)\n", "[ReachMD] Skipping 8472 outliers - would cause 100.00% drop (>10%)\n", "[CRM_Engagement_Count] Skipping 476 outliers - would cause 93.68% drop (>10%)\n", "[Event_Attendance] Skipping 156 outliers - would cause 100.00% drop (>10%)\n", "Total outliers removed: 0\n", "Remaining rows: 180000\n", "Outlier summary:\n", "                 channel        before         after  Percentage drop\n", "0     Organic_Engagement   8530.000000   8530.000000              0.0\n", "1       Staff_Engagement   2091.100000   2091.100000              0.0\n", "2       Email_Engagement    514.000000    514.000000              0.0\n", "3     Digital_Engagement    631.500000    631.500000              0.0\n", "4        Lead_Engagement   1529.900000   1529.900000              0.0\n", "5       Speaker_programs     69.000000     69.000000              0.0\n", "6         Doximity_views  61456.000000  61456.000000              0.0\n", "7                ReachMD   8472.000000   8472.000000              0.0\n", "8   CRM_Engagement_Count   3086.000000   3086.000000              0.0\n", "9       Event_Attendance    156.000000    156.000000              0.0\n", "10                 Units   5127.533333   5127.533333              0.0\n", "Type of df[date_column]: <class 'pandas._libs.tslibs.timestamps.Timestamp'>\n", "Ensuring all required features are created: ['Units_lag_1', 'Organic_Engagement_Adstock_50_Root9', 'Staff_Engagement_Adstock_90_Root8', 'Email_Engagement_Adstock_20_Root8', 'Digital_Engagement_Adstock_90_Root9', 'Lead_Engagement_Adstock_90_Root9', 'Doximity_views_Adstock_50_Root4', 'ReachMD_Adstock_80_Root4', 'Event_Attendance_Adstock_90_Root9']\n", "Creating feature: Organic_Engagement_Adstock_50_Root9 from base channel Organic_Engagement\n", "Successfully created feature: Organic_Engagement_Adstock_50_Root9\n", "Creating feature: Staff_Engagement_Adstock_90_Root8 from base channel Staff_Engagement\n", "Successfully created feature: Staff_Engagement_Adstock_90_Root8\n", "Creating feature: Email_Engagement_Adstock_20_Root8 from base channel Email_Engagement\n", "Successfully created feature: Email_Engagement_Adstock_20_Root8\n", "Creating feature: Digital_Engagement_Adstock_90_Root9 from base channel Digital_Engagement\n", "Successfully created feature: Digital_Engagement_Adstock_90_Root9\n", "Creating feature: Lead_Engagement_Adstock_90_Root9 from base channel Lead_Engagement\n", "Successfully created feature: Lead_Engagement_Adstock_90_Root9\n", "Creating feature: Doximity_views_Adstock_50_Root4 from base channel Doximity_views\n", "Successfully created feature: Doximity_views_Adstock_50_Root4\n", "Creating feature: ReachMD_Adstock_80_Root4 from base channel ReachMD\n", "Successfully created feature: ReachMD_Adstock_80_Root4\n", "Creating feature: Event_Attendance_Adstock_90_Root9 from base channel Event_Attendance\n", "Successfully created feature: Event_Attendance_Adstock_90_Root9\n", "Added 13 control variables\n", "Gemini API initialized successfully.\n", "\n", "Analyzing your question...\n", "\n", "Generating analysis...\n", "==================================================\n", "GENERATED CODE:\n", "==================================================\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df', 'spend_column', 'spend_channel_column']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {', '.join(missing_vars)}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{var_name} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{var_name} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{var_name} = []\")\n", "        else:\n", "            exec(f\"{var_name} = None\")\n", "\n", "try:\n", "    # Initialize variables\n", "    channel_name = None\n", "    highest_roi = None\n", "    highest_roi_channel = None\n", "    roi_value = 0\n", "    \n", "    # Verify that 'best_result_table' exists and is not empty\n", "    if best_result_table is None or best_result_table.empty:\n", "        print(\"The 'best_result_table' DataFrame is empty or does not exist.\")\n", "        highest_roi = None\n", "        highest_roi_channel = None\n", "    else:\n", "        # Filter out rows where 'ROI' is NaN\n", "        roi_df = best_result_table.dropna(subset=['ROI']).copy()\n", "        \n", "        # Filter out non-marketing variables\n", "        def get_marketing_variables_only(best_result_table, promo_channels):\n", "            marketing_rows = []\n", "            \n", "            for _, row in best_result_table.iterrows():\n", "                var_name = row.get('Channel', '')\n", "                # Check if this is a legitimate marketing variable\n", "                is_marketing_var = False\n", "                \n", "                # Check if the variable is derived from any promo channel\n", "                for channel in promo_channels:\n", "                    if var_name.startswith(channel + '_') or var_name == channel:\n", "                        is_marketing_var = True\n", "                        break\n", "                \n", "                # Explicitly exclude non-marketing variables\n", "                if (var_name == 'Intercept' or \n", "                    var_name.startswith('T') or \n", "                    'time' in var_name.lower() or \n", "                    'trend' in var_name.lower() or \n", "                    'Rtime' in var_name or\n", "                    'season' in var_name.lower() or\n", "                    'month' in var_name.lower() or\n", "                    'week' in var_name.lower() or\n", "                    'quarter' in var_name.lower() or\n", "                    '_lag_' in var_name.lower() or  # Exclude variables with '_lag_' pattern\n", "                    (var_name.lower().endswith('lag') or  # Ends with 'lag'\n", "                     any(var_name.lower().endswith(f'lag_{i}') for i in range(10)))):\n", "                    is_marketing_var = False\n", "                    \n", "                if is_marketing_var:\n", "                    marketing_rows.append(row)\n", "            \n", "            return pd.DataFrame(marketing_rows)\n", "\n", "        marketing_only_table = get_marketing_variables_only(roi_df, promo_channels)\n", "\n", "        # Find the channel with the highest ROI\n", "        if not marketing_only_table.empty:\n", "            highest_roi_row = marketing_only_table.loc[marketing_only_table['ROI'].idxmax()]\n", "            highest_roi = highest_roi_row['ROI']\n", "            highest_roi_channel = highest_roi_row['Channel']\n", "        else:\n", "            highest_roi = None\n", "            highest_roi_channel = None\n", "        \n", "        if highest_roi is not None:\n", "            print(f\"Channel with the highest ROI: {highest_roi_channel} with ROI: {highest_roi}\")\n", "        else:\n", "            print(\"No marketing channels with valid ROI values found in the best_result_table.\")\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")\n", "    traceback.print_exc()\n", "\n", "print(\"\n", "\" + \"=\"*80)\n", "print(\" BUSINESS INSIGHTS & RECOMMENDATIONS\")\n", "print(\"=\"*80)\n", "\n", "print(\"\n", "KEY FINDINGS:\")\n", "if highest_roi is not None:\n", "    print(f\"• The channel delivering the highest ROI is {highest_roi_channel} with an ROI of {highest_roi:.2f}.\")\n", "    print(\"• This indicates that for every dollar invested in this channel, the return is approximately $\", highest_roi)\n", "else:\n", "    print(\"• No marketing channels with valid ROI values were found. Please check the data and model results.\")\n", "\n", "print(\"\n", "💡 STRATEGIC IMPLICATIONS:\")\n", "if highest_roi is not None:\n", "    print(f\"• Focus budget allocation on the {highest_roi_channel} channel to maximize returns. Re-evaluate spending on lower-performing channels.\")\n", "    print(\"• Understand the factors contributing to the high ROI of this channel and replicate them in other channels where possible.\")\n", "else:\n", "    print(\"• Investigate why ROI values are missing for marketing channels in the best_result_table. This is critical for making informed decisions.\")\n", "\n", "print(\"\n", "🎯 RECOMMENDED ACTIONS:\")\n", "if highest_roi is not None:\n", "    print(\"• Increase investment in\", highest_roi_channel, \"while carefully monitoring for diminishing returns.\")\n", "    print(\"• Analyze the customer journey and touchpoints associated with\", highest_roi_channel, \"to gain insights into its effectiveness.\")\n", "    print(\"• Develop a long-term strategy to sustain the high ROI of this channel.\")\n", "else:\n", "    print(\"• Review the data inputs and model outputs to identify any issues with ROI calculations.\")\n", "    print(\"• Ensure that spend data and channel performance metrics are accurately captured and attributed.\")\n", "    print(\"• Recalibrate the model and re-evaluate channel performance once the data issues are resolved.\")\n", "\n", "print(\"\n", " RISKS & OPPORTUNITIES:\")\n", "if highest_roi is not None:\n", "    print(\"• Risk: Over-investment in a single channel could lead to saturation and reduced effectiveness.\")\n", "    print(\"• Opportunity: Explore synergies between\", highest_roi_channel, \"and other channels to further amplify returns.\")\n", "else:\n", "    print(\"• Risk: Without accurate ROI data, budget allocation decisions are based on incomplete information, potentially leading to suboptimal outcomes.\")\n", "    print(\"• Opportunity: Accurate ROI data enables data-driven decision-making and optimization of marketing investments.\")\n", "\n", "print(\"\n", "NEXT STEPS:\")\n", "if highest_roi is not None:\n", "    print(\"• Continuously monitor the ROI of\", highest_roi_channel, \"and adjust investments as needed.\")\n", "    print(\"• Test new strategies and tactics within this channel to further optimize performance.\")\n", "    print(\"• Track key metrics such as customer acquisition cost, conversion rates, and lifetime value to assess the overall profitability of this channel.\")\n", "else:\n", "    print(\"• Verify the accuracy of spend data and channel performance metrics.\")\n", "    print(\"• Re-run the marketing mix model with corrected data to obtain accurate ROI values.\")\n", "    print(\"• Conduct a sensitivity analysis to understand how changes in spend and channel performance impact ROI.\")\n", "==================================================\n", "\n", "Attempt 1/6...\n", "Attempt 1 failed: Error: unterminated string literal (detected at line 107) (<string>, line 107)\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4632\\1367437395.py\", line 39, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 107\n", "    print(\"\n", "          ^\n", "SyntaxError: unterminated string literal (detected at line 107)\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "Attempt 2/6...\n", "Attempt 2 failed: Error: unterminated string literal (detected at line 107) (<string>, line 107)\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4632\\1367437395.py\", line 39, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 107\n", "    print(\"\n", "          ^\n", "SyntaxError: unterminated string literal (detected at line 107)\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "Attempt 3/6...\n", "Attempt 3 failed: Error: unterminated string literal (detected at line 107) (<string>, line 107)\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4632\\1367437395.py\", line 39, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 107\n", "    print(\"\n", "          ^\n", "SyntaxError: unterminated string literal (detected at line 107)\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "Attempt 4/6...\n", "Attempt 4 failed: Error: unterminated string literal (detected at line 107) (<string>, line 107)\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4632\\1367437395.py\", line 39, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 107\n", "    print(\"\n", "          ^\n", "SyntaxError: unterminated string literal (detected at line 107)\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "Attempt 5/6...\n", "Attempt 5 failed: Error: unterminated string literal (detected at line 107) (<string>, line 107)\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4632\\1367437395.py\", line 39, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 107\n", "    print(\"\n", "          ^\n", "SyntaxError: unterminated string literal (detected at line 107)\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "Attempt 6/6...\n", "All 6 attempts failed.\n", "output_type: text\n", "\n", "Analysis Results:\n", "Failed to generate analysis after multiple attempts.\n", "Last error: Error: unterminated string literal (detected at line 107) (<string>, line 107)\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4632\\1367437395.py\", line 39, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 107\n", "    print(\"\n", "          ^\n", "SyntaxError: unterminated string literal (detected at line 107)\n", "\n", "Please try rephrasing your question or ask something different.\n", "Bot response saved with ID: 30\n", "\n", "=== Final Results ===\n", "Image URL: None\n", "Bot Message ID: 30\n"]}], "source": ["\n", "\n", "def initialize_gcs_client(bucket_name, project_id, credentials_path):\n", "    \"\"\"Initialize GCP storage client\"\"\"\n", "    from google.cloud import storage\n", "    from google.oauth2 import service_account\n", "    \n", "    credentials = service_account.Credentials.from_service_account_file(credentials_path)\n", "    gcs_client = storage.Client(project=project_id, credentials=credentials)\n", "    bucket = gcs_client.bucket(bucket_name)\n", "    return bucket\n", "\n", "def upload_image_to_gcs(bucket, image_data, file_path):\n", "    \"\"\"Upload image to GCS and return the URL\"\"\"\n", "    try:\n", "        blob = bucket.blob(file_path)\n", "        blob.upload_from_string(image_data, content_type=\"image/png\")\n", "        \n", "        # With uniform bucket-level access enabled, we use the standard GCS URL format\n", "        image_url = f\"https://storage.googleapis.com/{bucket.name}/{file_path}\"\n", "        \n", "        print(f\"Successfully uploaded image to: {image_url}\")\n", "        return image_url\n", "    except Exception as e:\n", "        print(f\"Error uploading to GCS: {str(e)}\")\n", "        return None\n", "\n", "def save_message_to_db(conn, owner_id, message_text, answers, model_run_id, image_url=None):\n", "    \"\"\"Save message to database and return message_id\"\"\"\n", "    cursor = conn.cursor()\n", "    \n", "    # SQL to insert message (message_id is SERIAL, auto-generated)\n", "    sql = \"\"\"\n", "    INSERT INTO messages (owner_id, message_text, answer, model_run_id, image_url)\n", "    VALUES (%s, %s, %s, %s, %s)\n", "    RETURNING message_id\n", "    \"\"\"\n", "    \n", "    # Execute the SQL\n", "    cursor.execute(sql, (owner_id, message_text, answers, model_run_id, image_url))\n", "    \n", "    # Get the message_id\n", "    message_id = cursor.fetchone()[0]\n", "    \n", "    # Commit the transaction\n", "    conn.commit()\n", "    cursor.close()\n", "    \n", "    return message_id\n", "\n", "def connect_to_database(db_config):\n", "    \"\"\"Establish database connection\"\"\"\n", "    import psycopg2\n", "    try:\n", "        conn = psycopg2.connect(\n", "            host=db_config['host'],\n", "            database=db_config['name'],\n", "            user=db_config['user'],\n", "            password=db_config['password'],\n", "            port=db_config['port']\n", "        )\n", "        return conn\n", "    except Exception as e:\n", "        print(f\"Error connecting to database: {e}\")\n", "        return None\n", "\n", "def chatbot(user_question, owner_id, model_run_id, gcs_config):\n", "    \"\"\"Process chatbot response and handle image upload/database storage\"\"\"\n", "    import os\n", "    from datetime import datetime\n", "    import io\n", "\n", "    try:\n", "        # Connect to database\n", "        db_conn = get_db_connection()\n", "        if not db_conn:\n", "            print(\"Failed to connect to database\")\n", "            exit(1)\n", "        \n", "        # Initialize GCS bucket\n", "        gcs_bucket = initialize_gcs_client(\n", "            bucket_name=gcs_config['bucket_name'],\n", "            project_id=gcs_config['project_id'],\n", "            credentials_path=gcs_config['credentials_path']\n", "        )\n", "        \n", "        # Run the chatbot with a sample owner ID and model_run_id\n", " \n", "    except Exception as e:\n", "        print(f\"Error in main execution: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "    \n", "    # Get response from chatbot\n", "    target_column, date_column, id_column, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact, unit_price, best_result_table, all_regression_results, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel, all_features, all_transformed_features, promotional_columns, best_features, date_format,params = result_extraction()\n", "    \n", "    # Load dataframes\n", "    file_path = \"MMix_data_for_testing.csv\"\n", "    spends_file_path = \"Spends_test.xlsx\"\n", "    historical_file_path = \"Historical_test.xlsx\"\n", "\n", "    df = load_file(file_path)\n", "    spends_df = load_file(spends_file_path)\n", "    historical_df = load_file(historical_file_path)\n", "    \n", "    df = process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date, date_format, best_features)\n", "    df, control_variables = add_control_variables(df, id_column, date_column)\n", "           \n", "    \n", "    result = interactive_mmm_chatbot(\n", "        user_question=user_question,\n", "        df=df,\n", "        target_column=target_column,\n", "        date_column=date_column,\n", "        promo_channels=promotional_columns,\n", "        all_transformed_features=all_transformed_features,\n", "        best_result_table=best_result_table,\n", "        params=params,\n", "        transformed_channels_by_promo=transformed_channels_by_promo,\n", "        adstocked_channels_by_promo=adstocked_channels_by_promo,\n", "        adstock_range_channel=adstock_range_channel,\n", "        spends_df=spends_df,\n", "        spend_channel_column=spend_channel_column,\n", "        spend_column=spend_column,\n", "        historical_df=historical_df,\n", "        historical_channel_column=historical_channel_column,\n", "        historical_impact=historical_impact,\n", "        unit_price=unit_price\n", "    )\n", "    \n", "    # Handle the result\n", "    output_type = result['output_type']\n", "    print(f\"output_type: {output_type}\")\n", "    \n", "    # Print text output if available\n", "    if 'text_output' in result and result['text_output']:\n", "        print(\"\\nAnalysis Results:\")\n", "        print(result['text_output'])\n", "    \n", "    # Handle image if available\n", "    if output_type in ['image', 'both'] and result['image'] is not None:\n", "        # Create images directory if it doesn't exist\n", "        os.makedirs('images', exist_ok=True)\n", "        \n", "        # Generate filename with timestamp\n", "        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "        filename = f\"mmm_analysis_{timestamp}.png\"\n", "        filepath = os.path.join('images', filename)\n", "        \n", "        try:\n", "            # Save the figure with high DPI for better quality\n", "            result['image'].savefig(filepath, dpi=300, bbox_inches='tight', \n", "                                  facecolor='white', edgecolor='none')\n", "            \n", "            print(f\"\\nImage successfully saved to: {filepath}\")\n", "            \n", "            # Verify the file was actually created\n", "            if os.path.exists(filepath):\n", "                file_size = os.path.getsize(filepath)\n", "                print(f\"File size: {file_size} bytes\")\n", "            else:\n", "                print(\"Warning: File was not created despite no errors\")\n", "                \n", "        except Exception as save_error:\n", "            print(f\"Error saving figure: {save_error}\")\n", "            traceback.print_exc()\n", "        \n", "        finally:\n", "            # Close the figure to free memory\n", "            plt.close(result['image'])\n", "        \n", "        # Optional: Display the image if you're in a Jupyter notebook\n", "        from IPython.display import Image, display\n", "        display(Image(filepath)) \n", "    \n", "    elif output_type in ['image', 'both']:\n", "        print(\"\\nExpected an image but none was generated.\")\n", "        print(\"This might be because:\")\n", "        print(\"1. The generated code didn't create any plots\")\n", "        print(\"2. The code had errors during execution\")\n", "        print(\"3. plt.show() was not called properly in the generated code\")\n", "    \n", "    \n", "    image_url = None\n", "    \n", "    # Handle image upload if image exists\n", "    if result['output_type'] in ['image', 'both'] and result['image'] is not None:\n", "        try:\n", "            # Convert matplotlib figure to bytes\n", "            img_buffer = io.BytesIO()\n", "            result['image'].savefig(img_buffer, format='png', dpi=300, \n", "                                    bbox_inches='tight', facecolor='white', edgecolor='none')\n", "            img_buffer.seek(0)\n", "            image_data = img_buffer.getvalue()\n", "            \n", "            # Generate unique filename for GCS\n", "            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "            gcs_filename = f\"mmm_analysis/{owner_id}/{model_run_id}_{timestamp}.png\"\n", "            \n", "            # Upload to GCS\n", "            image_url = upload_image_to_gcs(gcs_bucket, image_data, gcs_filename)\n", "            \n", "            print(f\"Image uploaded to GCS: {image_url}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing image: {e}\")\n", " \n", "    \n", "    # Save bot response to database\n", "    bot_response_text = result.get('text_output', 'Analysis completed')\n", "    bot_message_id = save_message_to_db(\n", "        conn=db_conn,\n", "        owner_id=owner_id,\n", "        message_text=user_question,\n", "        answers=bot_response_text,\n", "        model_run_id=model_run_id,\n", "        image_url=image_url\n", "    )\n", "    \n", "    # print(f\"User message saved with ID: {user_message_id}\")\n", "    print(f\"Bot response saved with ID: {bot_message_id}\")\n", "    \n", "    return {\n", "        'response': result,\n", "        'image_url': image_url,\n", "        'bot_message_id': bot_message_id\n", "    }\n", "\n", "if __name__ == \"__main__\":\n", "    # Set up database configuration\n", "    db_config = {\n", "        'host': 'localhost',\n", "        'name': '<PERSON><PERSON>',\n", "        'user': 'postgres',\n", "        'password': 'something',\n", "        'port': 5432\n", "    }\n", "    \n", "    # Set up GCS configuration\n", "    GCS_BUCKET_NAME = 'novomix'\n", "    GCS_PROJECT_ID = 'aichatbot-412315'\n", "\n", "    gcs_config = {\n", "        'bucket_name': GCS_BUCKET_NAME,\n", "        'project_id': GCS_PROJECT_ID,\n", "        'credentials_path': 'Service Account.json'\n", "    }\n", "    \n", "    # Initialize connections\n", "    try:\n", "        owner_id='09'\n", "        model_run_id='session123'\n", "        user_question=\"Which channel delivered the highest ROI?\"\n", "        # user_question=\"Are there any obvious correlations between our different marketing activities?\"\n", "        # user_question=\"What was the Return on Investment (ROI) for our spend on Organic_Engagement?\"\n", "        # Process the chatbot response with integrated upload and database storage\n", "        result = chatbot(\n", "            user_question=user_question,\n", "            owner_id=owner_id,\n", "            model_run_id=model_run_id,\n", "            gcs_config=gcs_config\n", "        )\n", "        \n", "        print(\"\\n=== Final Results ===\")\n", "        print(f\"Image URL: {result['image_url']}\")\n", "        # print(f\"User Message ID: {result['user_message_id']}\")\n", "        print(f\"Bot Message ID: {result['bot_message_id']}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in main execution: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "    \n", "    finally:\n", "        # Clean up database connection\n", "        if 'db_conn' in locals() and db_conn:\n", "            db_conn.close()\n", "            print(\"Database connection closed\")"]}, {"cell_type": "code", "execution_count": 78, "id": "79329582", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (2388493734.py, line 1)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[78], line 1\u001b[1;36m\u001b[0m\n\u001b[1;33m    Successfully uploaded image to: https://storage.googleapis.com/novomix/mmm_analysis/user123/session456_20250522_152310.png\u001b[0m\n\u001b[1;37m                 ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["Successfully uploaded image to: https://storage.googleapis.com/novomix/mmm_analysis/user123/session456_20250522_152310.png\n", "Image uploaded to GCS: https://storage.googleapis.com/novomix/mmm_analysis/user123/session456_20250522_152310.png\n", "Error in main execution: column \"answers\" of relation \"messages\" does not exist\n", "LINE 2:     INSERT INTO messages (owner_id, message_text, answers, m...\n", "                                                          ^\n", "\n", "Database connection closed\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6748\\2699388310.py\", line 268, in <module>\n", "    result = process_chatbot_response(\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6748\\2699388310.py\", line 198, in process_chatbot_response\n", "    user_message_id = save_message_to_db(\n", "                      ^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6748\\2699388310.py\", line 137, in save_message_to_db\n", "    cursor.execute(sql, (owner_id, message_text, message_by, model_run_id, image_url))\n", "psycopg2.errors.UndefinedColumn: column \"answers\" of relation \"messages\" does not exist\n", "LINE 2:     INSERT INTO messages (owner_id, message_text, answers, m...\n", "                                                          ^"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}