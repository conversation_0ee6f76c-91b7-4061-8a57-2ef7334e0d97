{"cells": [{"cell_type": "code", "execution_count": null, "id": "b7b7eded", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4defa0aa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import xlsxwriter\n", "import numpy as np\n", "import google.generativeai as genai\n", "import json\n", "import logging\n", "import statsmodels.api as sm\n", "import os\n", "import sys\n", "import os\n", "from google.cloud import storage\n", "from google.oauth2 import service_account"]}, {"cell_type": "code", "execution_count": null, "id": "a9c474ff", "metadata": {}, "outputs": [], "source": ["import sys"]}, {"cell_type": "code", "execution_count": null, "id": "deb4cde6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import xlsxwriter\n", "import numpy as np\n", "import google.generativeai as genai\n", "import json\n", "import logging\n", "import statsmodels.api as sm\n", "import os\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "f0af0a52", "metadata": {}, "outputs": [], "source": ["pandas\n", "xlsxwriter\n", "numpy as np\n", "import google.generativeai as genai\n", "import json\n", "import logging\n", "import statsmodels.api as sm\n", "import os"]}, {"cell_type": "markdown", "id": "9a25f491", "metadata": {}, "source": ["# Download files from gcp"]}, {"cell_type": "code", "execution_count": null, "id": "e0115915", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to download mmm/2/historical_data.xlsx from bucket novomix\n", "File downloaded to backend at c:\\Users\\<USER>\\OneDrive - Datazymes\\MMX Toolkit\\MMXtool_automate\\Trial codes\\final codes\\historical_data.xlsx\n"]}], "source": ["import os\n", "from google.cloud import storage\n", "from google.oauth2 import service_account\n", "\n", "def download_file_from_gcp_bucket(bucket_name, service_account_path, user_id, file_name, backend_file_path):\n", "    \"\"\"\n", "    Downloads a file from a GCP bucket and saves it to the backend server\n", "    \n", "    Args:\n", "        bucket_name (str): Name of the GCP bucket (with or without gs:// prefix)\n", "        service_account_path (str): Path to the service account JSON file\n", "        user_id (str): User ID folder name inside the 'mmm' main folder\n", "        file_name (str): Name of the file to download\n", "        backend_file_path (str): Path where to save the file on the backend server\n", "    \n", "    Returns:\n", "        str: Path to the downloaded file on the backend server\n", "    \"\"\"\n", "    try:\n", "        # Remove gs:// prefix if present\n", "        if bucket_name.startswith('gs://'):\n", "            bucket_name = bucket_name.replace('gs://', '')\n", "        \n", "        # Set up credentials using the service account file\n", "        credentials = service_account.Credentials.from_service_account_file(\n", "            service_account_path\n", "        )\n", "        \n", "        # Create a storage client\n", "        storage_client = storage.Client(credentials=credentials)\n", "        \n", "        # Get the bucket\n", "        bucket = storage_client.bucket(bucket_name)\n", "        \n", "        # Construct the blob path: mmm/user_id/file_name\n", "        blob_path = f\"mmm/{user_id}/{file_name}\"\n", "        blob = bucket.blob(blob_path)\n", "        \n", "        print(f\"Attempting to download {blob_path} from bucket {bucket_name}\")\n", "        \n", "        # Create directory if it doesn't exist\n", "        os.makedirs(os.path.dirname(backend_file_path), exist_ok=True)\n", "        \n", "        # Download the file to the backend server\n", "        blob.download_to_filename(backend_file_path)\n", "        print(f\"File downloaded to backend at {backend_file_path}\")\n", "            \n", "        return backend_file_path\n", "    \n", "    except Exception as e:\n", "        print(f\"Error: {str(e)}\")\n", "        raise\n", "\n", "# Usage example\n", "bucket_name = \"gs://novomix\"  # Will be cleaned in the function\n", "service_account_path = \"Service Account.json\"\n", "user_id = \"2\"  # From your GCP path: mmm/2/dummy1.xlsx\n", "file_name = \"historical_data.xlsx\"\n", "    \n", "# Store file in the current working directory\n", "try:\n", "    # Try to get the directory of the current script\n", "    current_dir = os.path.dirname(os.path.abspath(__file__))\n", "except NameError:\n", "    # If __file__ is not defined (e.g., in Jupyter notebook or interactive shell)\n", "    current_dir = os.getcwd()\n", "        \n", "backend_file_path = os.path.join(current_dir, file_name)\n", "\n", "# Download the file to the backend only\n", "downloaded_file_path = download_file_from_gcp_bucket(\n", "    bucket_name=bucket_name,\n", "    service_account_path=service_account_path,\n", "    user_id=user_id,\n", "    file_name=file_name,\n", "    backend_file_path=backend_file_path\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fa8e6602", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e9a8fba9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "061c46ba", "metadata": {}, "source": ["# Store files in database"]}, {"cell_type": "code", "execution_count": null, "id": "f97fbc4a", "metadata": {}, "outputs": [{"ename": "UniqueViolation", "evalue": "duplicate key value violates unique constraint \"file_info_user_id_file_name_key\"\nDETAIL:  Key (user_id, file_name)=(2, spends.xlsx) already exists.\n", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m\n", "\u001b[1;31mUniqueViolation\u001b[0m                           <PERSON><PERSON> (most recent call last)\n", "Cell \u001b[1;32mIn[34], line 65\u001b[0m\n", "\u001b[0;32m     63\u001b[0m df\u001b[38;5;241m=\u001b[39mload_file(file_name)\n", "\u001b[0;32m     64\u001b[0m columns\u001b[38;5;241m=\u001b[39mdf\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mtolist()\n", "\u001b[1;32m---> 65\u001b[0m record_id \u001b[38;5;241m=\u001b[39m \u001b[43mstore_file_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muser_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfile_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfile_type\u001b[49m\u001b[43m,\u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;32m     66\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInserted record with ID: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrecord_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;32m     69\u001b[0m conn\u001b[38;5;241m.\u001b[39mclose()\n", "\n", "Cell \u001b[1;32mIn[34], line 33\u001b[0m, in \u001b[0;36mstore_file_info\u001b[1;34m(db_conn, user_id, file_name, file_type, file_id, columns)\u001b[0m\n", "\u001b[0;32m     26\u001b[0m query \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\"\"\u001b[39m\n", "\u001b[0;32m     27\u001b[0m \u001b[38;5;124mINSERT INTO file_info (user_id, file_name, file_id, file_type, columns)\u001b[39m\n", "\u001b[0;32m     28\u001b[0m \u001b[38;5;124mVALUES (\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m)\u001b[39m\n", "\u001b[0;32m     29\u001b[0m \u001b[38;5;124mRETURNING id;\u001b[39m\n", "\u001b[0;32m     30\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n", "\u001b[0;32m     32\u001b[0m \u001b[38;5;66;03m# Execute the query with parameters\u001b[39;00m\n", "\u001b[1;32m---> 33\u001b[0m \u001b[43mcursor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43muser_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfile_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfile_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfile_type\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcolumns_json\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;32m     35\u001b[0m \u001b[38;5;66;03m# Get the inserted record ID\u001b[39;00m\n", "\u001b[0;32m     36\u001b[0m record_id \u001b[38;5;241m=\u001b[39m cursor\u001b[38;5;241m.\u001b[39mfetchone()[\u001b[38;5;241m0\u001b[39m]\n", "\n", "\u001b[1;31mUniqueViolation\u001b[0m: duplicate key value violates unique constraint \"file_info_user_id_file_name_key\"\n", "DETAIL:  Key (user_id, file_name)=(2, spends.xlsx) already exists.\n"]}], "source": ["import psycopg2\n", "import json\n", "\n", "def store_file_info(db_conn, user_id, file_name, file_type, file_id=None, columns=None):\n", "    \"\"\"\n", "    Store file information in the PostgreSQL database.\n", "    \n", "    Parameters:\n", "    - db_conn: PostgreSQL database connection\n", "    - user_id: User identifier\n", "    - file_name: Name of the file\n", "    - file_type: Type of file (e.g., \"Main\")\n", "    - file_id: Optional file identifier\n", "    - columns: Optional list of column names in the file\n", "    \n", "    Returns:\n", "    - id: The ID of the inserted record\n", "    \"\"\"\n", "\n", "    cursor = db_conn.cursor()\n", "    \n", "    # Convert columns to JSON string if provided\n", "    columns_json = json.dumps(columns) if columns is not None else None\n", "    \n", "    # SQL query to insert data\n", "    query = \"\"\"\n", "    INSERT INTO file_info (user_id, file_name, file_id, file_type, columns)\n", "    VALUES (%s, %s, %s, %s, %s)\n", "    RETURNING id;\n", "    \"\"\"\n", "    \n", "    # Execute the query with parameters\n", "    cursor.execute(query, (user_id, file_name, file_id, file_type, columns_json))\n", "    \n", "    # Get the inserted record ID\n", "    record_id = cursor.fetchone()[0]\n", "    \n", "    # Commit the transaction\n", "    db_conn.commit()\n", "    \n", "    # Close the cursor\n", "    cursor.close()\n", "    \n", "    return record_id\n", "\n", "\n", "\n", "# Example usage:\n", "if __name__ == \"__main__\":\n", "    # Example connection (replace with your actual connection parameters)\n", "    conn = psycopg2.connect(\n", "        host=\"localhost\",\n", "        database=\"MMX\",\n", "        user=\"postgres\",\n", "        password=\"something\"\n", "    )\n", "    \n", "    # Example storing file info\n", "    user_id = \"2\"\n", "    file_name = \"spends.xlsx\"\n", "    file_type = \"Spends\"\n", "\n", "    df=load_file(file_name)\n", "    columns=df.columns.tolist()\n", "    record_id = store_file_info(conn, user_id, file_name, file_type,columns)\n", "    print(f\"Inserted record with ID: {record_id}\")\n", "    \n", "    \n", "    conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "0d716dae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved file information:\n", "[{'id': 3, 'user_id': '2', 'file_name': 'spends.xlsx', 'file_id': None, 'file_type': 'Spends', 'columns': ['Channel', 'Spends']}]\n", "User ID: 2, File: spends.xlsx, ID: None, Type: Spends\n", "Columns: ['Channel', 'Spends']\n"]}], "source": ["import json\n", "import psycopg2\n", "\n", "def get_file_columns(db_conn, user_id, file_name=None, file_id=None):\n", "    \"\"\"\n", "    Retrieve file columns for a particular user and file name or file ID.\n", "    \n", "    Parameters:\n", "    - db_conn: PostgreSQL database connection\n", "    - user_id: User identifier\n", "    - file_name: Optional name of the file\n", "    - file_id: Optional file identifier\n", "    \n", "    Returns:\n", "    - List of dictionaries containing file information including columns\n", "    \"\"\"\n", "    cursor = db_conn.cursor()\n", "    \n", "    # Build the query based on provided parameters\n", "    # Added user_id to the SELECT statement\n", "    query = \"SELECT id, user_id, file_name, file_id, file_type, columns FROM file_info WHERE user_id = %s\"\n", "    params = [user_id]\n", "    \n", "    if file_name:\n", "        query += \" AND file_name = %s\"\n", "        params.append(file_name)\n", "    \n", "    if file_id:\n", "        query += \" AND file_id = %s\"\n", "        params.append(file_id)\n", "    \n", "    # Order by creation date (most recent first)\n", "    query += \" ORDER BY created_at DESC\"\n", "    \n", "    # Execute the query\n", "    cursor.execute(query, params)\n", "    \n", "    # Fetch all results\n", "    results = cursor.fetchall()\n", "    \n", "    # Process results into a more usable format\n", "    file_info_list = []\n", "    for row in results:\n", "        id, user_id, file_name, file_id, file_type, columns_data = row\n", "        # Handle different column data types - could be already a list, JSON string, or None\n", "        if isinstance(columns_data, str):\n", "            try:\n", "                columns_data = json.loads(columns_data)\n", "            except json.JSONDecodeError:\n", "                # If it's not valid JSON, keep as is\n", "                pass\n", "        \n", "        file_info = {\n", "            'id': id,\n", "            'user_id': user_id,  # Added user_id to returned data\n", "            'file_name': file_name,\n", "            'file_id': file_id,\n", "            'file_type': file_type,\n", "            'columns': columns_data\n", "        }\n", "        file_info_list.append(file_info)\n", "    \n", "    # Close the cursor\n", "    cursor.close()\n", "    \n", "    return file_info\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    conn = psycopg2.connect(\n", "        host=\"localhost\",\n", "        database=\"MMX\",\n", "        user=\"postgres\",\n", "        password=\"something\"\n", "    )\n", "    \n", "    # Example retrieving columns\n", "    user_id = \"2\"\n", "    file_name = \"spends.xlsx\"\n", "    \n", "    info = get_file_columns(conn, user_id, file_name=file_name)\n", "    print(\"Retrieved file information:\")\n", "    \n", "    # for info in file_info:\n", "    print(f\"User ID: {info['user_id']}, File: {info['file_name']}, ID: {info['file_id']}, Type: {info['file_type']}\")\n", "    print(f\"Columns: {info['columns']}\")\n", "    \n", "    # Close the connection\n", "    conn.close()"]}, {"cell_type": "markdown", "id": "cc125cd5", "metadata": {}, "source": ["#Functions "]}, {"cell_type": "code", "execution_count": null, "id": "970ce0d1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import google.generativeai as genai\n", "import json\n", "import logging\n", "import statsmodels.api as sm\n", "import os\n", "import sys\n", "import matplotlib.pyplot as plt\n", "import matplotlib_venn\n", "import seaborn as sns\n", "import datetime\n", "final_df = pd.DataFrame()\n", "# Configure logging\n", "logging.basicConfig(level=logging.ERROR)\n", "\n", "# Initialize Gemini API\n", "genai.configure(api_key=\"AIzaSyA3cXv0-vsm31kXkJtLUT3W_bHooZm3hN8\")\n", "model = genai.GenerativeModel(\"gemini-2.0-flash\")\n", "\n", "\n", "\n", "def get_unit_price():\n", "    \"\"\"Helper function to get user input for unit price\"\"\"\n", "    return float(input(\"Enter the price of each unit: \").strip())\n", "\n", "\n", "def load_file(file_path):\n", "    \"\"\"\n", "    Load data from a file based on its extension.\n", "\n", "    Args:\n", "        file_path (str): Path to the file to be loaded\n", "\n", "    Returns:\n", "        pandas.DataFrame: Loaded dataframe\n", "    \"\"\"\n", "    try:\n", "        logging.info(\"Loading data from file.\")\n", "\n", "        # Load data based on file extension\n", "        if file_path.endswith(\".csv\"):\n", "            df = pd.read_csv(file_path)\n", "        elif file_path.endswith(\".xlsx\"):\n", "            df = pd.read_excel(file_path)\n", "        else:\n", "            error_msg = f\"ERROR: Unsupported file format for '{file_path}'. Only CSV and Excel files are supported.\"\n", "            logging.error(error_msg)\n", "            print(error_msg)\n", "            sys.exit(1)  # Exit immediately\n", "\n", "        return df\n", "\n", "    except FileNotFoundError:\n", "        error_msg = f\"ERROR: File '{file_path}' not found. Please check the file path and try again.\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately for file not found\n", "    except Exception as e:\n", "        error_msg = f\"ERROR: Failed to load file '{file_path}': {str(e)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately for other loading errors\n", "import os\n", "import logging\n", "import psycopg2\n", "\n", "def get_db_connection():\n", "    \"\"\"\n", "    Create and return a connection to the PostgreSQL database\n", "    \n", "    Returns:\n", "        connection: PostgreSQL database connection object\n", "    \"\"\"\n", "    try:\n", "        # Connect to the PostgreSQL database\n", "        conn = psycopg2.connect(\n", "            host=os.environ.get(\"DB_HOST\", \"localhost\"),\n", "            database=os.environ.get(\"DB_NAME\", \"MMX\"),\n", "            user=os.environ.get(\"DB_USER\", \"postgres\"),\n", "            password=os.environ.get(\"DB_PASSWORD\", \"something\")\n", "        )\n", "        return conn\n", "    except Exception as e:\n", "        logging.error(f\"Error connecting to database: {str(e)}\")\n", "        return None\n", "\n", "\n", "\n", "def initialize_db_connection():\n", "    \"\"\"Initialize database connection\"\"\"\n", "    return get_db_connection()\n", "\n", "def close_db_connection(conn):\n", "    \"\"\"Close database connection\"\"\"\n", "    if conn:\n", "        try:\n", "            conn.close()\n", "            return True\n", "        except Exception as e:\n", "            logging.error(f\"Error closing database connection: {str(e)}\")\n", "            return False\n", "    return True\n", "def process_dates(df,date_column):\n", "    \"\"\"Process and clean date column in dataframe\"\"\"\n", "    # Make a copy of the original date column for debugging\n", "    df['original_date'] = df[date_column].copy()\n", "    date_format = select_date_format()\n", "    # Apply the custom conversion function\n", "    date_format = select_date_format()  # Your custom function to select format\n", "    df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))\n", "\n", "\n", "    # Print debugging info about the conversion success\n", "    na_count = df[date_column].isna().sum()\n", "    total_rows = len(df)\n", "    print(f\"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted\")\n", "\n", "    if na_count > 0:\n", "        # Show examples of problematic values\n", "        problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]\n", "        print(f\"Examples of problematic date values: {problem_examples}\")\n", "\n", "    # Drop the empty dates\n", "    orig_len = len(df)\n", "    df = df.dropna(subset=[date_column])\n", "    print(f\"Dropped {orig_len - len(df)} rows with invalid dates\")\n", "\n", "    # Drop the debug column\n", "    df = df.drop(columns=['original_date'])\n", "    \n", "    return df,date_format\n", "\n", "\n", "\n", "def get_main_data_columns(file_path):\n", "    df = load_file(file_path)\n", "    \"\"\"Get and validate column names for main data from user input\"\"\"\n", "\n", "    \n", "    # For testing, using hardcoded values\n", "    promo_channels = ['PDE','Copay']\n", "    promo_channels = [col.strip() for col in promo_channels]\n", "    date_column = 'Date'\n", "    id_column = 'ID'\n", "    target_column = 'NRx'\n", "    start_date = '202411'\n", "    end_date = '202512'\n", "    start_date = datetime.datetime.strptime(start_date, '%Y%m')\n", "    end_date = datetime.datetime.strptime(end_date, '%Y%m')\n", "\n", "    \n", "    # Check if required columns exist\n", "    missing_cols = [col for col in [date_column, target_column, id_column] if col not in df.columns]\n", "    if missing_cols:\n", "        error_msg = f\"ERROR: The following required columns are missing from your main data: {', '.join(missing_cols)}\"\n", "        error_msg += f\"\\nAvailable columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "\n", "    # Check if promotional channels exist\n", "    missing_promo_cols = [col for col in promo_channels if col not in df.columns]\n", "    if missing_promo_cols:\n", "        error_msg = f\"ERROR: The following promotional channel columns are missing: {', '.join(missing_promo_cols)}\"\n", "        error_msg += f\"\\nAvailable columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "    \n", "    print(f\"Validated main data columns: {date_column}, {id_column}, {target_column}\")\n", "    print(f\"Promotional channels: {', '.join(promo_channels)}\")\n", "    print(f\"Date range: {start_date} to {end_date}\")\n", "    \n", "    return df,promo_channels, date_column, id_column, target_column, start_date, end_date\n", "\n", "def select_date_format():\n", "    \"\"\"Select and return the date format from user input\"\"\"\n", "    global date_format\n", "    \n", "    print(\"\\nSelect date format from the following options:\")\n", "    print(\"1. MM/DD/YYYY\")\n", "    print(\"2. YYYY-MM-DD\")\n", "    print(\"3. YYYY-MM\")\n", "    print(\"4. MM-YYYY\")\n", "    \n", "    # For production, uncomment to get user input\n", "    # format_choice = input(\"Enter your choice (1-4): \").strip()\n", "    \n", "    # For testing, using a default value\n", "    format_choice = '4'\n", "    \n", "    if format_choice == '1':\n", "        date_format = 'MM/DD/YYYY'\n", "    elif format_choice == '2':\n", "        date_format = 'YYYY-MM-DD'\n", "    elif format_choice == '3':\n", "        date_format = 'YYYY-MM'\n", "    elif format_choice == '4':\n", "        date_format = 'MM-YYYY'\n", "    else:\n", "        print(\"Invalid choice. Defaulting to YYYY-MM-DD.\")\n", "        date_format = 'YYYY-MM-DD'\n", "    \n", "    print(f\"Selected date format: {date_format}\")\n", "    return date_format\n", "\n", "def convert_date(date_val,date_format):\n", "    \"\"\"Convert various date formats to YYYYMM format\"\"\"\n", "    if pd.isna(date_val):\n", "        return np.nan\n", "\n", "    # Convert to string if it's not already\n", "    date_str = str(date_val).strip()\n", "\n", "    try:\n", "        # First, try to parse based on user-selected format\n", "        try:\n", "            if date_format == 'MM/DD/YYYY':\n", "                date_obj = pd.to_datetime(date_str, format='%m/%d/%Y', errors='raise')\n", "            elif date_format == 'YYYY-MM-DD':\n", "                date_obj = pd.to_datetime(date_str, format='%Y-%m-%d', errors='raise')\n", "            elif date_format == 'YYYY-MM':\n", "                date_obj = pd.to_datetime(date_str, format='%Y-%m', errors='raise')\n", "            elif date_format == 'MM-YYYY':\n", "                date_obj = pd.to_datetime(date_str, format='%m-%Y', errors='raise')\n", "            else:\n", "                date_obj = None\n", "            if date_obj is not None:\n", "                return date_obj.replace(day=1)\n", "        except Exception:\n", "            # If specific format parsing fails, continue with fallback options\n", "            pass\n", "        \n", "        # For formats like '20247' (YYYYM) - year 2024, month 7\n", "        if date_str.isdigit() and len(date_str) == 5:\n", "            year = date_str[:4]\n", "            month = date_str[4:5]  # Just one digit\n", "            return f\"{year}{month.zfill(2)}\"  # zfill adds leading zero if needed\n", "\n", "        # For formats like '202407' (YYYYMM)\n", "        elif date_str.isdigit() and len(date_str) == 6:\n", "            year = date_str[:4]\n", "            month = date_str[4:6]\n", "            return f\"{year}{month}\"\n", "\n", "        # For integer timestamps\n", "        elif date_str.isdigit() and len(date_str) > 6:\n", "            # Convert timestamp to datetime\n", "            date_obj = pd.to_datetime(int(date_str), errors='coerce')\n", "            if not pd.isna(date_obj):\n", "                return date_obj.strftime('%Y%m')\n", "\n", "        # Try standard datetime parsing\n", "        date_obj = pd.to_datetime(date_str, errors='coerce')\n", "        if not pd.isna(date_obj):\n", "            return date_obj.strftime('%Y%m')\n", "\n", "        return np.nan\n", "    except:\n", "        return np.nan\n", "\n", "\n", "\n", "def handle_outliers(df, promo_channels, target_column, num_sigmas=3):\n", "    \"\"\"Handle outliers in promotional channels\"\"\"\n", "    all_columns = promo_channels + [target_column]\n", "\n", "    # Store initial sums\n", "    initial_sums = {col: df[col].sum() for col in all_columns if col in df.columns}\n", "    initial_sums_df = pd.DataFrame(initial_sums, index=[\"before\"])\n", "\n", "    # Track indices of all outliers to remove\n", "    outlier_indices = set()\n", "\n", "    # Dictionary to store outlier indices by column\n", "    column_outlier_indices = {}\n", "\n", "    for col in promo_channels:\n", "        if col in df.columns:\n", "            data = df[col]\n", "            mean = np.mean(data)\n", "            std = np.std(data)\n", "\n", "            # Identify outliers\n", "            lower_bound = mean - num_sigmas * std\n", "            upper_bound = mean + num_sigmas * std\n", "            outliers = df[(data < lower_bound) | (data > upper_bound)]\n", "            outlier_idx = outliers.index.tolist()\n", "\n", "            # Store outlier indices for this column\n", "            column_outlier_indices[col] = outlier_idx\n", "\n", "            # Calculate the percentage drop if we remove these outliers\n", "            if outlier_idx:\n", "                col_sum_before = df[col].sum()\n", "                col_sum_after = df.loc[~df.index.isin(outlier_idx), col].sum()\n", "                percentage_drop = ((col_sum_before - col_sum_after) / col_sum_before * 100) if col_sum_before != 0 else 0\n", "\n", "                # Only add to outlier indices if percentage drop is less than 10%\n", "                if percentage_drop <= 10:\n", "                    outlier_indices.update(outlier_idx)\n", "                    print(f\"[{col}] Removed {len(outlier_idx)} outliers (mu ± {num_sigmas}σ) - {percentage_drop:.2f}% drop\")\n", "                else:\n", "                    print(f\"[{col}] Skipping {len(outlier_idx)} outliers - would cause {percentage_drop:.2f}% drop (>10%)\")\n", "\n", "    # Drop all outliers and reset index\n", "    df = df.drop(index=outlier_indices).reset_index(drop=True)\n", " \n", "    # Store final sums\n", "    final_sums = {col: df[col].sum() for col in all_columns if col in df.columns}\n", "    final_sums_df = pd.DataFrame(final_sums, index=[\"after\"])\n", "\n", "    # Combine before/after\n", "    summary_df = pd.concat([initial_sums_df, final_sums_df]).T.reset_index()\n", "    summary_df.columns = [\"channel\", \"before\", \"after\"]\n", "    summary_df[\"Percentage drop\"] = summary_df.apply(\n", "        lambda row: ((row[\"before\"] - row[\"after\"]) / row[\"before\"] * 100) if row[\"before\"] != 0 else 0,\n", "        axis=1\n", "    )\n", "\n", "    print(f\"Total outliers removed: {len(outlier_indices)}\")\n", "    print(f\"Remaining rows: {len(df)}\")\n", "\n", "    print(\"Outlier summary:\")\n", "    print(summary_df)\n", "    \n", "    return df\n", "\n", "\n", "\n", "def add_control_variables(df, id_col, date_col):\n", "    \"\"\"Add control variables to the dataframe\"\"\"\n", "    df['Rtime'] = df.groupby(id_col).cumcount() + 1\n", "    control_variables = []\n", "\n", "    months = pd.to_datetime(df[date_col], errors='coerce').dt.month\n", "    # Uncomment to create month dummies\n", "    # for m in range(1, 13):\n", "    #     df[f'm{m}'] = (months == m).astype(int)\n", "    #     control_variables.append(f'm{m}')\n", "\n", "    max_period = df.groupby(id_col).size().max()\n", "    for t in range(1, max_period + 1):\n", "        df[f'T{t}'] = (df['Rtime'] == t).astype(int)\n", "        control_variables.append(f'T{t}')\n", "    control_variables.append('Rtime')\n", "\n", "    print(f\"Added {len(control_variables)} control variables\")\n", "    return df, control_variables\n", "\n", "#################################\n", "# REGRESSION FUNCTIONS\n", "#################################\n", "def perform_regression(df, promo_columns, target_column, date_column,start_date,end_date):\n", "\n", "    \"\"\"\n", "    Perform regression analysis with the specified promotional columns.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promo_columns (list): List of promotional channel columns to use as predictors\n", "        target_column (str): Target variable name\n", "        date_column (str): Column containing date information\n", "\n", "    Returns:\n", "        pd.DataFrame: Regression results table\n", "    \"\"\"\n", "\n", "    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    try:\n", "        # Make a copy of the input DataFrame to avoid modifying the original\n", "        df = df.copy()\n", "        final_df = df.copy()\n", "\n", "        if not promo_columns:\n", "            logging.error(\"No valid promotional columns selected for regression.\")\n", "            return pd.DataFrame([['Error', 'No valid promotional columns selected.']])\n", "\n", "        # Ensure all promo columns exist in the dataframe\n", "        missing_columns = [col for col in promo_columns if col not in df.columns]\n", "        if missing_columns:\n", "            error_msg = f\"Missing columns in dataframe: {missing_columns}\"\n", "            logging.error(error_msg)\n", "            return pd.DataFrame([['Error', error_msg]])\n", "\n", "        # Prepare data for regression\n", "        X = df[promo_columns]\n", "        y = df[target_column]\n", "        X = sm.add_constant(X)\n", "\n", "        # Fit OLS regression model\n", "        model = sm.OLS(y, X).fit()\n", "        predictions = model.predict(X)\n", "\n", "        # Metrics\n", "        mape = np.mean(np.abs((y - predictions) / y)) * 100\n", "        r_squared = model.rsquared\n", "        adj_r_squared = model.rsquared_adj\n", "        modeled_sales = predictions.sum()\n", "        actual_sales = y.sum()\n", "\n", "        # Initialize an empty list to store result rows instead of empty DataFrame\n", "        result_rows = []\n", "\n", "        # Total modeled activity for impact calculation\n", "        total_activity = sum(df[c].sum() * model.params[c] for c in promo_columns if c in model.params)\n", "        channel_impacts = {}\n", "        for col in promo_columns:\n", "            if col in model.params:\n", "                estimate = model.params[col]\n", "                activity = df[col].sum()\n", "                channel_impacts[col] = estimate * activity\n", "\n", "        # Total impact is the sum of all individual channel impacts\n", "        total_impact = sum(channel_impacts.values())\n", "        for col in promo_columns:\n", "            if col not in model.params:\n", "                continue\n", "\n", "            estimate = model.params[col]\n", "            p_value = model.pvalues[col]\n", "            activity = df[col].sum()\n", "\n", "            # Extract original variable name for linear activity\n", "            parts = col.split(\"_\")\n", "\n", "            if \"Tier\" in parts:\n", "                tier_index = parts.index(\"Tier\")\n", "                name = \"_\".join([parts[0], \"Tier\", parts[tier_index + 1]])\n", "            else:\n", "                if \"Adstock\" in parts:\n", "                    adstock_index = parts.index(\"Adstock\")\n", "                    name = \"_\".join(parts[:adstock_index])\n", "                else:\n", "                    name = parts[0]\n", "\n", "            try:\n", "                linear_activity = df[name].sum()\n", "            except KeyError:\n", "                linear_activity = activity\n", "\n", "            target_sum = df[target_column].sum()\n", "            impact_percentage = ((estimate * activity) / modeled_sales)*100 if modeled_sales != 0 else 0\n", "            effectiveness = (estimate * linear_activity)\n", "\n", "            # Append row to list instead of concatenating DataFrames\n", "            result_rows.append({\n", "                'Channel': col,\n", "                'Estimate': estimate,\n", "                'Impact Percentage': impact_percentage,\n", "                'P-Value': p_value,\n", "                'Effectiveness': effectiveness,\n", "                'Linear Activity': linear_activity,\n", "                'R²': r_squared,\n", "                'Adjusted R²': adj_r_squared,\n", "                'Total Modeled Activity': activity,\n", "                'Modeled Sales': modeled_sales,\n", "                'Actual Sales': actual_sales\n", "            })\n", "\n", "        # Include Intercept (const) in result rows\n", "        if 'const' in model.params:\n", "            estimate = model.params['const']\n", "            p_value = model.pvalues['const']\n", "            impact=estimate*len(df)\n", "            impact_percentage=(impact/modeled_sales)*100\n", "\n", "            result_rows.append({\n", "                'Channel': 'Intercept',\n", "                'Estimate': estimate,\n", "                'Impact Percentage': impact_percentage,\n", "                'P-Value': p_value,\n", "                'Effectiveness': np.nan,\n", "                'Linear Activity': np.nan,\n", "                'R²': r_squared,\n", "                'Adjusted R²': adj_r_squared,\n", "                'Total Modeled Activity': np.nan,\n", "                'Modeled Sales': modeled_sales,\n", "                'Actual Sales': actual_sales\n", "            })\n", "\n", "        # Create result DataFrame from the list of rows after all rows are collected\n", "        result_table = pd.DataFrame(result_rows)\n", "\n", "        logging.info(\"OLS linear regression performed successfully.\")\n", "        logging.info(f\"Final Regression DataFrame head:\\n{result_table.head()}\")\n", "        return result_table\n", "\n", "    except Exception as e:\n", "        logging.error(f\"Error performing OLS linear regression: {e}\")\n", "        import traceback\n", "        logging.error(traceback.format_exc())\n", "        return pd.DataFrame([['Error', str(e)]])\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "9a155288", "metadata": {}, "source": ["# Functions to process data"]}, {"cell_type": "code", "execution_count": 20, "id": "d72fce9f", "metadata": {}, "outputs": [], "source": ["def process_main_data(file_path, model_run_id=None,db_conn=None):\n", "    \"\"\"\n", "    Clean and process the main data and update the PostgreSQL database directly.\n", "    \n", "    Args:\n", "        file_path (str): Path to the input file\n", "        model_run_id (UUID, optional): UUID of the model run to update in the database\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed main dataframe and related metadata\n", "    \"\"\"\n", "\n", "    \n", "    try:\n", "        # Get column names and date format\n", "        df, promo_channels, date_column, id_column, target_column, start_date, end_date = get_main_data_columns(file_path)\n", "        unit_price=get_unit_price()\n", "        # Process dates\n", "        df, date_format = process_dates(df, date_column)\n", "        \n", "        # Ensure numeric columns\n", "        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()\n", "        for col in numeric_columns:\n", "            df[col] = pd.to_numeric(df[col], errors='coerce')\n", "        df = df.drop_duplicates()\n", "        logging.info(\"Data successfully loaded and cleaned.\")\n", "        \n", "        # Create lag variable\n", "        df[f\"{target_column}_lag_1\"] = df.groupby(id_column)[target_column].shift(1).fillna(0)\n", "        df = df.sort_values(by=[id_column, date_column])\n", "        \n", "        # Handle outliers\n", "        df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)\n", "        \n", "        # If model_run_id is provided and we have a connection, update the database directly\n", "        if model_run_id and db_conn:\n", "            try:\n", "                # Create a cursor object\n", "                cursor = db_conn.cursor()\n", "                \n", "                # SQL to update model_run_data table\n", "                sql = \"\"\"\n", "                    UPDATE model_run_data\n", "                    SET \n", "                        date_column = %s,\n", "                        id_column = %s,\n", "                        target_column = %s,\n", "                        promotional_columns = %s,\n", "                        start_date = %s,\n", "                        end_date = %s,\n", "                        date_format = %s,\n", "                        unit_price = %s,\n", "                        updated_at = CURRENT_TIMESTAMP\n", "                    WHERE model_run_id = %s\n", "                \"\"\"\n", "                \n", "                # Execute the update query with the values\n", "                cursor.execute(sql, [\n", "                    date_column,\n", "                    id_column,\n", "                    target_column,\n", "                    promo_channels,  # This should be an array\n", "                    start_date,\n", "                    end_date,\n", "                    date_format,\n", "                    unit_price,\n", "                    model_run_id\n", "                ])\n", "                \n", "                # Commit the transaction\n", "                db_conn.commit()\n", "                \n", "                # Close the cursor (but not the connection)\n", "                cursor.close()\n", "                \n", "                logging.info(f\"Successfully updated model_run_data for model_run_id: {model_run_id}\")\n", "                \n", "            except Exception as e:\n", "                logging.error(f\"Error updating database in process_main_data: {str(e)}\")\n", "                # Try to rollback if possible\n", "                try:\n", "                    db_conn.rollback()\n", "                except:\n", "                    pass\n", "        \n", "        return df, promo_channels, date_column, id_column, target_column, date_format, start_date, end_date\n", "    \n", "    except Exception as e:\n", "        logging.error(f\"Error processing main data: {str(e)}\")\n", "        raise\n", "\n", "def process_spend_data(spends_file_path, model_run_id=None,db_conn=None):\n", "    \"\"\"\n", "    Process spend data and update the PostgreSQL database directly\n", "    \n", "    Args:\n", "        spends_file_path (str): Path to the spend data file\n", "        model_run_id (UUID, optional): UUID of the model run to update in the database\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed spend dataframe\n", "    \"\"\"\n", "\n", "    \n", "    df = load_file(spends_file_path)\n", "    \n", "    # Define spend column names (hardcoded for testing)\n", "    spend_column = 'Spends'\n", "    spend_channel_column = 'Channel'\n", "    \n", "    # Check if spend columns exist in the dataframe\n", "    if spend_column not in df.columns:\n", "        error_msg = f\"ERROR: Column '{spend_column}' not found in the spend dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "        \n", "    if spend_channel_column not in df.columns:\n", "        error_msg = f\"ERROR: Column '{spend_channel_column}' not found in the spend dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "    \n", "    # If model_run_id is provided and we have a database connection, update directly\n", "    if model_run_id and db_conn:\n", "        try:\n", "            # Create a cursor object\n", "            cursor = db_conn.cursor()\n", "            \n", "            # SQL to update model_run_data table\n", "            sql = \"\"\"\n", "                UPDATE model_run_data\n", "                SET \n", "                    spend_channel_column = %s,\n", "                    spend_column = %s,\n", "                    updated_at = CURRENT_TIMESTAMP\n", "                WHERE model_run_id = %s\n", "            \"\"\"\n", "            \n", "            # Execute the update query with the values\n", "            cursor.execute(sql, [\n", "                spend_channel_column,\n", "                spend_column,\n", "                model_run_id\n", "            ])\n", "            \n", "            # Commit the transaction\n", "            db_conn.commit()\n", "            \n", "            # Close the cursor (but not the connection)\n", "            cursor.close()\n", "            \n", "            logging.info(f\"Successfully updated spend data for model_run_id: {model_run_id}\")\n", "            \n", "        except Exception as e:\n", "            logging.error(f\"Error updating database in process_spend_data: {str(e)}\")\n", "            # Try to rollback if possible\n", "            try:\n", "                db_conn.rollback()\n", "            except:\n", "                pass\n", "    \n", "    return df, spend_column, spend_channel_column\n", "\n", "def process_historical_data(historical_file_path, model_run_id=None,db_conn=None):\n", "    \"\"\"\n", "    Process historical impact data and update the PostgreSQL database directly\n", "    \n", "    Args:\n", "        historical_file_path (str): Path to the historical data file\n", "        model_run_id (UUID, optional): UUID of the model run to update in the database\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed historical dataframe\n", "    \"\"\"\n", "\n", "    df = load_file(historical_file_path)\n", "    \n", "    # Define historical column names (hardcoded for testing)\n", "    historical_impact = 'contributions%'\n", "    historical_channel_column = 'Channel'\n", "    \n", "    # Check if historical columns exist in the dataframe\n", "    if historical_impact not in df.columns:\n", "        error_msg = f\"ERROR: Column '{historical_impact}' not found in the historical dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "        \n", "    if historical_channel_column not in df.columns:\n", "        error_msg = f\"ERROR: Column '{historical_channel_column}' not found in the historical dataframe. Available columns: {', '.join(df.columns)}\"\n", "        logging.error(error_msg)\n", "        print(error_msg)\n", "        sys.exit(1)  # Exit immediately\n", "    \n", "    # Additional processing specific to historical data can be added here\n", "    print(f\"Processed historical data with columns: {', '.join(df.columns)}\")\n", "    \n", "    # If model_run_id is provided and we have a database connection, update directly\n", "    if model_run_id and db_conn:\n", "        try:\n", "            # Create a cursor object\n", "            cursor = db_conn.cursor()\n", "            \n", "            # SQL to update model_run_data table\n", "            sql = \"\"\"\n", "                UPDATE model_run_data\n", "                SET \n", "                    historical_channel_column = %s,\n", "                    historical_impact = %s,\n", "                    updated_at = CURRENT_TIMESTAMP\n", "                WHERE model_run_id = %s\n", "            \"\"\"\n", "            \n", "            # Execute the update query with the values\n", "            cursor.execute(sql, [\n", "                historical_channel_column,\n", "                historical_impact,\n", "                model_run_id\n", "            ])\n", "            \n", "            # Commit the transaction\n", "            db_conn.commit()\n", "            \n", "            # Close the cursor (but not the connection)\n", "            cursor.close()\n", "            \n", "            logging.info(f\"Successfully updated historical data for model_run_id: {model_run_id}\")\n", "            \n", "        except Exception as e:\n", "            logging.error(f\"Error updating database in process_historical_data: {str(e)}\")\n", "            # Try to rollback if possible\n", "            try:\n", "                db_conn.rollback()\n", "            except:\n", "                pass\n", "    \n", "    return df, historical_impact, historical_channel_column"]}, {"cell_type": "markdown", "id": "999828fc", "metadata": {}, "source": ["# Selecct best Adstock functions"]}, {"cell_type": "code", "execution_count": null, "id": "d7f80c71", "metadata": {}, "outputs": [], "source": ["def get_adstock_ranges_from_gemini(promo_channels):\n", "    \"\"\"Use Gemini API to get recommended adstock ranges for different promotion types\"\"\"\n", "    prompt = f\"\"\"\n", "    For the following promotional channels in pharmaceutical marketing, provide the ideal adstock decay rate ranges.\n", "\n", "    For adstock settings, categorize promotions into two groups: Personal Promotions and Non-Personal Promotions. Set the adstock range for Personal Promotions (including PDE, Call, Call Activity, and TV)\n", "    between 70-80. For Non-Personal Promotions (such as Display and Banner), set the adstock range between 20-50.\n", "    Only Speaker programs or conferences can have adstock range from 70-90\n", "    Personal promotions include: Calls, PDE, TV etc\n", "    Non-Personal include: Banners ,Headlines ,Ads etc\n", "    Use the web and gather additional insights on these promotional activities, their impact on sales,\n", "    and industry best practices. Incorporate any relevant findings to refine adstock selection and improve model accuracy.\n", "\n", "    Categorize each channel as either \"Personal Promotion\", \"Non-Personal Promotion\", or \"Other\" and provide a\n", "    recommended min and max adstock decay rate (as percentages between 10 and 90).\n", "\n", "    Channels: {', '.join(promo_channels)}\n", "\n", "    Return results in JSON format with this structure:\n", "    {{\n", "        \"Channel_Name\": {{\n", "            \"type\": \"Personal Promotion|Non-Personal Promotion|Other\",\n", "            \"min_adstock\": 10-90,\n", "            \"max_adstock\": 10-90\n", "        }},\n", "        ...\n", "    }}\n", "\n", "    Ensure min_adstock is less than max_adstock. Base your ranges on typical pharmaceutical marketing benchmarks.\n", "    \"\"\"\n", "\n", "    try:\n", "        response = model.generate_content(prompt)\n", "        raw_output = response.text.strip()\n", "        raw_output = raw_output.replace(\"```json\", \"\").replace(\"```\", \"\").strip()\n", "        adstock_ranges = json.loads(raw_output)\n", "\n", "        logging.info(f\"Retrieved adstock ranges from Gemini API: {adstock_ranges}\")\n", "        return adstock_ranges\n", "    except Exception as e:\n", "        logging.error(f\"Error getting adstock ranges from Gemini: {e}\")\n", "        # Fallback default ranges if API call fails\n", "        default_ranges = {}\n", "        for channel in promo_channels:\n", "            default_ranges[channel] = {\n", "                \"type\": \"Other\",\n", "                \"min_adstock\": 10,\n", "                \"max_adstock\": 90\n", "            }\n", "        return default_ranges\n", "\n", "def get_channel_adstock_range(promo_col):\n", "    \"\"\"\n", "    Get recommended adstock range for a specific promotional channel.\n", "\n", "    Args:\n", "        promo_col (str): Name of the promotional channel column\n", "\n", "    Returns:\n", "        dict: Dictionary with channel type and min/max adstock values\n", "    \"\"\"\n", "    # Get adstock ranges for all promo channels\n", "    all_adstock_ranges = get_adstock_ranges_from_gemini([promo_col])\n", "    print(f\"All adstock ranges:{all_adstock_ranges}\")\n", "    # Get recommended range for this channel\n", "    channel_range = all_adstock_ranges.get(promo_col, {\n", "        \"type\": \"Other\",\n", "        \"min_adstock\": 10,\n", "        \"max_adstock\": 90\n", "    })\n", "\n", "    return channel_range\n", "def get_transformation_functions():\n", "    def safe_power(x, exp):\n", "        return np.power(np.clip(x, 0, None), exp)\n", "\n", "    def safe_log(x):\n", "        return np.log1p(np.clip(x, 0, None))\n", "\n", "    return {\n", "        \"Log\": safe_log,\n", "        \"Root1\": lambda x: safe_power(x, 1/10),\n", "        \"Root2\": lambda x: safe_power(x, 2/10),\n", "        \"Root3\": lambda x: safe_power(x, 3/10),\n", "        \"Root4\": lambda x: safe_power(x, 4/10),\n", "        \"Root5\": lambda x: safe_power(x, 5/10),\n", "        \"Root6\": lambda x: safe_power(x, 6/10),\n", "        \"Root7\": lambda x: safe_power(x, 7/10),\n", "        \"Root8\": lambda x: safe_power(x, 8/10),\n", "        \"Root9\": lambda x: safe_power(x, 9/10)\n", "        # \"Sigmoid\": lambda x: 1 / (1 + np.exp(-x))  # Optional, safe by design\n", "    }\n", "\n", "def apply_adstock(df, promo_col, id_col, adstock_rate):\n", "    \"\"\"\n", "    Apply adstock transformation to a promotional channel with specified rate.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promo_col (str): Name of promotional column\n", "        id_col (str): Column that identifies time series groups\n", "        adstock_rate (int): Adstock rate (0-100)\n", "\n", "    Returns:\n", "        tuple: (Column name, Series with adstocked values)\n", "    \"\"\"\n", "    # print(f\"apply_adedstock length: {len(df)}\")\n", "    rate = adstock_rate / 100\n", "    adstocked = []\n", "\n", "    for _, group in df.groupby(id_col):\n", "        cumulative = 0\n", "        group_adstocked = []\n", "        for value in group[promo_col]:\n", "            cumulative = value + rate * cumulative\n", "            group_adstocked.append(cumulative)\n", "        adstocked.extend(group_adstocked)\n", "\n", "    adstock_col = f\"{promo_col}_Adstock_{adstock_rate}\"\n", "    return adstock_col, pd.Series(adstocked, index=df.index)\n", "\n", "def apply_transformation(series, transform_name, transform_func, base_col_name):\n", "    \"\"\"\n", "    Apply transformation function to a series.\n", "\n", "    Args:\n", "        series (pd.Series): Data to transform\n", "        transform_name (str): Name of transformation\n", "        transform_func (callable): Transformation function\n", "        base_col_name (str): Base column name\n", "\n", "    Returns:\n", "        tuple: (Column name, Transformed series)\n", "    \"\"\"\n", "    # print(f\"apply_transformation length: {len(df)}\")\n", "    transformed_col = f\"{base_col_name}_{transform_name}\"\n", "    transformed_series = transform_func(series)\n", "    return transformed_col, transformed_series\n", "def fit_regression(df, feature_col,date_column, target_column,start_date,end_date, control_variables=None):\n", "    \"\"\"\n", "    Fit regression model and return key metrics.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        feature_col (str): Feature column name\n", "        target_column (str): Target column name\n", "        control_variables (list): Additional control variables\n", "\n", "    Returns:\n", "        dict: Regression metrics or None if error\n", "    \"\"\"\n", "\n", "    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    # print(f\"Fit_Regression length: {len(df)}\")\n", "    if control_variables is None:\n", "        control_variables = []\n", "\n", "    try:\n", "        features = [feature_col] +[f\"{target_column}_lag_1\"]\n", "        # features = [feature_col] + control_variables +[f\"{target_column}_lag_1\"]\n", "        X = df[features].copy()\n", "        X = sm.add_constant(X)\n", "        y = df[target_column]\n", "\n", "        model = sm.OLS(y, X).fit()\n", "\n", "        return {\n", "            'r_squared': model.rsquared,\n", "            'p_value': model.pvalues[feature_col],\n", "            'aic': model.aic\n", "        }\n", "    except Exception as e:\n", "        logging.error(f\"Error fitting model for {feature_col}: {e}\")\n", "        return None\n", "    \n", "def normalize_metric(values, higher_is_better=True):\n", "    \"\"\"\n", "    Normalize metric values to 0-1 range.\n", "\n", "    Args:\n", "        values (array): Metric values\n", "        higher_is_better (bool): Whether higher values are better\n", "\n", "    Returns:\n", "        array: Normalized values\n", "    \"\"\"\n", "    if len(values) <= 1:\n", "        return np.ones_like(values)\n", "\n", "    min_val = np.min(values)\n", "    max_val = np.max(values)\n", "\n", "    if max_val == min_val:\n", "        return np.ones_like(values)\n", "\n", "    if higher_is_better:\n", "        return (values - min_val) / (max_val - min_val)\n", "    else:\n", "        return (max_val - values) / (max_val - min_val)\n", "def calculate_composite_score_transformation(results_df):\n", "    \"\"\"\n", "    Calculate composite score based on metrics and business rules.\n", "\n", "    Args:\n", "        results_df (pd.DataFrame): DataFrame with model results\n", "\n", "    Returns:\n", "        pd.DataFrame: DataFrame with scores added\n", "    \"\"\"\n", "    # Normalize metrics\n", "    results_df['r_squared_norm'] = normalize_metric(results_df['r_squared'].values, higher_is_better=True)\n", "    results_df['p_value_norm'] = normalize_metric(results_df['p_value'].values, higher_is_better=False)\n", "    results_df['aic_norm'] = normalize_metric(results_df['aic'].values, higher_is_better=False)\n", "\n", "    # Apply business score based on recommended range\n", "    results_df['business_score'] = results_df['in_range'].apply(lambda x: 1.0 if x else 0.5)\n", "\n", "    # Apply weights and calculate composite score\n", "    # Mathematical metrics (100%)\n", "    # R²: 40%, p-value: 20%, AIC: 40%\n", "    results_df['math_score'] = (\n", "        0.4 * results_df['r_squared_norm'] +\n", "        0.2 * results_df['p_value_norm'] +\n", "        0.4 * results_df['aic_norm']\n", "    )\n", "\n", "    # Final composite score - business score can boost combinations within recommended range\n", "    results_df['final_score'] = 0.5*results_df['math_score'] +0.5* results_df['business_score']\n", "\n", "    return results_df\n", "def select_best_adstock_and_transformation(df, promo_col, id_col, target_column,date_column,start_date,end_date,control_variables=None):\n", "    global temp_df, global_feature_df\n", "   \n", "    \"\"\"\n", "    Select the top 3 adstock rate and transformation combinations for a promotional channel\n", "    using composite scoring of multiple metrics. Include control variables in the regression.\n", "\n", "    Optimized to avoid dataframe fragmentation.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promo_col (str): Name of promotional column\n", "        id_col (str): Column that identifies time series groups\n", "        target_column (str): Target variable name\n", "        control_variables (list): Additional control variables to include in regression\n", "\n", "    Returns:\n", "        tuple: (Updated dataframe, list of top 3 transformed column names)\n", "    \"\"\"\n", "    import gc\n", "    print(f\"\\nSelecting top 3 adstock and transformation combinations for: {promo_col}\")\n", "\n", "    \n", "\n", "    # Get channel info\n", "    channel_range = get_channel_adstock_range(promo_col)\n", "    min_adstock = channel_range[\"min_adstock\"]\n", "    max_adstock = channel_range[\"max_adstock\"]\n", "    channel_type = channel_range[\"type\"]\n", "\n", "    print(f\"Channel type: {channel_type}, Recommended adstock range: {min_adstock}% to {max_adstock}%\")\n", "\n", "    # Get transformation functions\n", "    transformations = get_transformation_functions()\n", "\n", "    # Variables to store results and new columns to be added\n", "    results = []\n", "    new_columns = {}\n", "    new_global_columns = {}  # To store all columns for global_feature_df\n", "\n", "    \n", "\n", "    # Iterate over all adstock rates\n", "    for adstock_rate in range(10, 100, 10):\n", "        # Apply adstock\n", "        temp_df = df.copy()\n", "        adstock_col, adstocked_series = apply_adstock(df, promo_col, id_col, adstock_rate)\n", "        new_columns[adstock_col] = adstocked_series\n", "        temp_df[adstock_col] = adstocked_series\n", "\n", "        # Store for later batch addition to global_feature_df\n", "        new_global_columns[adstock_col] = adstocked_series\n", "\n", "        # Check if adstock rate is within recommended range (business context)\n", "        in_recommended_range = min_adstock <= adstock_rate <= max_adstock\n", "\n", "        # Apply all transformations to this adstocked column\n", "        for transform_name, transform_func in transformations.items():\n", "            transformed_col, transformed_series = apply_transformation(\n", "                temp_df[adstock_col], transform_name, transform_func, adstock_col\n", "            )\n", "            new_columns[transformed_col] = transformed_series\n", "            temp_df[transformed_col] = transformed_series\n", "\n", "            # Store for later batch addition to global_feature_df\n", "            new_global_columns[transformed_col] = transformed_series\n", "\n", "            # Fit regression and get metrics\n", "            metrics = fit_regression(temp_df, transformed_col,date_column,target_column,start_date,end_date, control_variables)\n", "\n", "            if metrics:\n", "                # Add to results\n", "                results.append({\n", "                    'adstock_rate': adstock_rate,\n", "                    'transform': transform_name,\n", "                    'col_name': transformed_col,\n", "                    'r_squared': metrics['r_squared'],\n", "                    'p_value': metrics['p_value'],\n", "                    'aic': metrics['aic'],\n", "                    'in_range': in_recommended_range\n", "                })\n", "    del temp_df\n", "    # Add all new columns to global_feature_df at once to avoid fragmentation\n", "    # if new_global_columns:\n", "    #     global_feature_df = pd.concat([global_feature_df, pd.DataFrame(new_global_columns)], axis=1)\n", "    #     # Make a copy to defragment after all additions\n", "    #     global_feature_df = global_feature_df.copy()\n", "\n", "    # Check if we have valid results\n", "    if not results:\n", "        logging.error(f\"No valid models found for {promo_col}\")\n", "        return df, [], []\n", "\n", "    # Convert results to DataFrame\n", "    results_df = pd.DataFrame(results)\n", "\n", "    # Calculate scores\n", "    results_df = calculate_composite_score_transformation(results_df)\n", "\n", "\n", "    # Get the top 3 combinations\n", "    results_df = results_df.sort_values('final_score', ascending=False)\n", "    top_3_rows = results_df.head(3)\n", "\n", "    # Create lists to store top 3 columns\n", "    top_adstock_cols = []\n", "    top_transformed_cols = []\n", "\n", "    # Print details of the top 3 combinations\n", "    print(f\"\\nTop 3 combinations selected for {promo_col}:\")\n", "\n", "    for i, (_, row) in enumerate(top_3_rows.iterrows(), 1):\n", "        adstock_col = f\"{promo_col}_Adstock_{int(row['adstock_rate'])}\"\n", "        transformed_col = row['col_name']\n", "\n", "        top_adstock_cols.append(adstock_col)\n", "        top_transformed_cols.append(transformed_col)\n", "\n", "        print(f\"\\nCombination #{i}:\")\n", "        print(f\"- Adstock rate: {int(row['adstock_rate'])}%\")\n", "        print(f\"- Transformation: {row['transform']}\")\n", "        print(f\"- R²: {row['r_squared']:.4f}\")\n", "        print(f\"- p-value: {row['p_value']:.4f}\")\n", "        print(f\"- AIC: {row['aic']:.4f}\")\n", "        print(f\"- In recommended range: {'Yes' if row['in_range'] else 'No'}\")\n", "        print(f\"- Final score: {row['final_score']:.4f}\")\n", "\n", "    # Only add the necessary columns to the original dataframe using concat\n", "    # This includes all columns needed for the top 3 combinations\n", "    columns_to_add = {}\n", "    for col in set(top_adstock_cols + top_transformed_cols):\n", "        columns_to_add[col] = new_columns[col]\n", "\n", "    new_df = pd.concat([df, pd.DataFrame(columns_to_add)], axis=1)\n", "\n", "    return new_df, top_adstock_cols, top_transformed_cols,channel_range\n", "\n", "def process_promo_channels(df,promo_channels,target_column,id_column,date_column,start_date,end_date,control_variables):\n", "    \"\"\"Process each promotional channel to find best transformations\"\"\"\n", "   \n", "    \n", "    # Dictionary to store top transformed columns for each promo channel\n", "    transformed_channels_by_promo = {}\n", "    adstocked_channels_by_promo = {}\n", "    adstock_range_channel = {}\n", "    all_transformed_features = []\n", "\n", "    for promo_col in promo_channels:\n", "        print(f\"\\nProcessing promotional channel: {promo_col}\")\n", "\n", "        # Use the modified method to get top 3 combinations with control variables\n", "        df, best_adstock_cols, best_transformed_cols, adstock_range = select_best_adstock_and_transformation(\n", "            df, promo_col, id_column, target_column,date_column, start_date, end_date, control_variables\n", "        )\n", "\n", "        print(f\"- Best Adstock columns: {', '.join(best_adstock_cols)}\")\n", "        print(f\"- Best Transformed columns: {', '.join(best_transformed_cols)}\")\n", "\n", "        # Store the top 3 transformed columns for this promo channel\n", "        adstocked_channels_by_promo[promo_col] = best_adstock_cols\n", "        adstock_range_channel[promo_col] = adstock_range\n", "        transformed_channels_by_promo[promo_col] = best_transformed_cols\n", "        all_transformed_features.extend(best_transformed_cols)\n", "\n", "    if not all_transformed_features:\n", "        return False, df, {}, {}, {}, [] \n", "    \n", "    return True,df,transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_transformed_features"]}, {"cell_type": "code", "execution_count": 43, "id": "aa434997", "metadata": {}, "outputs": [], "source": ["def get_benchmark_values(promo_channels, historical_df,historical_channel_column,historical_impact):\n", "    \"\"\"\n", "    Gets benchmark values for promotional channels, asking only once per base channel\n", "    and applying that value to all its transformations.\n", "\n", "    Args:\n", "        promo_channels: List of transformed promotional channel names\n", "\n", "    Returns:\n", "        Dictionary mapping each transformed channel to its benchmark value\n", "    \"\"\"\n", "    \n", "    # Identify base channel names and group transformations\n", "    base_channels = {}\n", "\n", "    # Common transformation indicators that would appear in transformed channel names\n", "    transform_indicators = ['_adstock', '_decay', '_power', '_lag', '_transform', '_delayed']\n", "\n", "    for channel in promo_channels:\n", "        # Find if this is a transformed channel by looking for transformation indicators\n", "        is_transformed = False\n", "        base_name = channel\n", "\n", "        for indicator in transform_indicators:\n", "            if indicator.lower() in channel.lower():\n", "                is_transformed = True\n", "                # Extract the base name (everything before the transformation indicator)\n", "                parts = channel.lower().split(indicator.lower(), 1)\n", "                base_name = parts[0].rstrip('_')\n", "                break\n", "\n", "        # If no transformation indicator found, it's likely a base channel itself\n", "        if not is_transformed:\n", "            # For channels with format like \"Channel_Adstock10\", try to detect numbers\n", "            numeric_split = re.match(r'(.+?)(\\d+)$', channel)\n", "            if numeric_split:\n", "                base_name = numeric_split.group(1).rstrip('_')\n", "\n", "        # Store in our dictionary\n", "        if base_name not in base_channels:\n", "            base_channels[base_name] = []\n", "        base_channels[base_name].append(channel)\n", "\n", "    # Get benchmark values for each base channel\n", "    typical_values = {}\n", "    \n", "    d={str(k).lower(): v for k, v in zip(historical_df[historical_channel_column], historical_df[historical_impact])}\n", "    \n", "\n", "    # Ask for benchmark values only once per base channel\n", "    for base_name, transformed_channels in base_channels.items():\n", "        try:\n", "            # benchmark = float(input(f\"Enter benchmark for channel: {base_name}\"))\n", "            benchmark=d[base_name]\n", "\n", "            # Apply this benchmark to all transformations of this base channel\n", "            for channel in transformed_channels:\n", "                typical_values[channel] = benchmark\n", "\n", "        except ValueError:\n", "            print(f\"Invalid input for {base_name}. Using default value of 0.\")\n", "            for channel in transformed_channels:\n", "                typical_values[channel] = 0.0\n", "\n", "    return typical_values\n", "\n", "def filter_dataframe_by_date(df, date_column, start_date, end_date):\n", "    \"\"\"Filter dataframe by start and end dates\"\"\"\n", "   \n", "    \n", "    print(\"\\nSelecting optimal channel combination...\")\n", "    print(f\"before optimal channel selection {len(df)}\")\n", "\n", "    # Filter the dataframe before optimal channel selection\n", "    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "84eac2fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "252f0528", "metadata": {}, "source": ["# Params setup"]}, {"cell_type": "code", "execution_count": 52, "id": "89605493", "metadata": {}, "outputs": [], "source": ["def setup_parameters(date_column, id_column, target_column, promo_channels, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact):\n", "    \"\"\"Set up the parameters dictionary for modeling\"\"\"\n", "    global params\n", "    \n", "    params = {\n", "        'main_dataset': {\n", "            'date_column': date_column,\n", "            'id_column': id_column,\n", "            'target_column': target_column,\n", "            'promo_channels': promo_channels,\n", "            'normalization_method': 'Percentile',\n", "            'population_column': None,\n", "            'start_date': start_date,\n", "            'end_date': end_date,\n", "            'data_level': 'HCP'\n", "        },\n", "        'spend_dataset': {\n", "            'channel_column': spend_channel_column,\n", "            'spend_column': spend_column\n", "        },\n", "        'historical_dataset': {\n", "            'channel_column': historical_channel_column,\n", "            'historical_impact': historical_impact\n", "        }\n", "    }\n", "    \n", "    return params"]}, {"cell_type": "markdown", "id": "625a5615", "metadata": {}, "source": ["# select optimal channels"]}, {"cell_type": "code", "execution_count": 73, "id": "c7a18277", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import statsmodels.api as sm\n", "import itertools\n", "import logging\n", "import traceback\n", "\n", "def evaluate_model(df, features, target_column, benchmark_values=None):\n", "    \"\"\"\n", "    Evaluate linear regression model with given features.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input data\n", "        features (list): List of feature columns\n", "        target_column (str): Target variable name\n", "        benchmark_values (dict): Dictionary of benchmark values\n", "\n", "    Returns:\n", "        dict: Dictionary of model metrics\n", "    \"\"\"\n", "    # Prepare data\n", "    X = df[features].dropna()\n", "    y = df.loc[X.index, target_column]\n", "\n", "    if len(X) < len(features) + 2:\n", "        print(f\"  Not enough data points ({len(X)}) for {len(features)} features\")\n", "        return None\n", "\n", "    # Add constant for intercept\n", "    X_with_const = sm.add_constant(X)\n", "\n", "    try:\n", "        # Fit model\n", "        model = sm.OLS(y, X_with_const).fit()\n", "\n", "        # Get coefficients\n", "        coefficients = model.params.drop('const').to_dict() if 'const' in model.params else model.params.to_dict()\n", "        target_sum=df[target_column].sum()\n", "\n", "        for col in features:\n", "          estimate = model.params[col]\n", "          activity = df[col].sum()\n", "\n", "          impact_percentage = ((estimate * activity) / target_sum) if target_sum != 0 else 0\n", "\n", "        # Calculate p-values for features\n", "        p_values = model.pvalues.drop('const').to_dict() if 'const' in model.pvalues else model.pvalues.to_dict()\n", "        avg_p_value = np.mean(list(p_values.values()))\n", "\n", "        # Calculate benchmark deviations if benchmarks provided\n", "        benchmark_deviations = {}\n", "        if benchmark_values:\n", "            for feature in coefficients:\n", "                if feature in benchmark_values:\n", "                    benchmark_val = benchmark_values[feature]\n", "                    model_val = impact_percentage\n", "                    relative_deviation = (model_val - benchmark_val) / ((benchmark_val) + 1e-6)\n", "                    benchmark_deviations[feature] = relative_deviation\n", "\n", "        avg_benchmark_deviation = np.mean(list(benchmark_deviations.values())) if benchmark_deviations else 0\n", "\n", "        # Return metrics\n", "        return {\n", "            'R2': model.rsquared,\n", "            'adj_R2': model.rsquared_adj,\n", "            'AIC': model.aic,\n", "            'BIC': model.bic,\n", "            'coefficients': coefficients,\n", "            'p_values': p_values,\n", "            'avg_p_value': avg_p_value,\n", "            'benchmark_deviations': benchmark_deviations,\n", "            'avg_benchmark_deviation': avg_benchmark_deviation\n", "        }\n", "\n", "    except Exception as e:\n", "        print(f\"  Error evaluating model: {str(e)}\")\n", "        return None\n", "\n", "\n", "\n", "def calculate_incremental_impact(baseline_model, augmented_model):\n", "    \"\"\"\n", "    Calculate the impact of adding a new variable to the model.\n", "\n", "    Args:\n", "        baseline_model (dict): Metrics from baseline model\n", "        augmented_model (dict): Metrics from augmented model (with additional variable)\n", "\n", "    Returns:\n", "        float: Incremental impact score\n", "    \"\"\"\n", "    if not baseline_model or not augmented_model:\n", "        return 0.0\n", "\n", "    baseline_coefs = baseline_model['coefficients']\n", "    augmented_coefs = augmented_model['coefficients']\n", "\n", "    # Get common coefficients\n", "    common_features = set(baseline_coefs.keys()) & set(augmented_coefs.keys())\n", "\n", "    changes = []\n", "    epsilon = 1e-6  # Small value to avoid division by zero\n", "\n", "    for feat in common_features:\n", "        baseline_val = baseline_coefs[feat]\n", "        augmented_val = augmented_coefs[feat]\n", "\n", "        relative_change = (augmented_val - baseline_val) / ((baseline_val) + epsilon)\n", "        changes.append(relative_change)\n", "\n", "    # Return average relative change\n", "    return np.mean(changes) if changes else 0.0\n", "def normalize_metric_channel(value, min_val, max_val, higher_is_better=True):\n", "    \"\"\"\n", "    Normalize a metric to a 0-1 scale.\n", "\n", "    Args:\n", "        value (float): Metric value\n", "        min_val (float): Minimum value in range\n", "        max_val (float): Maximum value in range\n", "        higher_is_better (bool): Whether higher values are better\n", "\n", "    Returns:\n", "        float: Normalized value\n", "    \"\"\"\n", "    if max_val <= min_val:\n", "        return 0.5  # De<PERSON><PERSON> if range is invalid\n", "\n", "    # Clip value to range\n", "    value = max(min_val, min(max_val, value))\n", "\n", "    # Normalize\n", "    if higher_is_better:\n", "        return (value - min_val) / (max_val - min_val)\n", "    else:\n", "        return 1 - ((value - min_val) / (max_val - min_val))\n", "\n", "def calculate_composite_score(metrics, ranges, weights):\n", "    \"\"\"\n", "    Calculate composite score based on multiple metrics.\n", "\n", "    Args:\n", "        metrics (dict): Model evaluation metrics\n", "        ranges (dict): Min-max ranges for each metric\n", "        weights (dict): Weight for each metric in final score\n", "\n", "    Returns:\n", "        float: Composite score\n", "    \"\"\"\n", "    # Normalize metrics\n", "    r2_norm = normalize_metric_channel(metrics['R2'], *ranges['R2'], higher_is_better=True)\n", "    p_norm = normalize_metric_channel(metrics['avg_p_value'], *ranges['p_value'], higher_is_better=False)\n", "    aic_norm = normalize_metric_channel(metrics['AIC'], *ranges['AIC'], higher_is_better=False)\n", "\n", "    # if 'incremental_impact' in metrics:\n", "    #     # Lower incremental impact is better (less change to existing coefficients)\n", "    #     incremental_norm = normalize_metric_channel(metrics['incremental_impact'],\n", "    #                                      *ranges['incremental'],\n", "    #                                      higher_is_better=False)\n", "    # else:\n", "    #     incremental_norm = 0.5  # Default if not available\n", "\n", "    # Calculate benchmark score\n", "    if 'avg_benchmark_deviation' in metrics:\n", "        benchmark_score = max(0, (metrics['avg_benchmark_deviation'] / ranges['benchmark_deviation'][1]))\n", "    else:\n", "        benchmark_score = 0.5  # Default if not available\n", "\n", "    # Calculate composite score\n", "    composite_score = (\n", "        weights['R2'] * r2_norm +\n", "        weights['p_value'] * p_norm +\n", "        weights['AIC'] * aic_norm +\n", "        # weights['incremental'] * incremental_norm +\n", "        weights['benchmark'] * benchmark_score\n", "    )\n", "\n", "    return composite_score\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "ca4f9f22", "metadata": {}, "source": ["# Optimal Channel Selection"]}, {"cell_type": "code", "execution_count": 74, "id": "ce9230ec", "metadata": {}, "outputs": [], "source": ["def select_optimal_channels(df, transformed_channels_by_promo, target_column, control_variables=None,\n", "                           benchmark_values=None):\n", "    import numpy as np\n", "    import pandas as pd\n", "    import copy\n", "\n", "    print(\"\\nSelecting optimal channel combination using one-variable-at-a-time optimization...\")\n", "\n", "    # Initialize control variables if not provided\n", "    if control_variables is None:\n", "        control_variables = []\n", "\n", "    # Define ranges for normalization\n", "    ranges = {\n", "        'R2': (0.5, 0.95),  # Range for R²\n", "        'p_value': (0.01, 0.1),  # Range for p-values\n", "        'AIC': (df[target_column].min(), df[target_column].max() * 2),  # Range for AIC\n", "        'benchmark_deviation': (0, 0.5)  # Range for benchmark deviation\n", "    }\n", "\n", "    # Define weights for composite score\n", "    weights = {\n", "        'R2': 0.20,        # 20% weight on R²\n", "        'p_value': 0.20,   # 20% weight on p-value\n", "        'AIC': 0.10,       # 10% weight on AIC\n", "        'benchmark': 0.50  # 50% weight on business benchmark\n", "    }\n", "\n", "    # Results storage for all tested combinations\n", "    all_results = []\n", "\n", "    # Start with baseline model (only control variables)\n", "    # base_features = control_variables.copy() + [f\"{target_column}_lag_1\"]\n", "    base_features = [f\"{target_column}_lag_1\"]\n", "\n", "    # Get promo channels\n", "    promo_channels = list(transformed_channels_by_promo.keys())\n", "    print(f\"Evaluating {len(promo_channels)} promotional channels using one-variable-at-a-time optimization...\")\n", "\n", "    # Initialize with first transformation for each channel\n", "    current_best_transforms = {}\n", "    for channel in promo_channels:\n", "        if transformed_channels_by_promo[channel]:  # Ensure there's at least one transformation\n", "            current_best_transforms[channel] = transformed_channels_by_promo[channel][0]\n", "\n", "    # One variable at a time optimization\n", "    for channel_idx, current_channel in enumerate(promo_channels):\n", "        print(f\"\\nTuning channel {channel_idx+1}/{len(promo_channels)}: {current_channel}\")\n", "\n", "        # Get transformations for this channel\n", "        channel_transforms = transformed_channels_by_promo[current_channel]\n", "        print(f\"Testing {len(channel_transforms)} transformations for {current_channel}...\")\n", "\n", "        # Track best for this channel\n", "        channel_best_score = -np.inf\n", "        channel_best_transform = None\n", "        channel_best_metrics = None\n", "\n", "        # Try each transformation for this channel\n", "        for transform_idx, transform in enumerate(channel_transforms):\n", "            # Build feature set with current transformation for this channel and best for others\n", "            feature_set = base_features.copy()\n", "            for other_channel, best_transform in current_best_transforms.items():\n", "                if other_channel == current_channel:\n", "                    feature_set.append(transform)  # Use current transformation being tested\n", "                else:\n", "                    feature_set.append(best_transform)  # Use best transformation for other channels\n", "\n", "            print(f\"  Testing transformation {transform_idx+1}/{len(channel_transforms)}: {transform}\")\n", "\n", "            # Evaluate this feature set\n", "            print(f\"Feature set{feature_set}\")\n", "            metrics = evaluate_model(df, feature_set, target_column, benchmark_values)\n", "\n", "            if not metrics:\n", "                print(f\"    Skipping invalid combination with {transform}\")\n", "                continue\n", "\n", "            # Calculate composite score\n", "            composite_score = calculate_composite_score(metrics, ranges, weights)\n", "            metrics['composite_score'] = composite_score\n", "\n", "            # Store result\n", "            result = {\n", "                'channel': current_channel,\n", "                'transformation': transform,\n", "                'features': feature_set.copy(),  # Store actual feature list instead of string\n", "                'features_str': str(feature_set),  # Keep string version for display\n", "                'R2': metrics['R2'],\n", "                'AIC': metrics['AIC'],\n", "                'avg_p_value': metrics['avg_p_value'],\n", "                'avg_benchmark_deviation': metrics.get('avg_benchmark_deviation', 0),\n", "                'composite_score': composite_score\n", "            }\n", "            all_results.append(result)\n", "\n", "            # Check if this is best for current channel\n", "            if composite_score > channel_best_score:\n", "                channel_best_score = composite_score\n", "                channel_best_transform = transform\n", "                channel_best_metrics = metrics.copy()\n", "                print(f\"    New best for channel {current_channel}! Score: {composite_score:.4f}\")\n", "                print(f\"    R²: {metrics['R2']:.4f}, AIC: {metrics['AIC']:.2f}\")\n", "\n", "        # Update the best transformation for this channel\n", "        if channel_best_transform:\n", "            print(f\"\\nBest transformation for {current_channel}: {channel_best_transform}\")\n", "            print(f\"Score: {channel_best_score:.4f}\")\n", "\n", "            # Update current best transformation for this channel\n", "            current_best_transforms[current_channel] = channel_best_transform\n", "        else:\n", "            print(f\"Warning: No valid transformation found for channel {current_channel}\")\n", "\n", "    # Convert final best transformations to feature list\n", "    best_features = base_features.copy()\n", "    for channel, transform in current_best_transforms.items():\n", "        best_features.append(transform)\n", "\n", "    # Evaluate final model with all channels\n", "    final_metrics = evaluate_model(df, best_features, target_column, benchmark_values)\n", "    if not final_metrics:\n", "        print(\"Warning: Final model with all channels could not be evaluated\")\n", "        # Fall back to the last valid metrics\n", "        for channel in reversed(promo_channels):\n", "            if channel in current_best_transforms:\n", "                # Remove the last added channel transform\n", "                partial_features = [f for f in best_features if f != current_best_transforms[channel]]\n", "                partial_metrics = evaluate_model(df, partial_features, target_column, benchmark_values)\n", "                if partial_metrics:\n", "                    final_metrics = partial_metrics\n", "                    print(f\"Using metrics from model without {channel}\")\n", "                    best_features = partial_features  # Update best_features to the valid set\n", "                    break\n", "\n", "    # Convert results to DataFrame for easier analysis\n", "    results_df = pd.DataFrame(all_results)\n", "\n", "    # Get top 5 alternative models\n", "    top_5_models = []\n", "    if not results_df.empty:\n", "        # Create a new column with a unique feature set identifier for grouping\n", "        results_df['feature_set_key'] = results_df['features_str'].apply(lambda x: str(sorted(eval(x))))\n", "\n", "        # Group by unique feature sets and get the highest score for each\n", "        grouped_results = results_df.sort_values('composite_score', ascending=False)\n", "        grouped_results = grouped_results.drop_duplicates(subset=['feature_set_key'])\n", "\n", "        # Sort by composite score\n", "        top_models = grouped_results.head(6)  # Get top 6 to include the best one\n", "\n", "        # Compare each model feature set with the best one\n", "        best_model_key = str(sorted(best_features))\n", "\n", "        # Get top 5 models that are different from the best one\n", "        count = 0\n", "        for _, row in top_models.iterrows():\n", "            model_features = row['features']  # Get the actual feature list\n", "\n", "            # Skip if this is the same as the best model\n", "            if str(sorted(model_features)) == best_model_key and count < 5:\n", "                continue\n", "\n", "            if count < 5:\n", "                top_5_models.append({\n", "                    'channel': row['channel'],\n", "                    'transformation': row['transformation'],\n", "                    'features': model_features,  # Store actual feature list\n", "                    'features_str': row['features_str'],  # Keep string version for display\n", "                    'R2': row['R2'],\n", "                    'AIC': row['AIC'],\n", "                    'avg_p_value': row['avg_p_value'],\n", "                    'composite_score': row['composite_score']\n", "                })\n", "                count += 1\n", "\n", "        # Save all results to CSV\n", "        # Convert feature lists back to strings for CSV export\n", "        export_results_df = results_df.copy()\n", "        export_results_df['features'] = export_results_df['features_str']\n", "        export_results_df = export_results_df.drop(['features_str', 'feature_set_key'], axis=1)\n", "\n", "        csv_filename = \"optimal_channel_selection_results.csv\"\n", "        export_results_df.to_csv(csv_filename, index=False)\n", "        print(f\"Saved detailed results to {csv_filename}\")\n", "\n", "        # Save top 5 alternative models to a separate CSV\n", "        if top_5_models:\n", "            # Prepare top 5 models for export\n", "            top5_export = [{\n", "                'channel': model['channel'],\n", "                'transformation': model['transformation'],\n", "                'features': model['features_str'],\n", "                'R2': model['R2'],\n", "                'AIC': model['AIC'],\n", "                'avg_p_value': model['avg_p_value'],\n", "                'composite_score': model['composite_score']\n", "            } for model in top_5_models]\n", "\n", "            top5_df = pd.DataFrame(top5_export)\n", "            top5_csv_filename = \"top5_alternative_models.csv\"\n", "            top5_df.to_csv(top5_csv_filename, index=False)\n", "            print(f\"Saved top 5 alternative models to {top5_csv_filename}\")\n", "\n", "    # Calculate composite score for final best features\n", "    best_score = -np.inf\n", "    if final_metrics:\n", "        best_score = calculate_composite_score(final_metrics, ranges, weights)\n", "\n", "    # Print best model details\n", "    if final_metrics:\n", "        promo_only = [f for f in best_features if f not in base_features]\n", "        print(\"\\nFinal optimal channel combination:\")\n", "        print(f\"- Promotional channels: {', '.join(promo_only)}\")\n", "        print(f\"- R²: {final_metrics['R2']:.4f}\")\n", "        print(f\"- AIC: {final_metrics['AIC']:.2f}\")\n", "        print(f\"- Avg. p-value: {final_metrics['avg_p_value']:.4f}\")\n", "        print(f\"- Composite score: {best_score:.4f}\")\n", "\n", "    # Print top 5 alternative models\n", "    if top_5_models:\n", "        print(\"\\nTop 5 alternative models:\")\n", "        for i, model in enumerate(top_5_models):\n", "            print(f\"{i+1}. Score: {model['composite_score']:.4f}, R²: {model['R2']:.4f}\")\n", "            print(f\"   Channel: {model['channel']}, Transform: {model['transformation']}\")\n", "            promo_only = [f for f in model['features'] if f not in base_features]\n", "            print(f\"   Promotional channels: {', '.join(promo_only)}\")\n", "\n", "    # Extract feature sets for all 6 models (best + top 5 alternatives)\n", "    all_model_features = [best_features]\n", "    all_model_metrics = [final_metrics]\n", "\n", "    for model in top_5_models:\n", "        all_model_features.append(model['features'])\n", "\n", "        # Get metrics for this feature set\n", "        model_metrics = evaluate_model(df, model['features'], target_column, benchmark_values)\n", "        all_model_metrics.append(model_metrics)\n", "\n", "    return best_features, final_metrics, all_model_features, all_model_metrics,top_5_models\n"]}, {"cell_type": "code", "execution_count": 80, "id": "56002db6", "metadata": {}, "outputs": [], "source": ["def fix_negative_estimates(df, best_features, target_column, control_variables, date_column, transformed_channels_by_promo, all_transformed_features, id_column,start_date,end_date):\n", "    \"\"\"\n", "    Fix negative estimates in the best_features model by trying different adstock and transformation combinations.\n", "    If a channel cannot be fixed, it will be removed from the model.\n", "\n", "    Returns:\n", "        Tuple of (new_best_features, fixed_channels_info)\n", "    \"\"\"\n", "\n", "    print(\"\\nChecking for negative estimates in best model...\")\n", "\n", "    # First run the regression with current best features to identify negative estimates\n", "    result_table = perform_regression(df, best_features, target_column, date_column,start_date,end_date)\n", "\n", "    if not isinstance(result_table, pd.DataFrame) or result_table.empty:\n", "        print(\"Error running initial regression. Cannot fix negative estimates.\")\n", "        return best_features, {}\n", "\n", "    # Check if we have the expected format from perform_regression\n", "    if 'Channel' in result_table.columns and 'Estimate' in result_table.columns:\n", "        var_column = 'Channel'\n", "        est_column = 'Estimate'\n", "    else:\n", "        print(\"Warning: Unexpected regression result table format.\")\n", "        print(\"Available columns:\", result_table.columns.tolist())\n", "        print(\"Cannot proceed with fixing negative estimates.\")\n", "        return best_features, {}\n", "\n", "    # Identify promotional channels with negative estimates\n", "    negative_channels = []\n", "    for _, row in result_table.iterrows():\n", "        var_name = row[var_column]\n", "\n", "        # Skip the intercept and any non-promotion variables\n", "        if var_name == 'Intercept' or var_name not in all_transformed_features:\n", "            continue\n", "\n", "        est_value = row[est_column]\n", "\n", "        if est_value < 0:\n", "            # Extract the base channel name from the transformed variable\n", "            base_channel = None\n", "            for promo_col, transformed_cols in transformed_channels_by_promo.items():\n", "                if var_name in transformed_cols:\n", "                    base_channel = promo_col\n", "                    break\n", "\n", "            if base_channel:\n", "                negative_channels.append((base_channel, var_name, est_value))\n", "\n", "    if not negative_channels:\n", "        print(\"No negative estimates found in promotional channels. Model is optimal.\")\n", "        return best_features, {}\n", "\n", "    print(f\"Found {len(negative_channels)} promotional channels with negative estimates:\")\n", "    for base_channel, variable, estimate in negative_channels:\n", "        print(f\"- {base_channel} (via {variable}): {estimate:.4f}\")\n", "\n", "    # Dictionary to track fixed channels and their new transformations\n", "    fixed_channels = {}\n", "    channels_to_remove = []\n", "    \n", "    # Dictionary to store only the final best transformations that we'll add to df\n", "    best_transformations = {}\n", "\n", "    # For each negative channel, try all adstock and transformation combinations\n", "    for base_channel, current_variable, _ in negative_channels:\n", "        print(f\"\\nAttempting to fix negative estimate for {base_channel}...\")\n", "\n", "        # Get adstock ranges for this channel\n", "        adstock_ranges = get_adstock_ranges_from_gemini([base_channel])\n", "        channel_range = adstock_ranges.get(base_channel, {\n", "            \"type\": \"Other\",\n", "            \"min_adstock\": 10,\n", "            \"max_adstock\": 90\n", "        })\n", "\n", "        min_adstock = channel_range[\"min_adstock\"]\n", "        max_adstock = channel_range[\"max_adstock\"]\n", "\n", "        # Define the adstock values to test\n", "        adstock_values = list(range(min_adstock, max_adstock + 1, 10))\n", "\n", "        # Define transformations to test\n", "        transformations = get_transformation_functions()\n", "        best_estimate = 0\n", "        best_variable = None\n", "        best_transformed_series = None\n", "\n", "        # Test each combination\n", "        for adstock_rate in adstock_values:\n", "            # Apply adstock transformation to a temporary series, not the original df\n", "            adstock_col, adstocked_series = apply_adstock(df, base_channel, id_column, adstock_rate)\n", "            \n", "            for transform_name, transform_func in transformations.items():\n", "                # Apply transformation to the temporary series\n", "                transformed_col, transformed_series = apply_transformation(\n", "                    adstocked_series, transform_name, transform_func, adstock_col\n", "                )\n", "                \n", "                # Create a temporary DataFrame for this test\n", "                temp_df = df.copy()\n", "                temp_df[transformed_col] = transformed_series\n", "\n", "                # Create a temporary feature set replacing the current transformation\n", "                temp_features = best_features.copy()\n", "\n", "                # Remove the current transformation of this channel from the features\n", "                for chan_transform in transformed_channels_by_promo.get(base_channel, []):\n", "                    if chan_transform in temp_features:\n", "                        temp_features.remove(chan_transform)\n", "\n", "                # Add the new transformation\n", "                temp_features.append(transformed_col)\n", "\n", "                # Run regression with this combination\n", "                temp_result = perform_regression(temp_df, temp_features, target_column, date_column,start_date,end_date)\n", "\n", "                if isinstance(temp_result, pd.DataFrame) and not temp_result.empty:\n", "                    # Find the estimate for this new transformed variable\n", "                    for _, row in temp_result.iterrows():\n", "                        if var_column in row and row[var_column] == transformed_col:\n", "                            if est_column in row:\n", "                                estimate = row[est_column]\n", "                                if estimate > best_estimate:\n", "                                    best_estimate = estimate\n", "                                    best_variable = transformed_col\n", "                                    best_transformed_series = transformed_series\n", "\n", "                # Clean up the temp_df to free memory\n", "                del temp_df\n", "\n", "        # After testing all combinations, check if we found a positive estimate\n", "        if best_estimate > 0:\n", "            print(f\"Fixed negative estimate for {base_channel}. New variable: {best_variable}, New estimate: {best_estimate:.4f}\")\n", "            fixed_channels[base_channel] = {\n", "                'old_variable': current_variable,\n", "                'new_variable': best_variable,\n", "                'new_estimate': best_estimate\n", "            }\n", "            # Store the best transformation to add to df later\n", "            best_transformations[best_variable] = best_transformed_series\n", "        else:\n", "            print(f\"Could not find positive estimate for {base_channel}. Will remove from model.\")\n", "            channels_to_remove.append(base_channel)\n", "\n", "    # Update the best_features list with the fixed transformations\n", "    new_best_features = best_features.copy()\n", "\n", "    # First, remove ALL transformed variables for channels that need to be replaced or removed\n", "    channels_to_process = list(fixed_channels.keys()) + channels_to_remove\n", "    for base_channel in channels_to_process:\n", "        # Remove all transformations of this channel from the feature list\n", "        for transformed_col in transformed_channels_by_promo.get(base_channel, []):\n", "            if transformed_col in new_best_features:\n", "                new_best_features.remove(transformed_col)\n", "\n", "    # Add back new variables for channels that were successfully fixed\n", "    for base_channel, info in fixed_channels.items():\n", "        # Add the new variable\n", "        if info['new_variable'] not in new_best_features:\n", "            new_best_features.append(info['new_variable'])\n", "            \n", "        # Now add the best transformation to the original df\n", "        if info['new_variable'] in best_transformations:\n", "            df[info['new_variable']] = best_transformations[info['new_variable']]\n", "\n", "    # Run a final check to ensure no negative estimates remain\n", "    final_check_result = perform_regression(df, new_best_features, target_column, date_column,start_date,end_date)\n", "    if isinstance(final_check_result, pd.DataFrame) and not final_check_result.empty:\n", "        still_negative = []\n", "\n", "        for _, row in final_check_result.iterrows():\n", "            if (var_column in row and row[var_column] in all_transformed_features and\n", "                est_column in row and row[est_column] < 0):\n", "                still_negative.append(row[var_column])\n", "\n", "        if still_negative:\n", "            print(f\"Warning: {len(still_negative)} promotional channels still have negative estimates:\")\n", "            for var in still_negative:\n", "                print(f\"- {var}\")\n", "            print(\"Consider manual inspection of these variables.\")\n", "\n", "    print(f\"\\nOriginal model had {len(best_features)} features.\")\n", "    print(f\"New model has {len(new_best_features)} features.\")\n", "    print(f\"Fixed {len(fixed_channels)} channels with negative estimates.\")\n", "    print(f\"Removed {len(channels_to_remove)} channels that couldn't be fixed.\")\n", "\n", "    return new_best_features, fixed_channels"]}, {"cell_type": "code", "execution_count": 81, "id": "fc34cd43", "metadata": {}, "outputs": [], "source": ["def run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics):\n", "    \"\"\"Run regression analyses for top models and save results without Excel export\"\"\"\n", "    global all_regression_results, best_result_table\n", "    print(f\"best fetaures in run_regression_analyses{best_features}\")\n", "    print(f\"all_feature_sets in run_regression_analyses{all_feature_sets}\")\n", "    all_regression_results = []\n", "    \n", "    print(\"\\nRunning regression analysis for top 6 models...\")\n", "    for i, feature_set in enumerate(all_feature_sets[:6]):  # Limit to top 6 models\n", "        model_name = \"Best Model\" if i == 0 else f\"Alternative Model {i}\"\n", "        print(f\"\\nRunning regression for {model_name}...\")\n", "\n", "        # Extract only promotional channels for display\n", "        promo_features = [f for f in feature_set if f in all_transformed_features]\n", "        promo_features_str = ', '.join(promo_features)\n", "        print(f\"Promotional channels: {promo_features_str}\")\n", "\n", "        # Perform regression\n", "        result_table = perform_regression(df, feature_set, target_column, date_column, start_date, end_date)\n", "\n", "        # Add model identifier\n", "        if isinstance(result_table, pd.DataFrame) and not result_table.empty:\n", "            try:\n", "                # Add model columns if there's at least one row\n", "                if len(result_table) > 0:\n", "                    result_table['model'] = model_name\n", "                    result_table['model_rank'] = i + 1\n", "\n", "                all_regression_results.append(result_table)\n", "\n", "                # Get metrics for this model\n", "                metrics = all_metrics[i] if i < len(all_metrics) else None\n", "                if metrics:\n", "                    r2 = metrics['R2']\n", "                    aic = metrics['AIC']\n", "                    avg_p_value = metrics['avg_p_value']\n", "                    print(f\"R²: {r2:.4f}, AIC: {aic:.2f}\")\n", "                else:\n", "                    print(\"Metrics not available for this model\")\n", "            except Exception as e:\n", "                print(f\"Error processing regression results: {e}\")\n", "        else:\n", "            print(f\"Error running regression for {model_name}\")\n", "\n", "    # Set the best result table\n", "    best_result_table = all_regression_results[0] if all_regression_results else None\n", "\n", "    return all_regression_results, best_result_table\n", "\n", "def display_best_model_results():\n", "    \"\"\"Display the results of the best model\"\"\"\n", "    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:\n", "        print(\"\\nBest Model Regression Results:\")\n", "        print(\"=\"*80)\n", "        try:\n", "            print(best_result_table.to_string(index=False))\n", "        except Exception as e:\n", "            print(f\"Error displaying results table: {e}\")\n", "            print(\"Results available in Excel file.\")\n", "        print(\"=\"*80)\n", "        return True\n", "    return False"]}, {"cell_type": "markdown", "id": "89f329bd", "metadata": {}, "source": ["# Update database after regression"]}, {"cell_type": "code", "execution_count": 93, "id": "bdf9863c", "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import json\n", "from psycopg2.extras import <PERSON>son\n", "import pandas as pd\n", "import datetime\n", "import logging\n", "\n", "def update_model_regression(conn, model_run_id, all_transformed_features, best_result_table, \n", "                           transformed_channels_by_promo, adstocked_channels_by_promo, \n", "                           adstock_range_channel, parms, all_regression_results, best_features, all_feature_sets):\n", "    \"\"\"\n", "    Update the JSONB fields in the model_run_data table for a specific model_run_id.\n", "    \n", "    Parameters:\n", "    conn: PostgreSQL connection object\n", "    model_run_id: UUID of the model run to update\n", "    all_transformed_features: List of feature names\n", "    best_result_table: DataFrame (will be converted to JSON)\n", "    transformed_channels_by_promo: Dict\n", "    adstocked_channels_by_promo: Dict\n", "    adstock_range_channel: Dict\n", "    parms: Dict (contains date column)\n", "    all_regression_results: List of DataFrames\n", "    best_features: List of best features\n", "    all_feature_sets: List of all feature sets\n", "    \"\"\"\n", "    if not conn or conn.closed:\n", "        logging.error(\"Invalid database connection\")\n", "        return False\n", "    \n", "    # Custom JSON encoder to handle datetime objects\n", "    class DateTimeEncoder(json.JSONEncoder):\n", "        def default(self, obj):\n", "            if isinstance(obj, (datetime.datetime, datetime.date)):\n", "                return obj.isoformat()\n", "            return super().default(obj)\n", "    \n", "    # Helper function to safely convert any pandas DataFrames to serializable dictionaries\n", "    def convert_pandas_to_dict(obj):\n", "        if isinstance(obj, pd.DataFrame):\n", "            # Convert DataFrame to dict with records orientation and convert datetime columns to ISO format\n", "            for col in obj.select_dtypes(include=['datetime64']).columns:\n", "                obj[col] = obj[col].astype(str)\n", "            return json.loads(obj.to_json(orient='records', date_format='iso'))\n", "        elif isinstance(obj, pd.Series):\n", "            # Handle datetime Series\n", "            if pd.api.types.is_datetime64_any_dtype(obj):\n", "                obj = obj.astype(str)\n", "            return json.loads(obj.to_json())\n", "        elif isinstance(obj, dict):\n", "            # Handle dictionaries - recursively process all values\n", "            return {k: convert_pandas_to_dict(v) for k, v in obj.items()}\n", "        elif isinstance(obj, list):\n", "            # Handle lists - recursively process all items\n", "            return [convert_pandas_to_dict(item) for item in obj]\n", "        elif isinstance(obj, (datetime.datetime, datetime.date)):\n", "            # Convert datetime objects to ISO format string\n", "            return obj.isoformat()\n", "        else:\n", "            return obj\n", "    \n", "    try:\n", "        # Convert each parameter to a serializable format\n", "        serialized_all_transformed_features = convert_pandas_to_dict(all_transformed_features)\n", "        serialized_best_result_table = convert_pandas_to_dict(best_result_table)\n", "        serialized_transformed_channels = convert_pandas_to_dict(transformed_channels_by_promo)\n", "        serialized_adstocked_channels = convert_pandas_to_dict(adstocked_channels_by_promo)\n", "        serialized_adstock_range = convert_pandas_to_dict(adstock_range_channel)\n", "        serialized_parms = convert_pandas_to_dict(parms)\n", "        serialized_regression_results = convert_pandas_to_dict(all_regression_results)\n", "        serialized_best_features = convert_pandas_to_dict(best_features)\n", "        serialized_all_feature_sets = convert_pandas_to_dict(all_feature_sets)\n", "        \n", "        # Create a cursor\n", "        cursor = conn.cursor()\n", "        \n", "        # SQL query for update - now including best_features and all_features\n", "        update_query = \"\"\"\n", "        UPDATE model_run_data\n", "        SET \n", "            all_transformed_features = %s,\n", "            best_result_table = %s,\n", "            transformed_channels_by_promo = %s,\n", "            adstocked_channels_by_promo = %s,\n", "            adstock_range_channel = %s,\n", "            parms = %s,\n", "            model_results = %s,\n", "            best_features = %s,\n", "            all_features = %s,\n", "            updated_at = CURRENT_TIMESTAMP\n", "        WHERE model_run_id = %s\n", "        \"\"\"\n", "        \n", "        # Execute the query with parameters - now including the new fields\n", "        cursor.execute(update_query, (\n", "            <PERSON>son(serialized_all_transformed_features),\n", "            <PERSON>son(serialized_best_result_table),\n", "            Json(serialized_transformed_channels),\n", "            Json(serialized_adstocked_channels),\n", "            Json(serialized_adstock_range),\n", "            <PERSON><PERSON>(serialized_parms),\n", "            <PERSON><PERSON>(serialized_regression_results),\n", "            <PERSON>son(serialized_best_features),\n", "            <PERSON><PERSON>(serialized_all_feature_sets),\n", "            model_run_id\n", "        ))\n", "        \n", "        # Commit the transaction\n", "        conn.commit()\n", "        \n", "        print(f\"Successfully updated model run data for model_run_id: {model_run_id}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error updating database: {e}\")\n", "        conn.rollback()\n", "        return False\n", "    finally:\n", "        # Close the cursor if it exists\n", "        if 'cursor' in locals() and cursor is not None:\n", "            cursor.close()"]}, {"cell_type": "markdown", "id": "d2237263", "metadata": {}, "source": ["# Main pipeline"]}, {"cell_type": "code", "execution_count": null, "id": "18847455", "metadata": {}, "outputs": [], "source": ["def run_pipeline():\n", "    \"\"\"Main pipeline function to orchestrate the marketing mix modeling process\"\"\"\n", "    # Load input files\n", "    db_conn = initialize_db_connection()\n", "    file_path = \"dummy1.xlsx\"\n", "    spends_file_path = \"spends.xlsx\"\n", "    historical_file_path = \"historical_data.xlsx\"\n", "\n", "    model_run_id = \"00000000-0000-0000-0000-000000000001\"\n", "    df,promo_channels, date_column, id_column, target_column, date_format,start_date,end_date= process_main_data(file_path,model_run_id,db_conn)\n", "    spends_df,spend_column,spend_channel_column = process_spend_data(spends_file_path,model_run_id,db_conn)\n", "    historical_df,historical_impact,historical_channel_column = process_historical_data(historical_file_path,model_run_id,db_conn)\n", "    df, control_variables = add_control_variables(df, id_column, date_column)\n", "\n", "    # Setup control variables\n", "    df, control_variables = add_control_variables(df, id_column, date_column)\n", "\n", "    params=setup_parameters(df, target_column, date_column, id_column, start_date, end_date, spend_channel_column,spend_column,historical_channel_column,historical_impact)\n", "    \n", "    # Process promo channels\n", "    success,df, transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_transformed_features = process_promo_channels(df,promo_channels,target_column,id_column,date_column,start_date,end_date,control_variables)\n", "  \n", "    #Get historical benchmark\n", "    benchmark_values = get_benchmark_values(all_transformed_features, historical_df,historical_channel_column,historical_impact)\n", "    \n", "    #Filter the dataframe\n", "    df = filter_dataframe_by_date(df,date_column,start_date,end_date)\n", "\n", "    # Select optimal feature set\n", "    best_features, best_metrics, all_feature_sets, all_metrics,top_5_models = select_optimal_channels(\n", "        df,\n", "        transformed_channels_by_promo,\n", "        target_column,\n", "        control_variables=control_variables,\n", "        benchmark_values=benchmark_values\n", "    )\n", "    \n", "    # Fix negative estimates in model\n", "   \n", "    corrected_features, fixed_channels_info = fix_negative_estimates(\n", "        df,\n", "        best_features,\n", "        target_column,\n", "        control_variables,\n", "        date_column,\n", "        transformed_channels_by_promo,\n", "        all_transformed_features,\n", "        id_column,\n", "        start_date,\n", "        end_date\n", "    )\n", "    # Update the best features for final regression\n", "    best_features = corrected_features\n", "    all_feature_sets[0] = corrected_features\n", "\n", "    # Run regression analyses\n", "    all_regression_results, best_result_table = run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics)\n", "    update_model_regression(db_conn, model_run_id, all_transformed_features, best_result_table, \n", "                          transformed_channels_by_promo, adstocked_channels_by_promo, \n", "                          adstock_range_channel, params, all_regression_results,best_features,all_feature_sets)\n", "    print(best_result_table)\n", "    # channel_updates = [\n", "    #     ('PDE', 10, 'log'),\n", "    #     ('<PERSON><PERSON>', 30, 'Root8')\n", "    # ]\n", "    # updated_features=update_multiple_channel_transformations(df,best_features,best_result_table,id_column, channel_updates)\n", "   \n", "    print(best_result_table)\n", "    \n", "    close_db_connection(db_conn)\n", "    print(f\"After loading data {len(df)}\")\n", "    if df is None:\n", "        return \"Error in data loading.\"\n", "   "]}, {"cell_type": "code", "execution_count": null, "id": "f4f322f9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 96, "id": "2de01507", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validated main data columns: Date, ID, NRx\n", "Promotional channels: PDE, Copay\n", "Date range: 2024-11-01 00:00:00 to 2025-12-01 00:00:00\n", "\n", "Select date format from the following options:\n", "1. MM/DD/YYYY\n", "2. YYYY-MM-DD\n", "3. YYYY-MM\n", "4. MM-YYYY\n", "Selected date format: MM-YYYY\n", "\n", "Select date format from the following options:\n", "1. MM/DD/YYYY\n", "2. YYYY-MM-DD\n", "3. YYYY-MM\n", "4. MM-YYYY\n", "Selected date format: MM-YYYY\n", "Date conversion: 95/95 dates successfully converted\n", "Dropped 0 rows with invalid dates\n", "[PDE] Removed 1 outliers (mu ± 3σ) - 0.00% drop\n", "Total outliers removed: 1\n", "Remaining rows: 94\n", "Outlier summary:\n", "  channel  before  after  Percentage drop\n", "0     PDE    5867   5867         0.000000\n", "1   Copay    4058   4058         0.000000\n", "2     NRx   10584  10495         0.840892\n", "Processed historical data with columns: Channel, contributions%\n", "Added 25 control variables\n", "Added 25 control variables\n", "\n", "Processing promotional channel: PDE\n", "\n", "Selecting top 3 adstock and transformation combinations for: PDE\n", "All adstock ranges:{'PDE': {'type': 'Personal Promotion', 'min_adstock': 70, 'max_adstock': 80}}\n", "Channel type: Personal Promotion, Recommended adstock range: 70% to 80%\n", "\n", "Top 3 combinations selected for PDE:\n", "\n", "Combination #1:\n", "- Adstock rate: 10%\n", "- Transformation: Root9\n", "- R²: 0.0308\n", "- p-value: 0.2065\n", "- AIC: 562.4651\n", "- In recommended range: No\n", "- Final score: 0.7500\n", "\n", "Combination #2:\n", "- Adstock rate: 10%\n", "- Transformation: Root8\n", "- R²: 0.0299\n", "- p-value: 0.2133\n", "- AIC: 562.5151\n", "- In recommended range: No\n", "- Final score: 0.7373\n", "\n", "Combination #3:\n", "- Adstock rate: 10%\n", "- Transformation: Root7\n", "- R²: 0.0290\n", "- p-value: 0.2206\n", "- AIC: 562.5669\n", "- In recommended range: No\n", "- Final score: 0.7241\n", "- Best Adstock columns: PDE_Adstock_10, PDE_Adstock_10, PDE_Adstock_10\n", "- Best Transformed columns: PDE_Adstock_10_Root9, PDE_Adstock_10_Root8, PDE_Adstock_10_Root7\n", "\n", "Processing promotional channel: Copay\n", "\n", "Selecting top 3 adstock and transformation combinations for: Copay\n", "All adstock ranges:{'Copay': {'type': 'Other', 'min_adstock': 10, 'max_adstock': 30}}\n", "Channel type: Other, Recommended adstock range: 10% to 30%\n", "\n", "Top 3 combinations selected for Copay:\n", "\n", "Combination #1:\n", "- Adstock rate: 10%\n", "- Transformation: Root7\n", "- R²: 0.0115\n", "- p-value: 0.4457\n", "- AIC: 563.5484\n", "- In recommended range: Yes\n", "- Final score: 1.0000\n", "\n", "Combination #2:\n", "- Adstock rate: 10%\n", "- Transformation: Root6\n", "- R²: 0.0114\n", "- p-value: 0.4466\n", "- AIC: 563.5509\n", "- In recommended range: Yes\n", "- Final score: 0.9975\n", "\n", "Combination #3:\n", "- Adstock rate: 10%\n", "- Transformation: Root8\n", "- R²: 0.0114\n", "- p-value: 0.4477\n", "- AIC: 563.5537\n", "- In recommended range: Yes\n", "- Final score: 0.9947\n", "- Best Adstock columns: Copay_Adstock_10, Copay_Adstock_10, Copay_Adstock_10\n", "- Best Transformed columns: <PERSON><PERSON>_Adstock_10_Root7, <PERSON>y_Adstock_10_Root6, <PERSON>y_Adstock_10_Root8\n", "\n", "Selecting optimal channel combination...\n", "before optimal channel selection 94\n", "\n", "Selecting optimal channel combination using one-variable-at-a-time optimization...\n", "Evaluating 2 promotional channels using one-variable-at-a-time optimization...\n", "\n", "Tuning channel 1/2: PDE\n", "Testing 3 transformations for PDE...\n", "  Testing transformation 1/3: PDE_Adstock_10_Root9\n", "Feature set['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root7']\n", "    New best for channel PDE! Score: 0.0000\n", "    R²: 0.0354, AIC: 564.20\n", "  Testing transformation 2/3: PDE_Adstock_10_Root8\n", "Feature set['NRx_lag_1', 'PDE_Adstock_10_Root8', 'Copay_Adstock_10_Root7']\n", "  Testing transformation 3/3: PDE_Adstock_10_Root7\n", "Feature set['NRx_lag_1', 'PDE_Adstock_10_Root7', 'Copay_Adstock_10_Root7']\n", "\n", "Best transformation for PDE: PDE_Adstock_10_Root9\n", "Score: 0.0000\n", "\n", "Tuning channel 2/2: Copay\n", "Testing 3 transformations for Copay...\n", "  Testing transformation 1/3: <PERSON><PERSON>_Adstock_10_Root7\n", "Feature set['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root7']\n", "    New best for channel Copay! Score: 0.0000\n", "    R²: 0.0354, AIC: 564.20\n", "  Testing transformation 2/3: <PERSON><PERSON>_Adstock_10_Root6\n", "Feature set['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root6']\n", "  Testing transformation 3/3: <PERSON><PERSON>_Adstock_10_Root8\n", "Feature set['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root8']\n", "\n", "Best transformation for Copay: Copay_Adstock_10_Root7\n", "Score: 0.0000\n", "Saved detailed results to optimal_channel_selection_results.csv\n", "Saved top 5 alternative models to top5_alternative_models.csv\n", "\n", "Final optimal channel combination:\n", "- Promotional channels: PDE_Adstock_10_Root9, Copay_Adstock_10_Root7\n", "- R²: 0.0354\n", "- AIC: 564.20\n", "- Avg. p-value: 0.5569\n", "- Composite score: 0.0000\n", "\n", "Top 5 alternative models:\n", "1. Score: 0.0000, R²: 0.0346\n", "   Channel: PDE, Transform: PDE_Adstock_10_Root8\n", "   Promotional channels: PDE_Adstock_10_Root8, Copay_Adstock_10_Root7\n", "2. Score: 0.0000, R²: 0.0338\n", "   Channel: PDE, Transform: PDE_Adstock_10_Root7\n", "   Promotional channels: PDE_Adstock_10_Root7, Copay_Adstock_10_Root7\n", "3. Score: 0.0000, R²: 0.0356\n", "   Channel: Copay, Transform: Copay_Adstock_10_Root6\n", "   Promotional channels: PDE_Adstock_10_Root9, Copay_Adstock_10_Root6\n", "4. Score: 0.0000, R²: 0.0352\n", "   Channel: Copay, Transform: Copay_Adstock_10_Root8\n", "   Promotional channels: PDE_Adstock_10_Root9, Copay_Adstock_10_Root8\n", "\n", "Checking for negative estimates in best model...\n", "No negative estimates found in promotional channels. Model is optimal.\n", "best fetaures in run_regression_analyses['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root7']\n", "all_feature_sets in run_regression_analyses[['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root7'], ['NRx_lag_1', 'PDE_Adstock_10_Root8', 'Copay_Adstock_10_Root7'], ['NRx_lag_1', 'PDE_Adstock_10_Root7', 'Copay_Adstock_10_Root7'], ['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root6'], ['NRx_lag_1', 'PDE_Adstock_10_Root9', 'Copay_Adstock_10_Root8']]\n", "\n", "Running regression analysis for top 6 models...\n", "\n", "Running regression for Best Model...\n", "Promotional channels: PDE_Adstock_10_Root9, Copay_Adstock_10_Root7\n", "R²: 0.0354, AIC: 564.20\n", "\n", "Running regression for Alternative Model 1...\n", "Promotional channels: PDE_Adstock_10_Root8, Copay_Adstock_10_Root7\n", "R²: 0.0346, AIC: 564.24\n", "\n", "Running regression for Alternative Model 2...\n", "Promotional channels: PDE_Adstock_10_Root7, Copay_Adstock_10_Root7\n", "R²: 0.0338, AIC: 564.29\n", "\n", "Running regression for Alternative Model 3...\n", "Promotional channels: PDE_Adstock_10_Root9, Copay_Adstock_10_Root6\n", "R²: 0.0356, AIC: 564.19\n", "\n", "Running regression for Alternative Model 4...\n", "Promotional channels: PDE_Adstock_10_Root9, Copay_Adstock_10_Root8\n", "R²: 0.0352, AIC: 564.21\n", "Successfully updated model run data for model_run_id: 00000000-0000-0000-0000-000000000001\n", "                  Channel   Estimate  Impact Percentage   P-Value  \\\n", "0               NRx_lag_1  -0.038762          -3.907412  0.782951   \n", "1    PDE_Adstock_10_Root9   0.537300          22.200207  0.265879   \n", "2  Copay_Adstock_10_Root7   0.303833           3.943469  0.621950   \n", "3               Intercept  84.182779          77.763736  0.001531   \n", "\n", "   Effectiveness  Linear Activity        R²  Adjusted R²  \\\n", "0    -230.786771           5954.0  0.035422    -0.021317   \n", "1    1821.447896           3390.0  0.035422    -0.021317   \n", "2     753.506575           2480.0  0.035422    -0.021317   \n", "3            NaN              NaN  0.035422    -0.021317   \n", "\n", "   Total Modeled Activity  Modeled Sales  Actual Sales       model  model_rank  \n", "0             6002.000000         5954.0          5954  Best Model           1  \n", "1             2460.077569         5954.0          5954  Best Model           1  \n", "2              772.772922         5954.0          5954  Best Model           1  \n", "3                     NaN         5954.0          5954  Best Model           1  \n", "                  Channel   Estimate  Impact Percentage   P-Value  \\\n", "0               NRx_lag_1  -0.038762          -3.907412  0.782951   \n", "1    PDE_Adstock_10_Root9   0.537300          22.200207  0.265879   \n", "2  Copay_Adstock_10_Root7   0.303833           3.943469  0.621950   \n", "3               Intercept  84.182779          77.763736  0.001531   \n", "\n", "   Effectiveness  Linear Activity        R²  Adjusted R²  \\\n", "0    -230.786771           5954.0  0.035422    -0.021317   \n", "1    1821.447896           3390.0  0.035422    -0.021317   \n", "2     753.506575           2480.0  0.035422    -0.021317   \n", "3            NaN              NaN  0.035422    -0.021317   \n", "\n", "   Total Modeled Activity  Modeled Sales  Actual Sales       model  model_rank  \n", "0             6002.000000         5954.0          5954  Best Model           1  \n", "1             2460.077569         5954.0          5954  Best Model           1  \n", "2              772.772922         5954.0          5954  Best Model           1  \n", "3                     NaN         5954.0          5954  Best Model           1  \n", "After loading data 55\n"]}], "source": ["# transformed_channels_by_promo={}\n", "# result_df=None\n", "# all_transformed_features=[]\n", "\n", "# control_variables=[]\n", "# date_column=None\n", "# params={}\n", "# best_result_table=None\n", "\n", "# adstock_range_channel={}\n", "\n", "run_pipeline()"]}, {"cell_type": "markdown", "id": "4a35961f", "metadata": {}, "source": ["# Extract Model features from database"]}, {"cell_type": "code", "execution_count": 38, "id": "62caaf0f", "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import datetime\n", "import logging\n", "from psycopg2.extras import RealDictCursor\n", "from uuid import UUID\n", "\n", "def extract_model_data(conn, model_run_id):\n", "    \"\"\"\n", "    Extract ALL model data from the database for a specific model_run_id and convert it back\n", "    to its original Python data structures.\n", "    \n", "    Parameters:\n", "    conn: PostgreSQL connection object\n", "    model_run_id: UUID of the model run to extract\n", "    \n", "    Returns:\n", "    dict: Dictionary containing all the extracted model data in their original format\n", "    \"\"\"\n", "    if not conn or conn.closed:\n", "        logging.error(\"Invalid database connection\")\n", "        return None\n", "    \n", "    try:\n", "        # Create a cursor that returns rows as dictionaries\n", "        cursor = conn.cursor(cursor_factory=RealDictCursor)\n", "        \n", "        # SQL query to extract ALL data for a specific model_run_id\n", "        query = \"\"\"\n", "        SELECT *\n", "        FROM model_run_data\n", "        WHERE model_run_id = %s\n", "        \"\"\"\n", "        \n", "        # Execute the query\n", "        cursor.execute(query, (model_run_id,))\n", "        \n", "        # Fetch the result\n", "        result = cursor.fetchone()\n", "        \n", "        if not result:\n", "            logging.error(f\"No data found for model_run_id: {model_run_id}\")\n", "            return None\n", "            \n", "        # Convert result to Python data structures\n", "        model_data = {}\n", "        \n", "        # Process standard columns (non-JSONB types)\n", "        for column in result:\n", "            value = result[column]\n", "            \n", "            # Skip columns we'll process specially later\n", "            if column in ['all_transformed_features', 'best_result_table', 'transformed_channels_by_promo',\n", "                         'adstocked_channels_by_promo', 'adstock_range_channel', 'parms',\n", "                         'model_results', 'best_features', 'all_features']:\n", "                continue\n", "                \n", "            # Process special types\n", "            if isinstance(value, UUID):\n", "                model_data[column] = str(value)\n", "            elif isinstance(value, list) and column == 'promotional_columns':\n", "                # Keep array types as lists\n", "                model_data[column] = value\n", "            else:\n", "                # Copy other values directly\n", "                model_data[column] = value\n", "        \n", "        # Convert JSONB columns back to appropriate Python structures\n", "        \n", "        # Convert all_transformed_features back to list\n", "        if result['all_transformed_features']:\n", "            model_data['all_transformed_features'] = result['all_transformed_features']\n", "            # If it contains more complex data, additional conversion may be needed\n", "        \n", "        # Convert best_result_table back to DataFrame\n", "        if result['best_result_table']:\n", "            if isinstance(result['best_result_table'], list):\n", "                model_data['best_result_table'] = pd.DataFrame(result['best_result_table'])\n", "            else:\n", "                # Handle case where best_result_table is stored in a different format\n", "                model_data['best_result_table'] = pd.DataFrame.from_dict(result['best_result_table'])\n", "        \n", "        # Convert transformed_channels_by_promo back to dict\n", "        if result['transformed_channels_by_promo']:\n", "            model_data['transformed_channels_by_promo'] = result['transformed_channels_by_promo']\n", "            # Convert inner values to appropriate data structures if needed\n", "            for key, value in model_data['transformed_channels_by_promo'].items():\n", "                if isinstance(value, list):\n", "                    # Check if this looks like DataFrame data\n", "                    if value and isinstance(value[0], dict) and all(isinstance(item, dict) for item in value):\n", "                        model_data['transformed_channels_by_promo'][key] = pd.DataFrame(value)\n", "        \n", "        # Convert adstocked_channels_by_promo back to dict\n", "        if result['adstocked_channels_by_promo']:\n", "            model_data['adstocked_channels_by_promo'] = result['adstocked_channels_by_promo']\n", "            # Convert inner values to appropriate data structures if needed\n", "            for key, value in model_data['adstocked_channels_by_promo'].items():\n", "                if isinstance(value, list):\n", "                    # Check if this looks like DataFrame data\n", "                    if value and isinstance(value[0], dict) and all(isinstance(item, dict) for item in value):\n", "                        model_data['adstocked_channels_by_promo'][key] = pd.DataFrame(value)\n", "        \n", "        # Convert adstock_range_channel back to dict\n", "        if result['adstock_range_channel']:\n", "            model_data['adstock_range_channel'] = result['adstock_range_channel']\n", "        \n", "        # Convert parms back to dict\n", "        if result['parms']:\n", "            model_data['parms'] = result['parms']\n", "            \n", "            # Convert date strings back to datetime objects if needed\n", "            for key, value in model_data['parms'].items():\n", "                if isinstance(value, str) and key.lower().endswith('date'):\n", "                    try:\n", "                        model_data['parms'][key] = pd.to_datetime(value)\n", "                    except:\n", "                        # Keep as string if conversion fails\n", "                        pass\n", "        \n", "        # Convert model_results back to list of DataFrames\n", "        if result['model_results']:\n", "            if isinstance(result['model_results'], list):\n", "                # If it's a list of records that look like DataFrames\n", "                model_results = []\n", "                for item in result['model_results']:\n", "                    if isinstance(item, list):\n", "                        model_results.append(pd.DataFrame(item))\n", "                    elif isinstance(item, dict):\n", "                        # Handle different storage formats\n", "                        model_results.append(pd.DataFrame.from_dict(item))\n", "                model_data['model_results'] = model_results\n", "            else:\n", "                # If it's stored in a different format\n", "                model_data['model_results'] = result['model_results']\n", "        \n", "        # Convert best_features back to list\n", "        if result['best_features']:\n", "            model_data['best_features'] = result['best_features']\n", "            # Convert to appropriate format if needed\n", "            if isinstance(model_data['best_features'], dict) and 'series' in model_data['best_features']:\n", "                try:\n", "                    model_data['best_features'] = pd.Series(model_data['best_features']['series'])\n", "                except:\n", "                    pass\n", "        \n", "        # Convert all_features back to list\n", "        if result['all_features']:\n", "            model_data['all_features'] = result['all_features']\n", "            # Convert nested structures if needed\n", "            if isinstance(model_data['all_features'], list):\n", "                # If it's a list of feature sets, each potentially being a DataFrame\n", "                for i, feature_set in enumerate(model_data['all_features']):\n", "                    if isinstance(feature_set, list) and feature_set and isinstance(feature_set[0], dict):\n", "                        model_data['all_features'][i] = pd.DataFrame(feature_set)\n", "        \n", "        return model_data\n", "    \n", "    except Exception as e:\n", "        logging.error(f\"Error extracting data from database: {e}\")\n", "        return None\n", "    finally:\n", "        # Close the cursor if it exists\n", "        if 'cursor' in locals() and cursor is not None:\n", "            cursor.close()\n", "\n", "# Example usage:\n", "# conn = psycopg2.connect(\"dbname=mydb user=user password=pass host=localhost\")\n", "# model_data = extract_model_data(conn, \"123e4567-e89b-12d3-a456-426614174000\")\n", "# \n", "# # Access all data including standard columns\n", "# date_column = model_data['date_column']\n", "# id_column = model_data['id_column']\n", "# target_column = model_data['target_column']\n", "# promotional_columns = model_data['promotional_columns']\n", "# start_date = model_data['start_date']\n", "# end_date = model_data['end_date']\n", "# \n", "# # Access complex columns\n", "# best_result_table = model_data['best_result_table']\n", "# best_features = model_data['best_features']\n", "# all_features = model_data['all_features']"]}, {"cell_type": "markdown", "id": "17943e80", "metadata": {}, "source": ["# Updating channels"]}, {"cell_type": "code", "execution_count": 39, "id": "f20b9eea", "metadata": {}, "outputs": [], "source": ["def update_multiple_channel_transformations(df,id_column,best_features, channel_updates):\n", "    \"\"\"\n", "    Update multiple channel transformations and return updated feature list for regression.\n", "    \n", "    Args:\n", "        best_result_table (pd.DataFrame): The current best result table\n", "        channel_updates (list): List of (base_channel, new_adstock_rate, new_transform_name) tuples\n", "    \n", "    Returns:\n", "        list: List of features for perform_regression, with updated transformed features\n", "    \"\"\"\n", "    \n", "    # Create a copy of the best_features to update\n", "    updated_features = best_features.copy()\n", "    \n", "    print(f\"Starting multiple channel updates. Initial features: {len(best_features)}\")\n", "    \n", "    for base_channel, new_adstock_rate, new_transform_name in channel_updates:\n", "        # Identify the current feature used for this base channel in updated_features\n", "        current_feature = None\n", "        for feature in updated_features:\n", "            if feature.startswith(f\"{base_channel}_Adstock_\"):\n", "                current_feature = feature\n", "                break\n", "        \n", "        if current_feature is None:\n", "            print(f\"Warning: No feature for {base_channel} found in features list. Skipping.\")\n", "            continue\n", "        \n", "        print(f\"\\nUpdating channel: {base_channel}\")\n", "        print(f\"Current feature: {current_feature}\")\n", "        \n", "        # Create the new channel name\n", "        new_feature_name = f\"{base_channel}_Adstock_{new_adstock_rate}_{new_transform_name}\"\n", "        print(f\"New feature name will be: {new_feature_name}\")\n", "        \n", "        # Check if the base channel exists in the dataframe\n", "        if base_channel not in df.columns:\n", "            print(f\"Warning: Base channel {base_channel} not found in dataframe. Skipping.\")\n", "            continue\n", "        \n", "        # Apply new adstock\n", "        print(f\"Applying adstock with rate {new_adstock_rate}...\")\n", "        try:\n", "            adstock_col, adstocked_series = apply_adstock(\n", "                df, \n", "                base_channel, \n", "                id_column,  # Assuming id_column is globally available\n", "                new_adstock_rate\n", "            )\n", "            print(f\"Created adstock column: {adstock_col}\")\n", "            \n", "            # Apply new transformation\n", "            transform_funcs = get_transformation_functions()\n", "            if new_transform_name not in transform_funcs:\n", "                print(f\"Warning: Transformation {new_transform_name} not found. Skipping.\")\n", "                continue\n", "            \n", "            print(f\"Applying transformation: {new_transform_name}...\")\n", "            transform_func = transform_funcs[new_transform_name]\n", "            transformed_col = new_feature_name\n", "            _, transformed_series = apply_transformation(\n", "                adstocked_series,\n", "                new_transform_name,\n", "                transform_func,\n", "                adstock_col\n", "            )\n", "            print(f\"Created transformed column: {transformed_col}\")\n", "            \n", "            # Add the transformed data to the dataframe\n", "            df[transformed_col] = transformed_series\n", "            print(f\"Added {transformed_col} to dataframe\")\n", "            \n", "            # Update feature list by replacing the current feature with the new one\n", "            feature_index = updated_features.index(current_feature)\n", "            updated_features[feature_index] = new_feature_name\n", "            print(f\"Updated features: Replaced {current_feature} with {new_feature_name}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error updating {base_channel}: {e}\")\n", "            print(f\"Skipping this channel and continuing with others.\")\n", "    \n", "    print(f\"\\nMultiple channel updates complete. Total features in updated list: {len(updated_features)}\")\n", "    return df,updated_features"]}, {"cell_type": "code", "execution_count": null, "id": "467fc259", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b8c67f8a", "metadata": {}, "source": ["# process main file"]}, {"cell_type": "code", "execution_count": 49, "id": "ed4d0f63", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def process_and_filter_main_data(file_path, promo_channels, date_column, id_column, target_column, start_date, end_date, date_format):\n", "    \"\"\"\n", "    Clean and process the main data and update the PostgreSQL database directly.\n", "    \n", "    Args:\n", "        file_path (str): Path to the input file\n", "        promo_channels (list): List of promotional channel columns\n", "        date_column (str): Name of the date column\n", "        id_column (str): Name of the ID column\n", "        target_column (str): Name of the target column\n", "        start_date (datetime.date): Start date for filtering\n", "        end_date (datetime.date): End date for filtering\n", "        date_format (str): Format of dates in the original data\n", "        \n", "    Returns:\n", "        pandas.DataFrame: Processed main dataframe\n", "    \"\"\"\n", "    import pandas as pd\n", "    import numpy as np\n", "    import logging\n", "    \n", "    try:\n", "        # Load the data\n", "        df = load_file(file_path)\n", "        df['original_date'] = df[date_column].copy()\n", "        \n", "        # Convert dates\n", "        df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))\n", "        print(type(date_column))  # This will print <class 'str'>\n", "\n", "        # Print debugging info about the conversion success\n", "        na_count = df[date_column].isna().sum()\n", "        total_rows = len(df)\n", "        print(f\"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted\")\n", "\n", "        if na_count > 0:\n", "            # Show examples of problematic values\n", "            problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]\n", "            print(f\"Examples of problematic date values: {problem_examples}\")\n", "\n", "        # Drop the empty dates\n", "        orig_len = len(df)\n", "        df = df.dropna(subset=[date_column])\n", "        print(f\"Dropped {orig_len - len(df)} rows with invalid dates\")\n", "\n", "        # Drop the debug column\n", "        df = df.drop(columns=['original_date'])\n", "        \n", "        # Ensure numeric columns\n", "        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()\n", "        for col in numeric_columns:\n", "            df[col] = pd.to_numeric(df[col], errors='coerce')\n", "        df = df.drop_duplicates()\n", "        logging.info(\"Data successfully loaded and cleaned.\")\n", "        \n", "        # Create lag variable\n", "        df = df.sort_values(by=[id_column, date_column])\n", "        df[f\"{target_column}_lag_1\"] = df.groupby(id_column)[target_column].shift(1).fillna(0)\n", "        \n", "        # Handle outliers\n", "        df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)\n", "\n", "        # Convert start_date and end_date to pandas datetime64 for correct comparison\n", "        \n", "        \n", "        # Filter the dataframe by date range\n", "        df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "        \n", "        print(f\"Type of df[date_column]: {type(df[date_column].iloc[0]) if not df.empty else 'No data'}\")\n", "        \n", "        \n", "        return df\n", "    \n", "    except Exception as e:\n", "        logging.error(f\"Error processing main data: {str(e)}\")\n", "        raise"]}, {"cell_type": "markdown", "id": "c4721e58", "metadata": {}, "source": ["# update change sto database after the user satisfaction"]}, {"cell_type": "code", "execution_count": 172, "id": "70b65a5a", "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import json\n", "from psycopg2.extras import <PERSON>son\n", "import pandas as pd\n", "import datetime\n", "import logging\n", "\n", "def update_model_specific_fields(conn, model_run_id, best_result_table=None, all_regression_results=None, \n", "                                best_features=None, all_feature_sets=None):\n", "    \"\"\"\n", "    Update specific JSONB fields in the model_run_data table for a specific model_run_id.\n", "    Only updates the fields that are provided (not None).\n", "    \n", "    Parameters:\n", "    conn: PostgreSQL connection object\n", "    model_run_id: UUID of the model run to update\n", "    best_result_table: DataFrame (will be converted to JSON)\n", "    all_regression_results: List of regression results\n", "    best_features: List of best features\n", "    all_feature_sets: List of all feature sets (will be stored as all_features in database)\n", "    \n", "    Returns:\n", "    bool: True if update was successful, False otherwise\n", "    \"\"\"\n", "    if not conn or conn.closed:\n", "        logging.error(\"Invalid database connection\")\n", "        return False\n", "    \n", "    # Helper function to safely convert pandas DataFrames to serializable dictionaries\n", "    def convert_pandas_to_dict(obj):\n", "        if isinstance(obj, pd.DataFrame):\n", "            # Convert DataFrame to dict with records orientation and convert datetime columns to ISO format\n", "            for col in obj.select_dtypes(include=['datetime64']).columns:\n", "                obj[col] = obj[col].astype(str)\n", "            return json.loads(obj.to_json(orient='records', date_format='iso'))\n", "        elif isinstance(obj, pd.Series):\n", "            # Handle datetime Series\n", "            if pd.api.types.is_datetime64_any_dtype(obj):\n", "                obj = obj.astype(str)\n", "            return json.loads(obj.to_json())\n", "        elif isinstance(obj, dict):\n", "            # Handle dictionaries - recursively process all values\n", "            return {k: convert_pandas_to_dict(v) for k, v in obj.items()}\n", "        elif isinstance(obj, list):\n", "            # Handle lists - recursively process all items\n", "            return [convert_pandas_to_dict(item) for item in obj]\n", "        elif isinstance(obj, (datetime.datetime, datetime.date)):\n", "            # Convert datetime objects to ISO format string\n", "            return obj.isoformat()\n", "        else:\n", "            return obj\n", "    \n", "    try:\n", "        # Build dynamic SQL query based on provided parameters\n", "        update_fields = []\n", "        params = []\n", "        \n", "        # Process best_result_table if provided\n", "        if best_result_table is not None:\n", "            serialized_best_result_table = convert_pandas_to_dict(best_result_table)\n", "            update_fields.append(\"best_result_table = %s\")\n", "            params.append(Json(serialized_best_result_table))\n", "        \n", "        # Process all_regression_results if provided\n", "        if all_regression_results is not None:\n", "            serialized_regression_results = convert_pandas_to_dict(all_regression_results)\n", "            update_fields.append(\"model_results = %s\")\n", "            params.append(Json(serialized_regression_results))\n", "        \n", "        # Process best_features if provided\n", "        if best_features is not None:\n", "            serialized_best_features = convert_pandas_to_dict(best_features)\n", "            update_fields.append(\"best_features = %s\")\n", "            params.append(Json(serialized_best_features))\n", "        \n", "        # Process all_feature_sets if provided (stored as all_features in database)\n", "        if all_feature_sets is not None:\n", "            serialized_all_feature_sets = convert_pandas_to_dict(all_feature_sets)\n", "            update_fields.append(\"all_features = %s\")\n", "            params.append(Json(serialized_all_feature_sets))\n", "        \n", "        # Add updated_at timestamp\n", "        update_fields.append(\"updated_at = CURRENT_TIMESTAMP\")\n", "        \n", "        # If no fields to update, return early\n", "        if not update_fields:\n", "            logging.warning(\"No fields provided for update\")\n", "            return False\n", "        \n", "        # Create a cursor\n", "        cursor = conn.cursor()\n", "        \n", "        # Build the SQL query\n", "        update_query = f\"\"\"\n", "        UPDATE model_run_data\n", "        SET {', '.join(update_fields)}\n", "        WHERE model_run_id = %s\n", "        \"\"\"\n", "        \n", "        # Add the model_run_id to params\n", "        params.append(model_run_id)\n", "        \n", "        # Execute the query with parameters\n", "        cursor.execute(update_query, params)\n", "        \n", "        # Commit the transaction\n", "        conn.commit()\n", "        \n", "        # Check if any rows were updated\n", "        rows_updated = cursor.rowcount\n", "        if rows_updated > 0:\n", "            print(f\"Successfully updated {rows_updated} row(s) for model_run_id: {model_run_id}\")\n", "            return True\n", "        else:\n", "            print(f\"No rows were updated for model_run_id: {model_run_id}\")\n", "            return False\n", "    \n", "    except Exception as e:\n", "        print(f\"Error updating database: {e}\")\n", "        if conn:\n", "            conn.rollback()\n", "        return False\n", "    \n", "    finally:\n", "        # Close the cursor if it exists\n", "        if 'cursor' in locals() and cursor is not None:\n", "            cursor.close()\n", "\n"]}, {"cell_type": "markdown", "id": "8469ff1e", "metadata": {}, "source": ["# main function to  update best result table"]}, {"cell_type": "code", "execution_count": 176, "id": "6fc33dfe", "metadata": {}, "outputs": [], "source": ["def update_channels():\n", "    \n", "    db_conn = initialize_db_connection()\n", "    model_run_id=\"00000000-0000-0000-0000-000000000001\"\n", "\n", "    model_data = extract_model_data(conn, model_run_id)\n", "    \n", "    all_features=model_data['all_features']\n", "    best_result_table = model_data['best_result_table']\n", "    best_features = model_data['best_features']\n", "    all_regression_results=model_data['model_results']\n", "    date_column = model_data['date_column']\n", "    id_column = model_data['id_column']\n", "    target_column = model_data['target_column']\n", "    promotional_columns = model_data['promotional_columns']\n", "    start_date = model_data['start_date']\n", "    end_date = model_data['end_date']\n", "    date_format = model_data['date_format']\n", "\n", "    start_date = pd.to_datetime(start_date)\n", "    end_date = pd.to_datetime(end_date)\n", "    file_path='../dummy1.xlsx'\n", "\n", "    df=process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date,date_format)\n", "    \n", "    channel_updates = [\n", "        ('PDE', 10, 'Log'),\n", "        ('<PERSON><PERSON>', 30, 'Root8')\n", "    ]\n", "\n", "    df,updated_features = update_multiple_channel_transformations(\n", "                        df,id_column,best_features,channel_updates\n", "                    )\n", "                    \n", "                    # Run regression again with the updated features\n", "    print(\"\\nRunning regression with updated channel transformations...\")\n", "                    \n", "                    # Update best_features with the new feature list\n", "    best_features = updated_features\n", "    print(f\"best features: {best_features}\")\n", "    \n", "    all_features[0]=best_features\n", "                    \n", "                    # Perform regression with updated feature set\n", "    updated_result_table = perform_regression(\n", "                        df, \n", "                        updated_features, \n", "                        target_column, \n", "                        date_column, \n", "                        start_date, \n", "                        end_date\n", "                    )\n", "                    \n", "                    # Update best_result_table with the new results\n", "    if isinstance(updated_result_table, pd.DataFrame) and not updated_result_table.empty:\n", "        best_result_table = updated_result_table\n", "                        \n", "                        # Also update in all_regression_results if it exists\n", "    if all_regression_results and len(all_regression_results) > 0:\n", "        all_regression_results[0] = best_result_table\n", "    \n", "    print(f\"best result table: {best_result_table}\")\n", "    \n", "    update_successful = update_model_specific_fields(\n", "        db_conn,\n", "        model_run_id,\n", "        best_result_table=best_result_table,\n", "        all_regression_results=all_regression_results,\n", "        best_features=best_features,\n", "        all_feature_sets=all_features\n", "    )\n", "    \n", "    close_db_connection(db_conn)"]}, {"cell_type": "code", "execution_count": 178, "id": "5616c2cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'>\n", "Date conversion: 95/95 dates successfully converted\n", "Dropped 0 rows with invalid dates\n", "[PDE] Removed 1 outliers (mu ± 3σ) - 0.00% drop\n", "Total outliers removed: 1\n", "Remaining rows: 94\n", "Outlier summary:\n", "  channel  before  after  Percentage drop\n", "0     PDE    5867   5867         0.000000\n", "1   Copay    4058   4058         0.000000\n", "2     NRx   10584  10495         0.840892\n", "Type of df[date_column]: <class 'pandas._libs.tslibs.timestamps.Timestamp'>\n", "Starting multiple channel updates. Initial features: 3\n", "\n", "Updating channel: PDE\n", "Current feature: PDE_Adstock_10_Root9\n", "New feature name will be: PDE_Adstock_10_Log\n", "Applying adstock with rate 10...\n", "Created adstock column: PDE_Adstock_10\n", "Applying transformation: Log...\n", "Created transformed column: PDE_Adstock_10_Log\n", "Added PDE_Adstock_10_Log to dataframe\n", "Updated features: Replaced PDE_Adstock_10_Root9 with PDE_Adstock_10_Log\n", "\n", "Updating channel: Copay\n", "Current feature: Copay_Adstock_30_Root8\n", "New feature name will be: Copay_Adstock_30_Root8\n", "Applying adstock with rate 30...\n", "Created adstock column: Copay_Adstock_30\n", "Applying transformation: Root8...\n", "Created transformed column: Copay_Adstock_30_Root8\n", "Added Copay_Adstock_30_Root8 to dataframe\n", "Updated features: Replaced Copay_Adstock_30_Root8 with Copay_Adstock_30_Root8\n", "\n", "Multiple channel updates complete. Total features in updated list: 3\n", "\n", "Running regression with updated channel transformations...\n", "best features: ['NRx_lag_1', 'PDE_Adstock_10_Log', 'Copay_Adstock_30_Root8']\n", "best result table:                   Channel   Estimate  Impact Percentage   P-Value  \\\n", "0               NRx_lag_1  -0.126892         -12.791495  0.372925   \n", "1      PDE_Adstock_10_Log  20.188197          78.055567  0.247213   \n", "2  Copay_Adstock_30_Root8   0.171073           4.043321  0.616006   \n", "3               Intercept  33.226142          30.692607  0.636768   \n", "\n", "   Effectiveness  Linear Activity        R²  Adjusted R²  \\\n", "0    -755.514783           5954.0  0.043712     -0.01254   \n", "1   68437.986180           3390.0  0.043712     -0.01254   \n", "2     424.259966           2480.0  0.043712     -0.01254   \n", "3            NaN              NaN  0.043712     -0.01254   \n", "\n", "   Total Modeled Activity  Modeled Sales  Actual Sales  \n", "0             6002.000000         5954.0          5954  \n", "1              230.205233         5954.0          5954  \n", "2             1407.235143         5954.0          5954  \n", "3                     NaN         5954.0          5954  \n", "Successfully updated 1 row(s) for model_run_id: 00000000-0000-0000-0000-000000000001\n"]}], "source": ["update_channels()"]}, {"cell_type": "code", "execution_count": 40, "id": "5a6c45a2", "metadata": {}, "outputs": [], "source": ["def interactive_mmm_chatbot(df, target_column, date_column, promo_channels, all_transformed_features, best_result_table, params, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel, spends_df, spend_channel_column, spend_column, historical_df, historical_channel_column, historical_impact):\n", "    import pandas as pd\n", "    import matplotlib.pyplot as plt\n", "    import seaborn as sns\n", "    import numpy as np\n", "    import json\n", "    import logging\n", "    from datetime import datetime\n", "    import matplotlib.dates as mdates\n", "    from statsmodels.stats.outliers_influence import variance_inflation_factor\n", "    from matplotlib_venn import venn2\n", "    import sys\n", "    import io\n", "    from contextlib import redirect_stdout\n", "    import traceback\n", "\n", "    # Knowledge base of common MMM analysis functions\n", "    knowledge_base = \"\"\"\n", "    These are some of the functions that are commonly used in marketing mix to answer user questions\n", "    def month_on_month_summary(df: pd.DataFrame, date_column: str, target_column: str, promo_columns: list):\n", "\n", "        logging.info(\"Generating month-on-month summary visualization.\")\n", "        df_filtered=df.copy(deep=True)\n", "        # Convert date column to datetime format\n", "        df_filtered[date_column] = pd.to_datetime(df_filtered[date_column])\n", "\n", "        # Filter the DataFrame based on the date range\n", "        # df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "        df_grouped = df_filtered.groupby(date_column).sum()\n", "\n", "        plt.figure(figsize=(12, 6))\n", "\n", "        # Plot promotional columns as line plots\n", "        sns.lineplot(data=df_grouped[promo_columns], palette='tab10', linewidth=2)\n", "\n", "        # Plot target column as a filled area plot\n", "        plt.fill_between(df_grouped.index, df_grouped[target_column], color='gray', alpha=0.3, label=target_column)\n", "\n", "        # Format the x-axis with readable date formatting\n", "        plt.gca().xaxis.set_major_locator(mdates.MonthLocator())  # Show every month\n", "        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))  # Format as \"Month Year\"\n", "        plt.xticks(rotation=45)  # Rotate the labels for better readability\n", "\n", "        plt.title(\"Month-on-Month Summary\")\n", "        plt.xlabel(\"Time Period\")\n", "        plt.ylabel(\"Value\")\n", "        plt.legend()\n", "        plt.tight_layout()  # Adjust layout to fit rotated labels\n", "        plt.show(block=False)\n", "        plt.pause(0.1)\n", "\n", "    Correlation summary\n", "    def correlation_summary(df: pd.DataFrame, promo_columns: list, target_column: str, date_column: str, start_date: str, end_date: str):\n", "        import pandas as pd\n", "        import seaborn as sns\n", "        import matplotlib.pyplot as plt\n", "        import matplotlib.dates as mdates\n", "        import logging\n", "\n", "        try:\n", "            # Calculate correlation matrix\n", "            promo_cols = [col for col in promo_channels if col in df.columns]\n", "            if not promo_cols:\n", "            print(\"No promotional channels found in the DataFrame.\")\n", "\n", "            else:\n", "            correlation_data = df[promo_cols + [target_column]].corr(method='pearson')\n", "\n", "            # Plot the heatmap for correlations\n", "            plt.figure(figsize=(10, 6))\n", "            sns.heatmap(correlation_data, annot=True, cmap=\"coolwarm\", fmt=\".2f\", linewidths=0.5)\n", "            plt.title(\"Correlation Summary\")\n", "            plt.tight_layout()\n", "            plt.show()\n", "            print(\"Correlation heatmap displayed. Check for strong positive or negative correlations between marketing activities (values close to 1 or -1).\")\n", "\n", "except Exception as e:\n", "    logging.error(f\"An error occurred during correlation analysis: {e}\")\n", "    print(\"An error occurred during correlation analysis. Please check the logs for details.\")\n", "\n", "    Aggregate channel summary\n", "    def aggregate_channel_summary(df: pd.DataFrame, id_column: str, promo_columns: list, target_column: str):\n", "\n", "        Compute total, min, max, avg values for each channel, along with reach and frequency.\n", "\n", "        logging.info(\"Computing aggregated channel summary.\")\n", "        summary = df[promo_columns + [target_column]].agg(['sum', 'min', 'max', 'mean'])\n", "\n", "        reach = {col: df[df[col] > 0][id_column].nunique() for col in promo_columns}\n", "        frequency = {col: summary.loc['sum', col] / reach[col] if reach[col] > 0 else 0 for col in promo_columns}\n", "\n", "        reach_df = pd.DataFrame.from_dict(reach, orient='index', columns=['Reach'])\n", "        frequency_df = pd.DataFrame.from_dict(frequency, orient='index', columns=['Frequency'])\n", "\n", "        summary = pd.concat([summary, reach_df.T, frequency_df.T])\n", "        summary= summary.reset_index()\n", "        summary.set_index('index', inplace=True)\n", "        print(\"Aggregated Channel Summary with Reach and Frequency:\")\n", "        print(summary)\n", "    \n", "    Tier Based summary\n", "    def tier_based_summary(df: pd.DataFrame, tier_column: str, promo_columns: list, target_column: str):\n", "\n", "        Compute total, min, max, avg values for each channel at the tier level, along with reach and frequency.\n", "\n", "        logging.info(\"Computing tier-based aggregated summary.\")\n", "        tier_summary = df.groupby(tier_column).agg({col: ['sum', 'min', 'max', 'mean'] for col in promo_columns + [target_column]})\n", "        tier_reach = df.groupby(tier_column)[promo_columns].apply(lambda x: x.gt(0).sum())\n", "\n", "        tier_reach.index = tier_reach.index.astype(str)  # Convert to string for consistency\n", "        tier_summary.index = tier_summary.index.astype(str)\n", "\n", "        tier_frequency = tier_summary.loc[:, (slice(None), 'sum')].div(tier_reach.replace(0, 1))\n", "        tier_frequency.columns = pd.MultiIndex.from_tuples([(col, 'frequency') for col in promo_columns])\n", "\n", "        tier_summary = pd.concat([tier_summary, tier_reach.add_suffix('_reach'), tier_frequency], axis=1)\n", "\n", "        print(\"Tier-Based Aggregated Channel Summary with Reach and Frequency:\")\n", "        print(tier_summary.reset_index())\n", "\n", "    Overlap_summary\n", "    def overlap_summary(df: pd.DataFrame, id_column: str, target_column: str, promo_columns: str, date_column: str, start_date: str, end_date: str):\n", "\n", "        Generate a Venn diagram and summary table showing overlap between sales and selected promotion at ID level.\n", "\n", "        logging.info(\"Generating overlap summary.\")\n", "        df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "        for promo_column in promo_columns:\n", "            df_filtered[promo_column] = df_filtered[promo_column].fillna(0)\n", "            df_grouped = df_filtered.groupby(id_column).agg({target_column: 'sum', promo_column: 'sum'}).reset_index()\n", "            df_grouped['promo_flag'] = df_grouped[promo_column] > 0\n", "            df_grouped['sales_flag'] = df_grouped[target_column] > 0\n", "\n", "            both = df_grouped[df_grouped['promo_flag'] & df_grouped['sales_flag']][id_column].nunique()\n", "            only_sales = df_grouped[~df_grouped['promo_flag'] & df_grouped['sales_flag']][id_column].nunique()\n", "            only_promo = df_grouped[df_grouped['promo_flag'] & ~df_grouped['sales_flag']][id_column].nunique()\n", "            neither = df_grouped[~df_grouped['promo_flag'] & ~df_grouped['sales_flag']][id_column].nunique()\n", "\n", "            plt.figure(figsize=(6, 6))\n", "            venn2(subsets=(only_sales, only_promo, both), set_labels=(\"Sales\", promo_column))\n", "            plt.title(\"Overlap Summary\")\n", "            plt.show(block=False)\n", "            plt.pause(0.1)\n", "\n", "\n", "            summary_table = pd.DataFrame({\n", "                \"Category\": [\"Sales & Promotion\", \"Sales Only\", \"Promotion Only\", \"Neither\"],\n", "                \"Unique IDs\": [both, only_sales, only_promo, neither],\n", "                \"Total Sales\": [df_grouped.loc[df_grouped['sales_flag'], target_column].sum()] * 4,\n", "                \"Total Promotional Volume\": [df_grouped.loc[df_grouped['promo_flag'], promo_column].sum()] * 4\n", "            })\n", "            print(\"Overlap Summary Table:\")\n", "            print(summary_table)\n", "\n", "    Box plot summaru generation\n", "    def box_plot_summary(df: pd.DataFrame, target_column: str, promo_columns: list, date_column: str, start_date: str, end_date: str):\n", "\n", "        Generate box plots for the target variable and promotional variables over time.\n", "\n", "        logging.info(\"Generating box plot summaries.\")\n", "        df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]\n", "\n", "        # selected_promo_columns = input(\"Enter promotional variable columns to include in box plots (comma-separated): \").split(',')\n", "\n", "        plt.figure(figsize=(12, 6))\n", "        sns.boxplot(x=date_column, y=target_column, data=df_filtered)\n", "        plt.xticks(rotation=45)\n", "        plt.title(f\"Box Plot of {target_column} Over Time\")\n", "        plt.show(block=False)\n", "        plt.pause(0.1)\n", "\n", "\n", "        for promo in promo_columns:\n", "            if promo in df_filtered.columns:\n", "                plt.figure(figsize=(12, 6))\n", "                sns.boxplot(x=date_column, y=promo, data=df_filtered)\n", "                plt.xticks(rotation=45)\n", "                plt.title(f\"Box Plot of {promo} Over Time\")\n", "                plt.show(block=False)\n", "                plt.pause(0.1)\n", "    \n", "                \n", "    ROI CALCULATION ENFORCEMENT:\n", "- CRITICAL: For ANY question related to ROI calculations, you MUST use EXACTLY this formula:\n", "  \n", "  ROI = (unit_price * estimate*Total Modeled Activity) / spends\n", "  unit_price ALWAYS ASK USER\n", "  \n", "  Where:\n", "  - unit_price: The price per unit of the target variable (obtain from user or use standard value)\n", "  - impact: The total impact or modeled activity for the channel (from best_result_table)\n", "  - spends: The spending amount for the channel (from spends_df)\n", "\n", "- NEVER deviate from this formula when calculating ROI\n", "- NEVER invent your own ROI calculation method\n", "- ALWAYS map between base channel names in spends_df and transformed channel names in best_result_table\n", "- ALWAYS include error handling for zero spends (to avoid division by zero)\n", "- ALWAYS show the exact formula being used in a comment\n", "    def ROI(spends_df, df):  # df is best_result_table\n", "        unit_price = float(input(\"Enter the price of each unit: \"))\n", "        roi_dict = {}\n", "\n", "        for i, row in spends_df.iterrows():\n", "            channel = row['Channel']\n", "            spend = row['Spends']\n", "\n", "            # Find matching variable in best_result_table\n", "            matched_row = df[df['Variable'].str.startswith(channel + '_Adstock')]\n", "\n", "            if not matched_row.empty:\n", "                estimate = matched_row['Estimate'].values[0]\n", "                modeled_activity = matched_row['Total Modeled Activity'].values[0]\n", "                returns = unit_price * estimate *modeled_activity\n", "                roi = returns / spend if spend != 0 else 0\n", "                roi_dict[channel] = roi\n", "            else:\n", "                roi_dict[channel] = None  # or 0 or \"No match\"\n", "\n", "        return roi_dict\n", "\n", "EXAMPLE code pattern for filtering out non-marketing variables\n", "# Example code pattern for filtering out non-marketing variables\n", "def get_marketing_variables_only(best_result_table, promo_channels):\n", "     marketing_rows = []\n", "    \n", "    for _, row in best_result_table.iterrows():\n", "        var_name = row.get('Channel', '')\n", "        # Check if this is a legitimate marketing variable\n", "        is_marketing_var = False\n", "        \n", "        # Check if the variable is derived from any promo channel\n", "        for channel in promo_channels:\n", "            if var_name.startswith(channel + '_') or var_name == channel:\n", "                is_marketing_var = True\n", "                break\n", "        \n", "        # Explicitly exclude non-marketing variables\n", "        if (var_name == 'Intercept' or \n", "            var_name.startswith('T') or \n", "            'time' in var_name.lower() or \n", "            'trend' in var_name.lower() or \n", "            'Rtime' in var_name or\n", "            'season' in var_name.lower() or\n", "            'month' in var_name.lower() or\n", "            'week' in var_name.lower() or\n", "            'quarter' in var_name.lower() or\n", "            '_lag_' in var_name.lower() or  # Exclude variables with '_lag_' pattern\n", "            (var_name.lower().endswith('lag') or  # Ends with 'lag'\n", "             any(var_name.lower().endswith(f'lag_{i}') for i in range(10)))):\n", "            is_marketing_var = False\n", "            \n", "        if is_marketing_var:\n", "            marketing_rows.append(row)\n", "    \n", "    return pd.DataFrame(marketing_rows)\n", "\n", "# Then use this when answering questions about top marketing drivers\n", "marketing_only_table = get_marketing_variables_only(best_result_table, promo_channels)\n", "top_n_drivers = marketing_only_table.sort_values('Impact Percentage', ascending=False).head(3)\n", "\n", "\n", "    \"\"\"\n", "\n", "    # Verify that Gemini API is available\n", "    try:\n", "        # Assuming genai is already configured with API key\n", "        # model = genai.GenerativeModel(\"gemini-1.5-pro-latest\")\n", "        model = genai.GenerativeModel(\"gemini-2.0-flash\")\n", "        print(\"Gemini API initialized successfully.\")\n", "    except Exception as e:\n", "        print(f\"Error: Could not initialize Gemini API: {e}\")\n", "        print(\"Please check your API key configuration.\")\n", "        print(\"Exiting chatbot.\")\n", "        return\n", "\n", "    # Ensure the date column is in datetime format\n", "    if df[date_column].dtype != 'datetime64[ns]':\n", "        try:\n", "            df[date_column] = pd.to_datetime(df[date_column])\n", "            print(f\"Converted {date_column} to datetime format.\")\n", "        except Exception as e:\n", "            print(f\"Warning: Could not convert {date_column} to datetime format: {e}\")\n", "\n", "    # Generate column summaries for Gemini context\n", "    column_summaries = {}\n", "    for col in df.columns:\n", "        if col in [date_column, target_column] + promo_channels:\n", "            try:\n", "                summary = {\n", "                    \"min\": float(df[col].min()),\n", "                    \"max\": float(df[col].max()),\n", "                    \"mean\": float(df[col].mean()),\n", "                    \"median\": float(df[col].median())\n", "                }\n", "                column_summaries[col] = summary\n", "            except:\n", "                pass\n", "\n", "    # Create a dataframe schema for Gemini\n", "    df_schema = {\n", "        \"columns\": list(df.columns),\n", "        \"shape\": df.shape,\n", "        \"dtypes\": {col: str(df[col].dtype) for col in df.columns}\n", "    }\n", "\n", "    # Extract model coefficients for easier access\n", "    # Extract model coefficients and other important info for easier access\n", "    model_coefficients = {}\n", "    channel_impacts = {}\n", "    channel_effectiveness = {}\n", "    channel_transformations = {}\n", "\n", "    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:\n", "        try:\n", "            for _, row in best_result_table.iterrows():\n", "                channel = row.get('Channel', '')\n", "                if channel:\n", "                    # Store various metrics from the best_result_table\n", "                    model_coefficients[channel] = row.get('Estimate', 0)\n", "                    channel_impacts[channel] = row.get('Impact Percentage', 0)\n", "                    channel_effectiveness[channel] = row.get('Effectiveness', 0)\n", "\n", "                    # For questions about contributions/impact\n", "                    if 'Impact Percentage' in row:\n", "                        channel_impacts[channel] = row['Impact Percentage']\n", "        except Exception as e:\n", "            print(f\"Warning: Could not extract model details: {e}\")\n", "    print(f\"Channel impacts:{channel_impacts}\")\n", "\n", "    # Create a summary of the best_result_table structure for context\n", "    best_result_columns = []\n", "    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:\n", "        best_result_columns = list(best_result_table.columns)\n", "\n", "    # Function to attempt code execution with retries\n", "    def execute_code_with_retries(code_to_execute, local_vars, max_attempts=6):\n", "        \"\"\"\n", "        Attempts to execute the given code multiple times until success or max attempts reached.\n", "        \n", "        Args:\n", "            code_to_execute: String containing Python code to execute\n", "            local_vars: Dictionary of local variables for execution context\n", "            max_attempts: Maximum number of attempts to try executing the code\n", "            \n", "        Returns:\n", "            tuple: (success flag, output text, error message if any)\n", "        \"\"\"\n", "        for attempt in range(1, max_attempts + 1):\n", "            print(f\"\\nAttempt {attempt}/{max_attempts}...\")\n", "            \n", "            # Create a string buffer to capture output\n", "            output_buffer = io.StringIO()\n", "            error_message = None\n", "            success = False\n", "            \n", "            # Make a copy of the local variables to prevent pollution between attempts\n", "            local_vars_copy = {k: v for k, v in local_vars.items()}\n", "            \n", "            try:\n", "                # Redirect stdout to capture print statements\n", "                with redirect_stdout(output_buffer):\n", "                    # Execute the code\n", "                    exec(code_to_execute, globals(), local_vars_copy)\n", "                \n", "                # Check if there's any output and if it contains error messages\n", "                output_text = output_buffer.getvalue()\n", "                if \"An error occurred:\" in output_text or \"Error:\" in output_text:\n", "                    error_message = output_text\n", "                    raise Exception(f\"Execution produced an error: {output_text}\")\n", "                \n", "                # If we get here, execution was successful\n", "                success = True\n", "                return success, output_text, None\n", "                    \n", "            except Exception as e:\n", "                error_message = f\"Error: {str(e)}\\n{traceback.format_exc()}\"\n", "            \n", "            # If we're here, this attempt failed\n", "            if attempt < max_attempts:\n", "                print(f\"Attempt {attempt} failed: {error_message}\")\n", "                print(\"Trying again with a slightly modified approach...\")\n", "                \n", "                # For next attempt, slightly modify the prompt to get different code\n", "                new_context = context + f\"\\n\\nPrevious attempt failed with error: {error_message}\\nPlease provide an alternative implementation that avoids this issue.\"\n", "                try:\n", "                    response = model.generate_content(new_context)\n", "                    code_to_execute = response.text.strip()\n", "                    \n", "                    # Clean up the code if it starts with ```python and ends with ```\n", "                    if code_to_execute.startswith(\"```python\"):\n", "                        code_to_execute = code_to_execute[9:]\n", "                    if code_to_execute.startswith(\"```\"):\n", "                        code_to_execute = code_to_execute[3:]\n", "                    if code_to_execute.endswith(\"```\"):\n", "                        code_to_execute = code_to_execute[:-3]\n", "                    \n", "                    code_to_execute = code_to_execute.strip()\n", "                    print(\"\\nNew code generated for next attempt.\")\n", "                except Exception as retry_error:\n", "                    print(f\"Error generating new code: {retry_error}\")\n", "                    # If we can't get new code, use the original one\n", "            else:\n", "                print(f\"All {max_attempts} attempts failed.\")\n", "                return False, \"\", error_message\n", "        \n", "        # If we reach here, all attempts failed\n", "        return False, \"\", \"Maximum attempts reached without success.\"\n", "\n", "    # Main chatbot loop\n", "    while True:\n", "        user_question = input(\"\\nQuestion: \").strip()\n", "\n", "        if user_question.lower() == 'exit':\n", "            print(\"Exiting chatbot. Thank you for using the Marketing Mix Modeling Pipeline!\")\n", "            break\n", "\n", "        # Analyze question type to determine if it needs visualization or text analysis\n", "        question_needs_viz = any(kw in user_question.lower() for kw in [\n", "            'show', 'graph', 'plot', 'chart', 'visualize', 'trend', 'compare', 'timeline',\n", "            'over time', 'pattern'\n", "        ])\n", "\n", "        # New prompt that first checks the knowledge base for pre-defined functions\n", "        context = f\"\"\"\n", "I'm a marketing mix modeling chatbot generating Python code to answer questions about a marketing mix model.\n", "\n", "USER QUESTION: \"{user_question}\"\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> BASE OF PRE-DEFINED FUNCTIONS:\n", "IMPORTANT IMPLEMENTATION NOTES:\n", "1. The knowledge base contains EXAMPLE functions only - they are NOT actually implemented in the runtime environment.\n", "2. You MUST generate COMPLETE, SELF-CONTAINED code that includes ALL necessary imports and function definitions.\n", "3. Even if a similar function exists in the knowledge base, you must re-implement it fully in your response.\n", "4. DO NOT reference or call any functions from the knowledge base directly - they don't exist at runtime.\n", "5. Include explicit imports for all libraries used (matplotlib.pyplot as plt, seaborn as sns, etc.) at the beginning of your code.\n", "6. All visualization code must include proper exception handling and end with plt.show() to display results.\n", "7. Ensure your code uses ONLY the variables provided in the execution context:\n", "  - df, target_column, date_column, promo_channels, all_transformed_features, best_result_table,\n", "  - params, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel,\n", "  - model_coefficients, channel_impacts, channel_effectiveness, spends_df\n", "8. You can use any standard Python libraries that are already imported in the main function.\n", "9. The knowledge base functions are for REFERENCE ONLY to understand the expected style and approach.\n", "{knowledge_base}\n", "## CRITICAL: VA<PERSON><PERSON>LE ACCESS INSTRUCTIONS\n", "Your code will be executed in an environment where all variables are available, but you MUST:\n", "1. First verify the existence of ALL variables before using them\n", "2. Use ONLY the local variables already defined in the execution environment\n", "3. DO NOT try to import or redefine these variables\n", "4. ALWAYS check if dictionaries like channel_impacts exist before accessing them\n", "\n", "## CRITICAL: VARIABLE VERIFICATION CODE PATTERN\n", "ALWAYS include this pattern at the beginning of your code:\n", "\n", "```python\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df', 'spend_column', 'spend_channel_column']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {{', '.join(missing_vars)}}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{{var_name}} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{{var_name}} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{{var_name}} = []\")\n", "        else:\n", "            exec(f\"{{var_name}} = None\")\n", "\n", "For Correlation Question:\n", "If pharses like final transformed activity/ final features / final activity or similar are used then refer to the {best_result_table} to get the final heatmap with only the 'marketing activities'\n", "\n", "For questions related to sales perspective/profot/loss:\n", "After calculations and final output give a small sentance as well describing the output always\n", "\n", "## CRITICAL: ROI CALCULATION \n", "For ANY question related to ROI calculations, you MUST use THIS EXACT ROI function WITHOUT modification:\n", "- CRITICAL: For ANY question related to ROI calculations, you MUST use EXACTLY this formula:\n", "  \n", "  ROI = (unit_price * estimate*Total Modeled Activity) / spends\n", "  \n", "  Where:\n", "  - unit_price: The price per unit of the target variable (obtain from user or use standard value)\n", "  - impact: The total impact or modeled activity for the channel (from best_result_table)\n", "  - spends: The spending amount for the channel (from spends_df)\n", "\n", "- NEVER deviate from this formula when calculating ROI\n", "- NEVER invent your own ROI calculation method\n", "- ALWAYS map between base channel names in spends_df and transformed channel names in best_result_table\n", "- ALWAYS include error handling for zero spends (to avoid division by zero)\n", "- ALWAYS show the exact formula being used in a comment\n", "VARIABLE INITIALIZATION REQUIREMENTS\n", "\n", "CRITICAL: You MUST initialize ALL your own variables before use\n", "Initialize channel_name = None at the beginning of your code\n", "Initialize all variables that might be referenced later with appropriate defaults\n", "For loops, define iterator variables explicitly before usage\n", "For dictionary access, check if keys exist before accessing them\n", "\n", "CODE SAFETY REQUIREMENTS\n", "\n", "Use descriptive, unique variable names (e.g., impact_ch_name, base_ch_name)\n", "NEVER use generic variable names like 'channel', 'ch', or 'ch_name' in loops\n", "For dictionary iteration, use clear naming: for base_ch_name, values in dict_name.items():\n", "Before accessing dictionaries: ALWAYS use .get() with default values\n", "Before accessing DataFrame columns: ALWAYS check if columns exist\n", "Before any list or DataFrame index access: ALWAYS check length\n", "Initialize result containers (lists, dicts) BEFORE any loop\n", "Handle potential errors with try/except blocks\n", "\n", "AVAILABLE VARIABLES\n", "This code will be executed directly in the Python environment with access to these variables:\n", "\n", "df: The pandas DataFrame containing all data\n", "target_column: Name of the target variable (string)\n", "date_column: Name of the date column (string)\n", "promo_channels: List of promotional channel names (list of strings)\n", "all_transformed_features: List of all transformed feature names (list of strings)\n", "best_result_table: DataFrame with results from the best regression model\n", "params: Dictionary with all analysis parameters\n", "transformed_channels_by_promo: Dictionary with base channel as key and its top 3 transformations\n", "adstocked_channels_by_promo: Dictionary with base channel as key and its top 3 adstocks used\n", "adstock_range_channel: Dictionary of adstock range applied for each promo_channel\n", "model_coefficients: Dictionary mapping variable names to their coefficients\n", "channel_impacts: Dictionary of channel impact percentages (contributions)\n", "channel_effectiveness: Dictionary of channel effectiveness metrics\n", "spends_df: DataFrame containing channel names and their corresponding spend amounts\n", "spend_column: Spends column name of spends_df\n", "spend_channel_column: Channel name of spends_df\n", "historical_df: DataFrame containing channel names and their corresponding contributions of the history\n", "historical_impact: contribution column name of historical_df\n", "historical_channel_column: Channel name of historical_df\n", "\n", "IMPORTS\n", "Always include these imports at the beginning of your code:\n", "pythonimport pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "DATASET CONTEXT\n", "\n", "Target variable: {target_column}\n", "Date column: {date_column}\n", "Promotional channels: {', '.join(promo_channels)}\n", "Time period: {df[date_column].min().strftime('%Y-%m-%d') if hasattr(df[date_column].min(), 'strftime') else df[date_column].min()} to {df[date_column].max().strftime('%Y-%m-%d') if hasattr(df[date_column].max(), 'strftime') else df[date_column].max()}\n", "Dataset shape: {df.shape}\n", "Spend information available in spends_df with columns {spend_channel_column} and {spend_column}\n", "Historical data in historical_df with columns {historical_channel_column} and {historical_impact}\n", "\n", "MODEL CONTEXT:\n", "- Model coefficients: {json.dumps(model_coefficients)}\n", "- Channel impacts (contributions): {json.dumps(channel_impacts)}\n", "- Channel effectiveness: {json.dumps(channel_effectiveness)}\n", "- Transformed channels by promo: Dictionary with base channel as key and its top 3 transformations\n", "- Adstocked channels by promo: Dictionary with base channel as key and its top 3 adstocks used\n", "- Adstock range by channel: Dictionary of adstock range applied for each promo_channel\n", "- Best result table columns: {best_result_columns}\n", "- T1, T2, T3, etc. are trend variables used to understand trends in the data and NOT considered as promotions\n", "- NOTE: When calculating ROI, you'll need to map between base channel names in spends_df (like 'PDE') and transformed channel names in best_result_table (like 'PDE_Adstock_10_Log')\n", "\n", "VARIABLE CLASSIFICATION:\n", "- CRITICAL: When asked about \"drivers\", \"channels\", \"marketing factors\", \"top N marketing channels\", or similar terms:\n", "  * STRICTLY INCLUDE ONLY actual marketing variables (promo_channels and their transformations)\n", "  * STRICTLY EXCLUDE ALL of the following, which are NOT marketing drivers:\n", "    - Intercept/constant term (represents baseline/base sales)\n", "    - ANY trend variables (variables starting with 'T' followed by a number like T1, T2, or containing 'trend', 'time', or 'Rtime')\n", "    - ANY seasonal variables (containing 'season', 'month', 'week', 'quarter')\n", "    - ANY lag variables (containing '_lag_' or ending with 'lag' followed by a number like 'target_lag_1')\n", "    - ANY other non-promotional variables\n", "\n", "LAG VARIABLES CLASSIFICATION - CRITICAL:\n", "- Lag variables (e.g., 'target_lag_1', 'sales_lag_2', or any variable containing '_lag_') are ALWAYS considered NON-MARKETING variables\n", "- They represent the influence of past target values and MUST NEVER be included in marketing contribution analyses\n", "- When filtering variables for marketing analysis, ALWAYS exclude lag variables\n", "- Common lag variable patterns include:\n", "  * Variables containing '_lag_' anywhere in the name\n", "  * Variables ending with 'lag' followed by a number (e.g., 'revlag1')\n", "  * Any variable containing 'lag' that isn't clearly a marketing channel name\n", "- When categorizing variables, lag variables should ALWAYS be grouped with other non-marketing factors like intercept and trend\n", "\n", "\n", "CODE REQUIREMENTS\n", "\n", "Include ALL necessary imports at the beginning\n", "Use matplotlib/seaborn for visualizations with proper titles, labels, and legends\n", "End visualizations with plt.show()\n", "Include print statements with brief interpretations\n", "Format text-based results as clear, readable output\n", "Handle all potential errors gracefully\n", "Use try/except blocks liberally to catch any possible errors\n", "\n", "EXAMPLE OF PROPER VARIABLE HANDLING\n", "python# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import traceback\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients']\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "if missing_vars:\n", "    print(f\"Error: Missing required variables:  {{', '.join(missing_vars)}}\")\n", "\n", "# Initialize ALL variables\n", "channel_name = None\n", "impact_value = 0\n", "filtered_channels = []\n", "result_dict = {{}}\n", "\n", "try:\n", "    # Check if dictionary exists before using it\n", "    if 'channel_impacts' in locals() or 'channel_impacts' in globals():\n", "        # Safe dictionary access with get()\n", "        for impact_ch_name, impact_val in channel_impacts.items():\n", "            if impact_ch_name in promo_channels:\n", "                filtered_channels.append(impact_ch_name)\n", "        \n", "        # Handle edge cases\n", "        if not filtered_channels:\n", "            print(\"No matching channels found\")\n", "        else:\n", "            print(f\"Found {{len(filtered_channels)}} channels\")\n", "    else:\n", "        print(\"channel_impacts dictionary not available\")\n", "        \n", "except Exception as e:\n", "    print(f\"Error during analysis: {{str(e)}}\")\n", "    traceback.print_exc()\n", "\n", "CRITICAL_ FOR VISULIZATION\n", "When analyzing time series data with dates in YYYYMM format (e.g., 202411), please:\n", "\n", "1. Convert dates to proper datetime objects using:\n", "   `df['Date'] = pd.to_datetime(df['Date'].astype(str).str[:4] + '-' + df['Date'].astype(str).str[4:6] + '-01')`\n", "\n", "2. For questions like \"Show the average [target] for each month or quarter across the years\":\n", "   - Extract year and month components from the converted dates\n", "   - Group by month or quarter to aggregate across all years\n", "   - Calculate averages for each time period (month/quarter)\n", "   - Create visualizations showing the average values by time period with proper labels\n", "   - Include an \"Overall Average\" line or reference\n", "\n", "3. Ensure the code handles:\n", "   - Both monthly and quarterly aggregation options\n", "   - Proper sorting of months/quarters in chronological order\n", "   - Clear labels for time periods (e.g., \"<PERSON>\", \"Feb\", \"Mar\" instead of 1, 2, 3)\n", "   - Visualization of results with appropriate title, axis labels, and legend\n", "   - LABEL the points for the visulaizations\n", "\n", "The resulting analysis should make it easy to identify seasonal patterns regardless of year.\n", "\n", "I NEED COMPLETE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PYTHON CODE ONLY.\n", "\n", "\"\"\"\n", "\n", "        try:\n", "            print(\"\\nAnalyzing your question...\")\n", "            response = model.generate_content(context)\n", "            code_to_execute = response.text.strip()\n", "\n", "            # Clean up the code if it starts with ```python and ends with ```\n", "            if code_to_execute.startswith(\"```python\"):\n", "                code_to_execute = code_to_execute[9:]\n", "            if code_to_execute.startswith(\"```\"):\n", "                code_to_execute = code_to_execute[3:]\n", "            if code_to_execute.endswith(\"```\"):\n", "                code_to_execute = code_to_execute[:-3]\n", "\n", "            code_to_execute = code_to_execute.strip()\n", "            print(\"\\nGenerating analysis...\")\n", "            print(code_to_execute)\n", "\n", "            # Set up the execution environment\n", "            local_vars = {\n", "                'df': df.copy(),  # Use a copy to prevent modifications to original df\n", "                'target_column': target_column,\n", "                'date_column': date_column,\n", "                'promo_channels': promo_channels,\n", "                'all_transformed_features': all_transformed_features,\n", "                'best_result_table': best_result_table,\n", "                'params': params,\n", "                'model_coefficients': model_coefficients,\n", "                'channel_impacts': channel_impacts,\n", "                'channel_effectiveness': channel_effectiveness,\n", "                'transformed_channels_by_promo': transformed_channels_by_promo,\n", "                'adstocked_channels_by_promo': adstocked_channels_by_promo,\n", "                'adstock_range_channel': adstock_range_channel,\n", "                'spends_df': spends_df.copy(),\n", "                'spend_channel_column': spend_channel_column,\n", "                'spend_column': spend_column,\n", "                'historical_df': historical_df.copy(),\n", "                'historical_channel_column': historical_channel_column,\n", "                'historical_impact': historical_impact,\n", "                'pd': pd,\n", "                'plt': plt,\n", "                'sns': sns,\n", "                'np': np,\n", "                'datetime': datetime,\n", "                'mdates': mdates,\n", "                'variance_inflation_factor': variance_inflation_factor,\n", "                'venn2': venn2,\n", "                'logging': logging\n", "            }\n", "\n", "            # Execute the code with retries\n", "            success, output_text, error_message = execute_code_with_retries(code_to_execute, local_vars)\n", "            \n", "            if success:\n", "                print(\"\\nAnalysis completed successfully!\")\n", "                if output_text:\n", "                    print(\"\\nOutput:\")\n", "                    print(output_text)\n", "                \n", "                # Save the last generated figure if any and if it's a visualization question\n", "                if question_needs_viz and plt.get_fignums():\n", "                    filename = f\"mmm_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png\"\n", "                    plt.savefig(filename)\n", "                    print(f\"Visualization saved as {filename}\")\n", "                    plt.close('all')  # Close all figures to clear memory\n", "            else:\n", "                print(\"\\nFailed to generate analysis after multiple attempts.\")\n", "                if error_message:\n", "                    print(f\"Last error: {error_message}\")\n", "                print(\"Please try rephrasing your question or ask something different.\")\n", "\n", "        except Exception as e:\n", "            print(f\"Error generating analysis: {e}\")\n", "            print(\"Please try again with a different question.\")"]}, {"cell_type": "code", "execution_count": null, "id": "ad11d12e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 55, "id": "7d3c89fb", "metadata": {}, "outputs": [], "source": ["def chatbot():\n", "    db_conn=initialize_db_connection()\n", "    model_run_id=\"00000000-0000-0000-0000-000000000001\"\n", "    \n", "    # load dataframes\n", "    file_path=\"dummy1.xlsx\"\n", "    spends_file_path=\"spends.xlsx\"\n", "    historical_file_path=\"historical_data.xlsx\"\n", "   \n", "\n", "    df=load_file(file_path)\n", "    spends_df=load_file(spends_file_path)\n", "    historical_df=load_file(historical_file_path)\n", "\n", "    # load features from the database\n", "    model_data = extract_model_data(db_conn, model_run_id)\n", "    all_transformed_features=model_data['all_transformed_features']\n", "    transformed_channels_by_promo=model_data['transformed_channels_by_promo']\n", "    adstocked_channels_by_promo=model_data['adstocked_channels_by_promo']\n", "    adstock_range_channel=model_data['adstock_range_channel']\n", "    all_features=model_data['all_features']\n", "    best_result_table = model_data['best_result_table']\n", "    best_features = model_data['best_features']\n", "    all_regression_results=model_data['model_results']\n", "    date_column = model_data['date_column']\n", "    id_column = model_data['id_column']\n", "    target_column = model_data['target_column']\n", "    promotional_columns = model_data['promotional_columns']\n", "    start_date = model_data['start_date']\n", "    end_date = model_data['end_date']\n", "    date_format = model_data['date_format']\n", "    spend_channel_column = model_data['spend_channel_column']\n", "    spend_column = model_data['spend_column']\n", "    historical_channel_column = model_data['historical_channel_column']\n", "    historical_impact = model_data['historical_impact']\n", "\n", "    start_date = pd.to_datetime(start_date)\n", "    end_date = pd.to_datetime(end_date)\n", "    \n", "    df=process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date,date_format)\n", "    df, control_variables = add_control_variables(df, id_column, date_column)\n", "\n", "    params=setup_parameters(df, target_column, date_column, id_column, start_date, end_date, spend_channel_column,spend_column,historical_channel_column,historical_impact)\n", "    print(df)\n", "    interactive_mmm_chatbot(df, target_column, date_column, promotional_columns, all_transformed_features, best_result_table, params, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel, spends_df, spend_channel_column, spend_column, historical_df, historical_channel_column, historical_impact)\n", "   \n", "   "]}, {"cell_type": "code", "execution_count": 57, "id": "74ce52ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'>\n", "Date conversion: 95/95 dates successfully converted\n", "Dropped 0 rows with invalid dates\n", "[PDE] Removed 1 outliers (mu ± 3σ) - 0.00% drop\n", "Total outliers removed: 1\n", "Remaining rows: 94\n", "Outlier summary:\n", "  channel  before  after  Percentage drop\n", "0     PDE    5867   5867         0.000000\n", "1   Copay    4058   4058         0.000000\n", "2     NRx   10584  10495         0.840892\n", "Type of df[date_column]: <class 'pandas._libs.tslibs.timestamps.Timestamp'>\n", "Added 15 control variables\n", "    ID   Month       Date  Population  Copay  Speaker_Event  SpeakerEvent  \\\n", "2    1  202411 2024-11-01        3241     67             60            60   \n", "3    1  202412 2024-12-01           0     67             58            58   \n", "4    1  202502 2025-02-01        8922     56             43            43   \n", "5    1  202503 2025-03-01        1996     43             45            45   \n", "6    1  202504 2025-04-01        9136     83             76            76   \n", "7    1  202505 2025-05-01        4946     56             45            45   \n", "8    1  202506 2025-06-01        5106     75             60            60   \n", "9    1  202507 2025-07-01        4880     56             74            74   \n", "10   1  202508 2025-08-01        8300     75             50            50   \n", "11   1  202509 2025-09-01        6588     25             12            12   \n", "12   1  202510 2025-10-01        2411     46             70            70   \n", "13   1  202511 2025-11-01        8531     70             99            99   \n", "14   1  202512 2025-12-01        4855     35             91            91   \n", "25   2  202411 2024-11-01        2390      0             61            61   \n", "26   2  202412 2024-12-01        6040      0             56            56   \n", "27   2  202501 2025-01-01        7766      0             56            56   \n", "28   2  202502 2025-02-01        8215      0             53            53   \n", "29   2  202503 2025-03-01        9138      0             56            56   \n", "30   2  202504 2025-04-01        2502      0             47            47   \n", "31   2  202505 2025-05-01        4173      0             57            57   \n", "32   2  202506 2025-06-01        9723      0             34            34   \n", "33   2  202507 2025-07-01        3718      0             71            71   \n", "34   2  202508 2025-08-01        9097      0             56            56   \n", "35   2  202509 2025-09-01        8088      0             44            44   \n", "36   2  202510 2025-10-01        7970      0             59            59   \n", "37   2  202511 2025-11-01        6605      0             33            33   \n", "38   2  202512 2025-12-01        4382      0             82            82   \n", "49   3  202411 2024-11-01        8214     67             64            64   \n", "50   3  202412 2024-12-01        6264     43             80            80   \n", "51   3  202501 2025-01-01        9415     69             66            66   \n", "52   3  202502 2025-02-01        2082     88             78            78   \n", "53   3  202503 2025-03-01        1036     67             40            40   \n", "54   3  202504 2025-04-01        7362     37             78            78   \n", "55   3  202505 2025-05-01        3307     55             79            79   \n", "56   3  202506 2025-06-01        2405     62             38            38   \n", "57   3  202507 2025-07-01        1934     70             41            41   \n", "58   3  202508 2025-08-01        2467     77             76            76   \n", "59   3  202509 2025-09-01        4427     22             30            30   \n", "60   3  202510 2025-10-01        1626     55             36            36   \n", "61   3  202511 2025-11-01        7067     76             52            52   \n", "62   3  202512 2025-12-01        6513     70             56            56   \n", "73   4  202411 2024-11-01        2103     89             59            59   \n", "74   4  202412 2024-12-01        2041     61             68            68   \n", "75   4  202501 2025-01-01        5254     77             64            64   \n", "76   4  202502 2025-02-01        8839     54             55            55   \n", "77   4  202503 2025-03-01        7389     97             28            28   \n", "78   4  202504 2025-04-01        7052     51             42            42   \n", "79   4  202505 2025-05-01        6374     26             76            76   \n", "80   4  202506 2025-06-01        6926     47             79            79   \n", "81   4  202507 2025-07-01        2716     51             59            59   \n", "82   4  202508 2025-08-01        7629     97             76            76   \n", "83   4  202509 2025-09-01        3639     10             16            16   \n", "84   4  202510 2025-10-01        4767     51             69            69   \n", "85   4  202511 2025-11-01        2341     69             41            41   \n", "86   4  202512 2025-12-01        1983     88             45            45   \n", "\n", "    Promo1  Promo2  Promo3  ...  T5  T6  T7  T8  T9  T10  T11  T12  T13  T14  \n", "2       60      60      60  ...   0   0   0   0   0    0    0    0    0    0  \n", "3       58      58      58  ...   0   0   0   0   0    0    0    0    0    0  \n", "4        0      43      43  ...   0   0   0   0   0    0    0    0    0    0  \n", "5        0      45      45  ...   0   0   0   0   0    0    0    0    0    0  \n", "6        0      76      76  ...   1   0   0   0   0    0    0    0    0    0  \n", "7        0      45      45  ...   0   1   0   0   0    0    0    0    0    0  \n", "8        0      60      60  ...   0   0   1   0   0    0    0    0    0    0  \n", "9        0      74      74  ...   0   0   0   1   0    0    0    0    0    0  \n", "10       0      50      50  ...   0   0   0   0   1    0    0    0    0    0  \n", "11       0      12      12  ...   0   0   0   0   0    1    0    0    0    0  \n", "12       0      70      70  ...   0   0   0   0   0    0    1    0    0    0  \n", "13       0      99      99  ...   0   0   0   0   0    0    0    1    0    0  \n", "14       0      91      91  ...   0   0   0   0   0    0    0    0    1    0  \n", "25      61      61      61  ...   0   0   0   0   0    0    0    0    0    0  \n", "26      56      56      56  ...   0   0   0   0   0    0    0    0    0    0  \n", "27      56      56      56  ...   0   0   0   0   0    0    0    0    0    0  \n", "28      53      53      53  ...   0   0   0   0   0    0    0    0    0    0  \n", "29      56      56      56  ...   1   0   0   0   0    0    0    0    0    0  \n", "30      47      47      47  ...   0   1   0   0   0    0    0    0    0    0  \n", "31      57      57      57  ...   0   0   1   0   0    0    0    0    0    0  \n", "32      34      34      34  ...   0   0   0   1   0    0    0    0    0    0  \n", "33      71      71      71  ...   0   0   0   0   1    0    0    0    0    0  \n", "34       0      56      56  ...   0   0   0   0   0    1    0    0    0    0  \n", "35       0      44      44  ...   0   0   0   0   0    0    1    0    0    0  \n", "36       0      59      59  ...   0   0   0   0   0    0    0    1    0    0  \n", "37       0      33      33  ...   0   0   0   0   0    0    0    0    1    0  \n", "38       0      82      82  ...   0   0   0   0   0    0    0    0    0    1  \n", "49      64      64      64  ...   0   0   0   0   0    0    0    0    0    0  \n", "50      80      80      80  ...   0   0   0   0   0    0    0    0    0    0  \n", "51      66      66      66  ...   0   0   0   0   0    0    0    0    0    0  \n", "52      78      78      78  ...   0   0   0   0   0    0    0    0    0    0  \n", "53      40      40      40  ...   1   0   0   0   0    0    0    0    0    0  \n", "54      78      78      78  ...   0   1   0   0   0    0    0    0    0    0  \n", "55      79      79      79  ...   0   0   1   0   0    0    0    0    0    0  \n", "56      38      38      38  ...   0   0   0   1   0    0    0    0    0    0  \n", "57      41      41      41  ...   0   0   0   0   1    0    0    0    0    0  \n", "58      76      76      76  ...   0   0   0   0   0    1    0    0    0    0  \n", "59      30      30      30  ...   0   0   0   0   0    0    1    0    0    0  \n", "60      36      36      36  ...   0   0   0   0   0    0    0    1    0    0  \n", "61      52      52      52  ...   0   0   0   0   0    0    0    0    1    0  \n", "62      56      56      56  ...   0   0   0   0   0    0    0    0    0    1  \n", "73      59      59      59  ...   0   0   0   0   0    0    0    0    0    0  \n", "74      68      68      68  ...   0   0   0   0   0    0    0    0    0    0  \n", "75      64      64      64  ...   0   0   0   0   0    0    0    0    0    0  \n", "76      55      55      55  ...   0   0   0   0   0    0    0    0    0    0  \n", "77       0      28      28  ...   1   0   0   0   0    0    0    0    0    0  \n", "78       0      42      42  ...   0   1   0   0   0    0    0    0    0    0  \n", "79       0      76      76  ...   0   0   1   0   0    0    0    0    0    0  \n", "80       0      79      79  ...   0   0   0   1   0    0    0    0    0    0  \n", "81       0      59      59  ...   0   0   0   0   1    0    0    0    0    0  \n", "82       0      76      76  ...   0   0   0   0   0    1    0    0    0    0  \n", "83       0      16      16  ...   0   0   0   0   0    0    1    0    0    0  \n", "84       0      69      69  ...   0   0   0   0   0    0    0    1    0    0  \n", "85       0      41      41  ...   0   0   0   0   0    0    0    0    1    0  \n", "86       0      45      45  ...   0   0   0   0   0    0    0    0    0    1  \n", "\n", "[55 rows x 29 columns]\n", "Gemini API initialized successfully.\n", "Channel impacts:{'NRx_lag_1': -12.7914947529, 'PDE_Adstock_10_Log': 78.0555672527, 'Copay_Adstock_30_Root8': 4.0433209335, 'Intercept': 30.6926065667}\n", "\n", "Analyzing your question...\n", "\n", "Generating analysis...\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df', 'spend_column', 'spend_channel_column']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {', '.join(missing_vars)}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{var_name} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{var_name} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{var_name} = []\")\n", "        else:\n", "            exec(f\"{var_name} = None\")\n", "\n", "try:\n", "    # Calculate the budget share for each channel\n", "\n", "    if spends_df is not None and not spends_df.empty:\n", "        total_budget = spends_df[spend_column].sum()\n", "        spends_df['Budget Share'] = spends_df[spend_column] / total_budget * 100\n", "\n", "        # Find the channel with the largest budget share\n", "        largest_share_channel = spends_df.loc[spends_df['Budget Share'].idxmax()]\n", "\n", "        # Find the channel with the smallest budget share\n", "        smallest_share_channel = spends_df.loc[spends_df['Budget Share'].idxmin()]\n", "\n", "        # Print the results\n", "        print(\"Channel with the largest budget share:\")\n", "        print(largest_share_channel)\n", "        print(\"\\nChannel with the smallest budget share:\")\n", "        print(smallest_share_channel)\n", "\n", "        # Create a bar plot of budget shares\n", "        plt.figure(figsize=(10, 6))\n", "        sns.barplot(x=spend_channel_column, y='Budget Share', data=spends_df)\n", "        plt.title('Budget Share by Channel')\n", "        plt.xlabel('Channel')\n", "        plt.ylabel('Budget Share (%)')\n", "        plt.xticks(rotation=45, ha='right')\n", "\n", "        # Annotate the bars with the budget share values\n", "        for index, value in enumerate(spends_df['Budget Share']):\n", "            plt.text(index, value + 0.5, f'{value:.1f}%', ha='center')\n", "\n", "        plt.tight_layout()\n", "        plt.show()\n", "        print('The Barplot showing the channel spend percentage')\n", "    else:\n", "        print(\"spends_df is either None or empty. Please check the data.\")\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")\n", "    traceback.print_exc()\n", "\n", "Attempt 1/6...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Analysis completed successfully!\n", "\n", "Output:\n", "Channel with the largest budget share:\n", "Channel         Copay\n", "Spends          30000\n", "Budget Share     50.0\n", "Name: 1, dtype: object\n", "\n", "Channel with the smallest budget share:\n", "Channel               PDE\n", "Spends              10000\n", "Budget Share    16.666667\n", "Name: 0, dtype: object\n", "The Barplot showing the channel spend percentage\n", "\n", "\n", "Analyzing your question...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Generating analysis...\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {', '.join(missing_vars)}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{var_name} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{var_name} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{var_name} = []\")\n", "        else:\n", "            exec(f\"{var_name} = None\")\n", "\n", "# Initialize channel_name\n", "channel_name = None\n", "\n", "# Initialize variables to store marketing and non-marketing contributions\n", "marketing_contribution = 0\n", "non_marketing_contribution = 0\n", "\n", "try:\n", "    # Implement the get_marketing_variables_only function (as it's not available in the environment)\n", "    def get_marketing_variables_only(best_result_table, promo_channels):\n", "        marketing_rows = []\n", "    \n", "        for _, row in best_result_table.iterrows():\n", "            var_name = row.get('Channel', '')\n", "            # Check if this is a legitimate marketing variable\n", "            is_marketing_var = False\n", "        \n", "            # Check if the variable is derived from any promo channel\n", "            for channel in promo_channels:\n", "                if var_name.startswith(channel + '_') or var_name == channel:\n", "                    is_marketing_var = True\n", "                    break\n", "        \n", "            # Explicitly exclude non-marketing variables\n", "            if (var_name == 'Intercept' or \n", "                var_name.startswith('T') or \n", "                'time' in var_name.lower() or \n", "                'trend' in var_name.lower() or \n", "                'Rtime' in var_name or\n", "                'season' in var_name.lower() or\n", "                'month' in var_name.lower() or\n", "                'week' in var_name.lower() or\n", "                'quarter' in var_name.lower() or\n", "                '_lag_' in var_name.lower() or  # Exclude variables with '_lag_' pattern\n", "                (var_name.lower().endswith('lag') or  # Ends with 'lag'\n", "                 any(var_name.lower().endswith(f'lag_{i}') for i in range(10)))):\n", "                is_marketing_var = False\n", "            \n", "            if is_marketing_var:\n", "                marketing_rows.append(row)\n", "    \n", "        return pd.DataFrame(marketing_rows)\n", "\n", "    # Separate marketing and non-marketing variables\n", "    marketing_only_table = get_marketing_variables_only(best_result_table, promo_channels)\n", "    \n", "    # Calculate the total impact of marketing variables\n", "    marketing_contribution = marketing_only_table['Impact Percentage'].sum()\n", "    \n", "    # Calculate the total impact of non-marketing variables\n", "    non_marketing_contribution = 100 - marketing_contribution  # Assuming total contribution is 100%\n", "\n", "    # Print the results\n", "    print(f\"Contribution from Marketing Efforts: {marketing_contribution:.2f}%\")\n", "    print(f\"Contribution from Non-Marketing Factors: {non_marketing_contribution:.2f}%\")\n", "\n", "    # Create a pie chart to visualize the contributions\n", "    labels = ['Marketing Efforts', 'Non-Marketing Factors']\n", "    sizes = [marketing_contribution, non_marketing_contribution]\n", "    colors = ['skyblue', 'lightcoral']\n", "    explode = (0.1, 0)  # explode 1st slice\n", "\n", "    plt.figure(figsize=(8, 6))\n", "    plt.pie(sizes, explode=explode, labels=labels, colors=colors, autopct='%1.1f%%', shadow=True, startangle=140)\n", "    plt.title('Contribution of Marketing vs. Non-Marketing Factors')\n", "    plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle.\n", "    plt.show()\n", "    print(\"Pie chart showing contribution of marketing and non-marketing factors is displayed.\")\n", "    print(\"It displays the percentage of contribution of marketing efforts vs non-marketing factors.\")\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")\n", "    traceback.print_exc()\n", "\n", "Attempt 1/6...\n", "\n", "Analysis completed successfully!\n", "\n", "Output:\n", "Contribution from Marketing Efforts: 82.10%\n", "Contribution from Non-Marketing Factors: 17.90%\n", "Pie chart showing contribution of marketing and non-marketing factors is displayed.\n", "It displays the percentage of contribution of marketing efforts vs non-marketing factors.\n", "\n", "\n", "Analyzing your question...\n", "\n", "Generating analysis...\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df', 'spend_column', 'spend_channel_column',\n", "                 'historical_df', 'historical_impact', 'historical_channel_column']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {', '.join(missing_vars)}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{var_name} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{var_name} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{var_name} = []\")\n", "        else:\n", "            exec(f\"{var_name} = None\")\n", "\n", "def ROI(spends_df, df):\n", "    \"\"\"\n", "    Calculates the Return on Investment (ROI) for each channel based on the marketing mix model results.\n", "\n", "    ROI = (unit_price * estimate * modeled_activity) / spends\n", "    \"\"\"\n", "    unit_price = float(input(\"Enter the price of each unit: \"))\n", "    roi_dict = {}\n", "\n", "    for i, row in spends_df.iterrows():\n", "        base_ch_name = row['Channel']\n", "        spends = row['Spends']\n", "\n", "        # Initialize variables\n", "        estimate = 0\n", "        modeled_activity = 0\n", "        matched_transformed_channel = None\n", "\n", "        # Find matching transformed channel\n", "        for transformed_ch_name in df['Channel'].unique():\n", "            if base_ch_name in transformed_ch_name:\n", "                matched_transformed_channel = transformed_ch_name\n", "                break\n", "\n", "        if matched_transformed_channel:\n", "            matched_row = df[df['Channel'] == matched_transformed_channel]\n", "            if not matched_row.empty:\n", "                try:\n", "                    estimate = matched_row['Estimate'].values[0]\n", "                    modeled_activity = matched_row['Total Modeled Activity'].values[0]\n", "\n", "                    # ROI = (unit_price * estimate * modeled_activity) / spends\n", "                    returns = unit_price * estimate * modeled_activity\n", "                    roi = returns / spends if spends != 0 else 0\n", "                    roi_dict[base_ch_name] = roi\n", "\n", "                except Exception as e:\n", "                    print(f\"Error calculating ROI for {base_ch_name}: {e}\")\n", "                    roi_dict[base_ch_name] = None\n", "\n", "            else:\n", "                roi_dict[base_ch_name] = None\n", "        else:\n", "            roi_dict[base_ch_name] = None\n", "\n", "    return roi_dict\n", "\n", "# Example usage:\n", "try:\n", "    # Check if spends_df and best_result_table are available\n", "    if spends_df is not None and best_result_table is not None:\n", "        # Calculate ROI\n", "        roi_results = ROI(spends_df, best_result_table)\n", "\n", "        # Print ROI results\n", "        if roi_results:\n", "            print(\"\\nROI Results:\")\n", "            for channel, roi in roi_results.items():\n", "                if roi is not None:\n", "                    print(f\"{channel}: {roi:.2f}\")\n", "                else:\n", "                    print(f\"{channel}: No ROI Data\")\n", "        else:\n", "            print(\"No ROI results to display.\")\n", "\n", "    else:\n", "        print(\"spends_df or best_result_table not available.\")\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")\n", "    traceback.print_exc()\n", "\n", "Attempt 1/6...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"<string>\", line 89, in <module>\n", "  File \"<string>\", line 43, in ROI\n", "ValueError: could not convert string to float: 'exit'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Attempt 1 failed: Error: Execution produced an error: An error occurred: could not convert string to float: 'exit'\n", "\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23220\\905685341.py\", line 369, in execute_code_with_retries\n", "    raise Exception(f\"Execution produced an error: {output_text}\")\n", "Exception: Execution produced an error: An error occurred: could not convert string to float: 'exit'\n", "\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "New code generated for next attempt.\n", "\n", "Attempt 2/6...\n", "Attempt 2 failed: Error: name 'traceback' is not defined\n", "Traceback (most recent call last):\n", "  File \"<string>\", line 63, in calculate_roi\n", "NameError: name 'spend_channel_column' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23220\\905685341.py\", line 363, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 100, in <module>\n", "  File \"<string>\", line 94, in calculate_roi\n", "NameError: name 'traceback' is not defined\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "New code generated for next attempt.\n", "\n", "Attempt 3/6...\n", "\n", "Analysis completed successfully!\n", "\n", "Output:\n", "Invalid input for unit price. Please enter a numerical value.\n", "ROI calculation failed.\n", "\n", "\n", "Analyzing your question...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Generating analysis...\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {', '.join(missing_vars)}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{var_name} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{var_name} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{var_name} = []\")\n", "        else:\n", "            exec(f\"{var_name} = None\")\n", "\n", "# Initialize variables\n", "channel_name = None\n", "top_n = 3\n", "top_drivers = pd.DataFrame()\n", "marketing_only_table = pd.DataFrame()  # Initialize here\n", "result_df = pd.DataFrame()\n", "estimate = None\n", "modeled_activity = None\n", "spends = None\n", "unit_price = None\n", "roi = None\n", "roi_dict = {}\n", "base_channel = None\n", "\n", "# Example code pattern for filtering out non-marketing variables\n", "def get_marketing_variables_only(best_result_table, promo_channels):\n", "    marketing_rows = []\n", "    \n", "    if best_result_table is None or best_result_table.empty:\n", "        print(\"Error: best_result_table is None or empty\")\n", "        return pd.DataFrame()\n", "\n", "    if promo_channels is None:\n", "        print(\"Error: promo_channels is None\")\n", "        return pd.DataFrame()\n", "    \n", "    for _, row in best_result_table.iterrows():\n", "        var_name = row.get('Channel', '')\n", "        # Check if this is a legitimate marketing variable\n", "        is_marketing_var = False\n", "        \n", "        # Check if the variable is derived from any promo channel\n", "        if promo_channels is not None:  # Check for None\n", "            for channel in promo_channels:\n", "                if var_name.startswith(channel + '_') or var_name == channel:\n", "                    is_marketing_var = True\n", "                    break\n", "        \n", "        # Explicitly exclude non-marketing variables\n", "        if (var_name == 'Intercept' or \n", "            var_name.startswith('T') or \n", "            'time' in var_name.lower() or \n", "            'trend' in var_name.lower() or \n", "            'Rtime' in var_name or\n", "            'season' in var_name.lower() or\n", "            'month' in var_name.lower() or\n", "            'week' in var_name.lower() or\n", "            'quarter' in var_name.lower() or\n", "            '_lag_' in var_name.lower() or  # Exclude variables with '_lag_' pattern\n", "            (var_name.lower().endswith('lag') or  # Ends with 'lag'\n", "             any(var_name.lower().endswith(f'lag_{i}') for i in range(10)))):\n", "            is_marketing_var = False\n", "            \n", "        if is_marketing_var:\n", "            marketing_rows.append(row)\n", "    \n", "    return pd.DataFrame(marketing_rows)\n", "\n", "try:\n", "    # Filter for marketing variables only\n", "    marketing_only_table = get_marketing_variables_only(best_result_table, promo_channels)\n", "\n", "    # Check if marketing_only_table is empty\n", "    if marketing_only_table.empty:\n", "        print(\"No marketing variables found in best_result_table\")\n", "    else:\n", "        # Sort by impact percentage and get the top N drivers\n", "        top_drivers = marketing_only_table.sort_values('Impact Percentage', ascending=False).head(top_n)\n", "        print(\"Top 3 Biggest Drivers Overall:\")\n", "        print(top_drivers)\n", "        \n", "        # Visualization\n", "        plt.figure(figsize=(10, 6))\n", "        plt.bar(top_drivers['Channel'], top_drivers['Impact Percentage'])\n", "        plt.xlabel('Channel')\n", "        plt.ylabel('Impact Percentage')\n", "        plt.title('Top 3 Drivers by Impact Percentage')\n", "        plt.xticks(rotation=45, ha='right')\n", "        plt.tight_layout()  # Adjust layout to prevent labels from overlapping\n", "        plt.show()\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")\n", "    traceback.print_exc()\n", "\n", "Attempt 1/6...\n", "\n", "Analysis completed successfully!\n", "\n", "Output:\n", "Top 3 Biggest Drivers Overall:\n", "         R²                 Channel   P-Value   Estimate  Actual Sales  \\\n", "1  0.043712      PDE_Adstock_10_Log  0.247213  20.188197          5954   \n", "2  0.043712  <PERSON><PERSON>_Adstock_30_Root8  0.616006   0.171073          5954   \n", "\n", "   Adjusted R²  Effectiveness  Modeled Sales  Linear Activity  \\\n", "1     -0.01254   68437.986180         5954.0           3390.0   \n", "2     -0.01254     424.259966         5954.0           2480.0   \n", "\n", "   Impact Percentage  Total Modeled Activity  \n", "1          78.055567              230.205233  \n", "2           4.043321             1407.235143  \n", "\n", "\n", "Analyzing your question...\n", "\n", "Generating analysis...\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import matplotlib.dates as mdates\n", "import io\n", "import sys\n", "import traceback\n", "from contextlib import redirect_stdout\n", "\n", "# First verify all required variables exist\n", "required_vars = ['df', 'target_column', 'date_column', 'promo_channels', \n", "                 'channel_impacts', 'model_coefficients', 'best_result_table',\n", "                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',\n", "                 'spends_df', 'spend_column', 'spend_channel_column']\n", "\n", "missing_vars = []\n", "for var_name in required_vars:\n", "    if var_name not in locals() and var_name not in globals():\n", "        missing_vars.append(var_name)\n", "        \n", "if missing_vars:\n", "    print(f\"Error: Missing required variables: {', '.join(missing_vars)}\")\n", "    # Create empty placeholders for missing variables to prevent further errors\n", "    for var_name in missing_vars:\n", "        if var_name == 'df' or var_name.endswith('_df'):\n", "            exec(f\"{var_name} = pd.DataFrame()\")\n", "        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:\n", "            exec(f\"{var_name} = dict()\")\n", "        elif var_name.endswith('s'):\n", "            exec(f\"{var_name} = []\")\n", "        else:\n", "            exec(f\"{var_name} = None\")\n", "\n", "def calculate_roi(spends_df, best_result_table):\n", "    \"\"\"\n", "    Calculates the Return on Investment (ROI) for each channel based on the provided formula.\n", "    ROI = (unit_price * estimate * Total Modeled Activity) / spends\n", "    \"\"\"\n", "    channel_name = None  # Initialize channel_name\n", "    unit_price = 0  # Initialize unit_price\n", "    roi_dict = {}  # Initialize roi_dict\n", "    matched_row = None  # Initialize matched_row\n", "    estimate = 0  # Initialize estimate\n", "    modeled_activity = 0  # Initialize modeled_activity\n", "    returns = 0  # Initialize returns\n", "    roi = 0  # Initialize roi\n", "\n", "    try:\n", "        unit_price = float(input(\"Enter the price of each unit: \"))\n", "        \n", "        for i, row in spends_df.iterrows():\n", "            base_ch_name = row[spend_channel_column]\n", "            spend = row[spend_column]\n", "            \n", "            # Find matching variable in best_result_table\n", "            matched_row = best_result_table[best_result_table['Channel'].str.startswith(base_ch_name + '_Adstock', na=False)]\n", "            \n", "            if not matched_row.empty:\n", "                estimate = matched_row['Estimate'].values[0]\n", "                modeled_activity = matched_row['Total Modeled Activity'].values[0]\n", "                \n", "                # ROI = (unit_price * estimate * Total Modeled Activity) / spends\n", "                returns = unit_price * estimate * modeled_activity\n", "                \n", "                if spend != 0:\n", "                    roi = returns / spend\n", "                else:\n", "                    roi = 0  # Avoid division by zero\n", "                \n", "                roi_dict[base_ch_name] = roi\n", "            else:\n", "                roi_dict[base_ch_name] = None  # No matching channel\n", "        \n", "        return roi_dict\n", "    \n", "    except ValueError:\n", "        print(\"Error: Invalid input for unit price. Please enter a numeric value.\")\n", "        return {}\n", "    except Exception as e:\n", "        print(f\"An error occurred during ROI calculation: {e}\")\n", "        traceback.print_exc()\n", "        return {}\n", "\n", "# Call the ROI calculation function\n", "roi_results = calculate_roi(spends_df, best_result_table)\n", "\n", "# Find the channel with the highest ROI\n", "highest_roi_channel = None\n", "highest_roi = float('-inf')\n", "\n", "for base_ch_name, roi in roi_results.items():\n", "    if roi is not None and roi > highest_roi:\n", "        highest_roi = roi\n", "        highest_roi_channel = base_ch_name\n", "\n", "# Print the results\n", "if highest_roi_channel:\n", "    print(f\"The channel with the highest ROI is {highest_roi_channel} with an ROI of {highest_roi:.2f}\")\n", "else:\n", "    print(\"Could not determine the channel with the highest ROI.\")\n", "\n", "Attempt 1/6...\n", "Attempt 1 failed: Error: name 'traceback' is not defined\n", "Traceback (most recent call last):\n", "  File \"<string>\", line 54, in calculate_roi\n", "NameError: name 'spend_channel_column' is not defined\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23220\\905685341.py\", line 363, in execute_code_with_retries\n", "    exec(code_to_execute, globals(), local_vars_copy)\n", "  File \"<string>\", line 87, in <module>\n", "  File \"<string>\", line 83, in calculate_roi\n", "NameError: name 'traceback' is not defined\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "New code generated for next attempt.\n", "\n", "Attempt 2/6...\n", "Attempt 2 failed: Error: Execution produced an error: An unexpected error occurred: name 'spend_column' is not defined\n", "NameError: name 'traceback' is not defined. Make sure all required variables are defined.\n", "\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23220\\905685341.py\", line 369, in execute_code_with_retries\n", "    raise Exception(f\"Execution produced an error: {output_text}\")\n", "Exception: Execution produced an error: An unexpected error occurred: name 'spend_column' is not defined\n", "NameError: name 'traceback' is not defined. Make sure all required variables are defined.\n", "\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "New code generated for next attempt.\n", "\n", "Attempt 3/6...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"<string>\", line 99, in <module>\n", "  File \"<string>\", line 45, in calculate_roi\n", "NameError: name 'spend_channel_column' is not defined\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Attempt 3 failed: Error: Execution produced an error: An error occurred: name 'spend_channel_column' is not defined\n", "\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23220\\905685341.py\", line 369, in execute_code_with_retries\n", "    raise Exception(f\"Execution produced an error: {output_text}\")\n", "Exception: Execution produced an error: An error occurred: name 'spend_channel_column' is not defined\n", "\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "New code generated for next attempt.\n", "\n", "Attempt 4/6...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"<string>\", line 119, in <module>\n", "  File \"<string>\", line 119, in <lambda>\n", "NameError: name 'roi_dict' is not defined\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Attempt 4 failed: Error: Execution produced an error: Warning: No matching transformed channel found in best_result_table for base channel: SpeakerEvent\n", "An error occurred: name 'roi_dict' is not defined\n", "\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23220\\905685341.py\", line 369, in execute_code_with_retries\n", "    raise Exception(f\"Execution produced an error: {output_text}\")\n", "Exception: Execution produced an error: Warning: No matching transformed channel found in best_result_table for base channel: SpeakerEvent\n", "An error occurred: name 'roi_dict' is not defined\n", "\n", "\n", "Trying again with a slightly modified approach...\n", "\n", "New code generated for next attempt.\n", "\n", "Attempt 5/6...\n", "\n", "Analysis completed successfully!\n", "\n", "Output:\n", "Warning: No matching transformed channel found in best_result_table for base channel: SpeakerEvent\n", "The channel with the highest ROI is: PDE with ROI = 4.65\n", "\n", "Exiting chatbot. Thank you for using the Marketing Mix Modeling Pipeline!\n"]}], "source": ["chatbot()"]}, {"cell_type": "code", "execution_count": null, "id": "f06243d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "28062f78", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 181, "id": "0cdab4e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting google-cloud-storageNote: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 23.2.1 -> 25.1.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "  Obtaining dependency information for google-cloud-storage from https://files.pythonhosted.org/packages/13/b8/c99c965659f45efa73080477c49ffddf7b9aecb00806be8422560bb5b824/google_cloud_storage-3.1.0-py2.py3-none-any.whl.metadata\n", "  Using cached google_cloud_storage-3.1.0-py2.py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: pandas in c:\\python\\lib\\site-packages (2.2.3)\n", "Requirement already satisfied: google-auth<3.0dev,>=2.26.1 in c:\\python\\lib\\site-packages (from google-cloud-storage) (2.39.0)\n", "Requirement already satisfied: google-api-core<3.0.0dev,>=2.15.0 in c:\\python\\lib\\site-packages (from google-cloud-storage) (2.24.2)\n", "Collecting google-cloud-core<3.0dev,>=2.4.2 (from google-cloud-storage)\n", "  Obtaining dependency information for google-cloud-core<3.0dev,>=2.4.2 from https://files.pythonhosted.org/packages/40/86/bda7241a8da2d28a754aad2ba0f6776e35b67e37c36ae0c45d49370f1014/google_cloud_core-2.4.3-py2.py3-none-any.whl.metadata\n", "  Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl.metadata (2.7 kB)\n", "Collecting google-resumable-media>=2.7.2 (from google-cloud-storage)\n", "  Obtaining dependency information for google-resumable-media>=2.7.2 from https://files.pythonhosted.org/packages/82/35/b8d3baf8c46695858cb9d8835a53baa1eeb9906ddaf2f728a5f5b640fd1e/google_resumable_media-2.7.2-py2.py3-none-any.whl.metadata\n", "  Using cached google_resumable_media-2.7.2-py2.py3-none-any.whl.metadata (2.2 kB)\n", "Requirement already satisfied: requests<3.0.0dev,>=2.18.0 in c:\\python\\lib\\site-packages (from google-cloud-storage) (2.32.3)\n", "Collecting google-crc32c<2.0dev,>=1.0 (from google-cloud-storage)\n", "  Obtaining dependency information for google-crc32c<2.0dev,>=1.0 from https://files.pythonhosted.org/packages/04/17/ed9aba495916fcf5fe4ecb2267ceb851fc5f273c4e4625ae453350cfd564/google_crc32c-1.7.1-cp311-cp311-win_amd64.whl.metadata\n", "  Downloading google_crc32c-1.7.1-cp311-cp311-win_amd64.whl.metadata (2.4 kB)\n", "Requirement already satisfied: numpy>=1.23.2 in c:\\python\\lib\\site-packages (from pandas) (2.2.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\python\\lib\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\python\\lib\\site-packages (from pandas) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\python\\lib\\site-packages (from pandas) (2025.1)\n", "Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\\python\\lib\\site-packages (from google-api-core<3.0.0dev,>=2.15.0->google-cloud-storage) (1.69.1)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.19.5 in c:\\python\\lib\\site-packages (from google-api-core<3.0.0dev,>=2.15.0->google-cloud-storage) (5.29.3)\n", "Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in c:\\python\\lib\\site-packages (from google-api-core<3.0.0dev,>=2.15.0->google-cloud-storage) (1.26.1)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\\python\\lib\\site-packages (from google-auth<3.0dev,>=2.26.1->google-cloud-storage) (5.5.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\\python\\lib\\site-packages (from google-auth<3.0dev,>=2.26.1->google-cloud-storage) (0.4.1)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in c:\\python\\lib\\site-packages (from google-auth<3.0dev,>=2.26.1->google-cloud-storage) (4.9)\n", "Requirement already satisfied: six>=1.5 in c:\\python\\lib\\site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\python\\lib\\site-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\python\\lib\\site-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\python\\lib\\site-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (1.26.20)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\python\\lib\\site-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (2024.12.14)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in c:\\python\\lib\\site-packages (from pyasn1-modules>=0.2.1->google-auth<3.0dev,>=2.26.1->google-cloud-storage) (0.6.1)\n", "Downloading google_cloud_storage-3.1.0-py2.py3-none-any.whl (174 kB)\n", "   ---------------------------------------- 0.0/174.9 kB ? eta -:--:--\n", "   ---------------------------------------- 0.0/174.9 kB ? eta -:--:--\n", "   -- ------------------------------------- 10.2/174.9 kB ? eta -:--:--\n", "   ------------- ------------------------- 61.4/174.9 kB 409.6 kB/s eta 0:00:01\n", "   -------------------------------------  174.1/174.9 kB 952.6 kB/s eta 0:00:01\n", "   -------------------------------------  174.1/174.9 kB 952.6 kB/s eta 0:00:01\n", "   -------------------------------------  174.1/174.9 kB 952.6 kB/s eta 0:00:01\n", "   -------------------------------------  174.1/174.9 kB 952.6 kB/s eta 0:00:01\n", "   -------------------------------------  174.1/174.9 kB 952.6 kB/s eta 0:00:01\n", "   -------------------------------------  174.1/174.9 kB 952.6 kB/s eta 0:00:01\n", "   -------------------------------------  174.1/174.9 kB 952.6 kB/s eta 0:00:01\n", "   -------------------------------------- 174.9/174.9 kB 269.9 kB/s eta 0:00:00\n", "Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl (29 kB)\n", "Downloading google_crc32c-1.7.1-cp311-cp311-win_amd64.whl (33 kB)\n", "Downloading google_resumable_media-2.7.2-py2.py3-none-any.whl (81 kB)\n", "   ---------------------------------------- 0.0/81.3 kB ? eta -:--:--\n", "   ---------------------------------------- 81.3/81.3 kB 4.4 MB/s eta 0:00:00\n", "Installing collected packages: google-crc32c, google-resumable-media, google-cloud-core, google-cloud-storage\n", "Successfully installed google-cloud-core-2.4.3 google-cloud-storage-3.1.0 google-crc32c-1.7.1 google-resumable-media-2.7.2\n"]}], "source": ["pip install google-cloud-storage pandas\n"]}, {"cell_type": "code", "execution_count": 182, "id": "358f3696", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: openpyxl in c:\\python\\lib\\site-packages (3.1.5)\n", "Requirement already satisfied: et-xmlfile in c:\\python\\lib\\site-packages (from openpyxl) (2.0.0)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 23.2.1 -> 25.1.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["\n", "pip install openpyxl\n"]}, {"cell_type": "code", "execution_count": 183, "id": "b54f1478", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pyarrow in c:\\python\\lib\\site-packages (19.0.1)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 23.2.1 -> 25.1.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["\n", "pip <PERSON> pyarrow"]}, {"cell_type": "code", "execution_count": null, "id": "a7b63166", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}