



import sys

import pandas as pd
import xlsxwriter
import numpy as np
import google.generativeai as genai
import json
import logging
import statsmodels.api as sm
import os

import pandas as pd
import numpy as np
import google.generativeai as genai
import json
import logging
import statsmodels.api as sm
import os
import sys
import matplotlib.pyplot as plt
import matplotlib_venn
import seaborn as sns
import datetime
final_df = pd.DataFrame()
# Configure logging
logging.basicConfig(level=logging.ERROR)

# Initialize Gemini API
genai.configure(api_key="AIzaSyA3cXv0-vsm31kXkJtLUT3W_bHooZm3hN8")
model = genai.GenerativeModel("gemini-2.0-flash")



def get_unit_price():
    """Helper function to get user input for unit price"""
    return float(input("Enter the price of each unit: ").strip())


def load_file(file_path):
    """
    Load data from a file based on its extension.

    Args:
        file_path (str): Path to the file to be loaded

    Returns:
        pandas.DataFrame: Loaded dataframe
    """
    try:
        logging.info("Loading data from file.")

        # Load data based on file extension
        if file_path.endswith(".csv"):
            df = pd.read_csv(file_path)
        elif file_path.endswith(".xlsx"):
            df = pd.read_excel(file_path)
        else:
            error_msg = f"ERROR: Unsupported file format for '{file_path}'. Only CSV and Excel files are supported."
            logging.error(error_msg)
            print(error_msg)
            sys.exit(1)  # Exit immediately

        return df

    except FileNotFoundError:
        error_msg = f"ERROR: File '{file_path}' not found. Please check the file path and try again."
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately for file not found
    except Exception as e:
        error_msg = f"ERROR: Failed to load file '{file_path}': {str(e)}"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately for other loading errors
import os
import logging
import psycopg2

def get_db_connection():
    """
    Create and return a connection to the PostgreSQL database
    
    Returns:
        connection: PostgreSQL database connection object
    """
    try:
        # Connect to the PostgreSQL database
        conn = psycopg2.connect(
            host=os.environ.get("DB_HOST", "localhost"),
            database=os.environ.get("DB_NAME", "MMX"),
            user=os.environ.get("DB_USER", "postgres"),
            password=os.environ.get("DB_PASSWORD", "something")
        )
        return conn
    except Exception as e:
        logging.error(f"Error connecting to database: {str(e)}")
        return None



def initialize_db_connection():
    """Initialize database connection"""
    return get_db_connection()

def close_db_connection(conn):
    """Close database connection"""
    if conn:
        try:
            conn.close()
            return True
        except Exception as e:
            logging.error(f"Error closing database connection: {str(e)}")
            return False
    return True
def process_dates(df,date_column,date_format):
    """Process and clean date column in dataframe"""
    # Make a copy of the original date column for debugging
    df['original_date'] = df[date_column].copy()
     # Your custom function to select format
    df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))


    # Print debugging info about the conversion success
    na_count = df[date_column].isna().sum()
    total_rows = len(df)
    print(f"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted")

    if na_count > 0:
        # Show examples of problematic values
        problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]
        print(f"Examples of problematic date values: {problem_examples}")

    # Drop the empty dates
    orig_len = len(df)
    df = df.dropna(subset=[date_column])
    print(f"Dropped {orig_len - len(df)} rows with invalid dates")

    # Drop the debug column
    df = df.drop(columns=['original_date'])
    
    return df



def get_main_data_columns(file_path):
    
    df = load_file(file_path)
    """Get and validate column names for main data from user input"""

    
    # For testing, using hardcoded values
    promo_channels = ['PDE','Copay']
    promo_channels = [col.strip() for col in promo_channels]
    date_column = 'Date'
    id_column = 'ID'
    target_column = 'NRx'
    start_date = '202411'
    end_date = '202512'
    start_date = datetime.datetime.strptime(start_date, '%Y%m')
    end_date = datetime.datetime.strptime(end_date, '%Y%m')

    
    # Check if required columns exist
    missing_cols = [col for col in [date_column, target_column, id_column] if col not in df.columns]
    if missing_cols:
        error_msg = f"ERROR: The following required columns are missing from your main data: {', '.join(missing_cols)}"
        error_msg += f"\nAvailable columns: {', '.join(df.columns)}"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately

    # Check if promotional channels exist
    missing_promo_cols = [col for col in promo_channels if col not in df.columns]
    if missing_promo_cols:
        error_msg = f"ERROR: The following promotional channel columns are missing: {', '.join(missing_promo_cols)}"
        error_msg += f"\nAvailable columns: {', '.join(df.columns)}"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately
    
    print(f"Validated main data columns: {date_column}, {id_column}, {target_column}")
    print(f"Promotional channels: {', '.join(promo_channels)}")
    print(f"Date range: {start_date} to {end_date}")
    
    return df,promo_channels, date_column, id_column, target_column, start_date, end_date

def select_date_format():
    """Select and return the date format from user input"""
    global date_format
    
    print("\nSelect date format from the following options:")
    print("1. MM/DD/YYYY")
    print("2. YYYY-MM-DD")
    print("3. YYYY-MM")
    print("4. MM-YYYY")
    
    # For production, uncomment to get user input
    # format_choice = input("Enter your choice (1-4): ").strip()
    
    # For testing, using a default value
    format_choice = '4'
    
    if format_choice == '1':
        date_format = 'MM/DD/YYYY'
    elif format_choice == '2':
        date_format = 'YYYY-MM-DD'
    elif format_choice == '3':
        date_format = 'YYYY-MM'
    elif format_choice == '4':
        date_format = 'MM-YYYY'
    else:
        print("Invalid choice. Defaulting to YYYY-MM-DD.")
        date_format = 'YYYY-MM-DD'
    
    print(f"Selected date format: {date_format}")
    return date_format

def convert_date(date_val,date_format):
    """Convert various date formats to YYYYMM format"""
    if pd.isna(date_val):
        return np.nan

    # Convert to string if it's not already
    date_str = str(date_val).strip()

    try:
        # First, try to parse based on user-selected format
        try:
            if date_format == 'MM/DD/YYYY':
                date_obj = pd.to_datetime(date_str, format='%m/%d/%Y', errors='raise')
            elif date_format == 'YYYY-MM-DD':
                date_obj = pd.to_datetime(date_str, format='%Y-%m-%d', errors='raise')
            elif date_format == 'YYYY-MM':
                date_obj = pd.to_datetime(date_str, format='%Y-%m', errors='raise')
            elif date_format == 'MM-YYYY':
                date_obj = pd.to_datetime(date_str, format='%m-%Y', errors='raise')
            else:
                date_obj = None
            if date_obj is not None:
                return date_obj.replace(day=1)
        except Exception:
            # If specific format parsing fails, continue with fallback options
            pass
        
        # For formats like '20247' (YYYYM) - year 2024, month 7
        if date_str.isdigit() and len(date_str) == 5:
            year = date_str[:4]
            month = date_str[4:5]  # Just one digit
            return f"{year}{month.zfill(2)}"  # zfill adds leading zero if needed

        # For formats like '202407' (YYYYMM)
        elif date_str.isdigit() and len(date_str) == 6:
            year = date_str[:4]
            month = date_str[4:6]
            return f"{year}{month}"

        # For integer timestamps
        elif date_str.isdigit() and len(date_str) > 6:
            # Convert timestamp to datetime
            date_obj = pd.to_datetime(int(date_str), errors='coerce')
            if not pd.isna(date_obj):
                return date_obj.strftime('%Y%m')

        # Try standard datetime parsing
        date_obj = pd.to_datetime(date_str, errors='coerce')
        if not pd.isna(date_obj):
            return date_obj.strftime('%Y%m')

        return np.nan
    except:
        return np.nan



def handle_outliers(df, promo_channels, target_column, num_sigmas=3):
    """Handle outliers in promotional channels"""
    all_columns = promo_channels + [target_column]

    # Store initial sums
    initial_sums = {col: df[col].sum() for col in all_columns if col in df.columns}
    initial_sums_df = pd.DataFrame(initial_sums, index=["before"])

    # Track indices of all outliers to remove
    outlier_indices = set()

    # Dictionary to store outlier indices by column
    column_outlier_indices = {}

    for col in promo_channels:
        if col in df.columns:
            data = df[col]
            mean = np.mean(data)
            std = np.std(data)

            # Identify outliers
            lower_bound = mean - num_sigmas * std
            upper_bound = mean + num_sigmas * std
            outliers = df[(data < lower_bound) | (data > upper_bound)]
            outlier_idx = outliers.index.tolist()

            # Store outlier indices for this column
            column_outlier_indices[col] = outlier_idx

            # Calculate the percentage drop if we remove these outliers
            if outlier_idx:
                col_sum_before = df[col].sum()
                col_sum_after = df.loc[~df.index.isin(outlier_idx), col].sum()
                percentage_drop = ((col_sum_before - col_sum_after) / col_sum_before * 100) if col_sum_before != 0 else 0

                # Only add to outlier indices if percentage drop is less than 10%
                if percentage_drop <= 10:
                    outlier_indices.update(outlier_idx)
                    print(f"[{col}] Removed {len(outlier_idx)} outliers (mu ± {num_sigmas}σ) - {percentage_drop:.2f}% drop")
                else:
                    print(f"[{col}] Skipping {len(outlier_idx)} outliers - would cause {percentage_drop:.2f}% drop (>10%)")

    # Drop all outliers and reset index
    df = df.drop(index=outlier_indices).reset_index(drop=True)
 
    # Store final sums
    final_sums = {col: df[col].sum() for col in all_columns if col in df.columns}
    final_sums_df = pd.DataFrame(final_sums, index=["after"])

    # Combine before/after
    summary_df = pd.concat([initial_sums_df, final_sums_df]).T.reset_index()
    summary_df.columns = ["channel", "before", "after"]
    summary_df["Percentage drop"] = summary_df.apply(
        lambda row: ((row["before"] - row["after"]) / row["before"] * 100) if row["before"] != 0 else 0,
        axis=1
    )

    print(f"Total outliers removed: {len(outlier_indices)}")
    print(f"Remaining rows: {len(df)}")

    print("Outlier summary:")
    print(summary_df)
    
    return df



def add_control_variables(df, id_col, date_col):
    """Add control variables to the dataframe"""
    df['Rtime'] = df.groupby(id_col).cumcount() + 1
    control_variables = []

    months = pd.to_datetime(df[date_col], errors='coerce').dt.month
    # Uncomment to create month dummies
    # for m in range(1, 13):
    #     df[f'm{m}'] = (months == m).astype(int)
    #     control_variables.append(f'm{m}')

    max_period = df.groupby(id_col).size().max()
    for t in range(1, max_period + 1):
        df[f'T{t}'] = (df['Rtime'] == t).astype(int)
        control_variables.append(f'T{t}')
    control_variables.append('Rtime')

    print(f"Added {len(control_variables)} control variables")
    return df, control_variables

#################################
# REGRESSION FUNCTIONS
#################################
# def perform_regression(df, promo_columns, target_column, date_column,start_date,end_date):

#     """
#     Perform regression analysis with the specified promotional columns.

#     Args:
#         df (pd.DataFrame): Input dataframe
#         promo_columns (list): List of promotional channel columns to use as predictors
#         target_column (str): Target variable name
#         date_column (str): Column containing date information

#     Returns:
#         pd.DataFrame: Regression results table
#     """

#     df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
#     try:
#         # Make a copy of the input DataFrame to avoid modifying the original
#         df = df.copy()
#         final_df = df.copy()

#         if not promo_columns:
#             logging.error("No valid promotional columns selected for regression.")
#             return pd.DataFrame([['Error', 'No valid promotional columns selected.']])

#         # Ensure all promo columns exist in the dataframe
#         missing_columns = [col for col in promo_columns if col not in df.columns]
#         if missing_columns:
#             error_msg = f"Missing columns in dataframe: {missing_columns}"
#             logging.error(error_msg)
#             return pd.DataFrame([['Error', error_msg]])

#         # Prepare data for regression
#         X = df[promo_columns]
#         y = df[target_column]
#         X = sm.add_constant(X)

#         # Fit OLS regression model
#         model = sm.OLS(y, X).fit()
#         predictions = model.predict(X)

#         # Metrics
#         mape = np.mean(np.abs((y - predictions) / y)) * 100
#         r_squared = model.rsquared
#         adj_r_squared = model.rsquared_adj
#         modeled_sales = predictions.sum()
#         actual_sales = y.sum()

#         # Initialize an empty list to store result rows instead of empty DataFrame
#         result_rows = []

#         # Total modeled activity for impact calculation
#         total_activity = sum(df[c].sum() * model.params[c] for c in promo_columns if c in model.params)
#         channel_impacts = {}
#         for col in promo_columns:
#             if col in model.params:
#                 estimate = model.params[col]
#                 activity = df[col].sum()
#                 channel_impacts[col] = estimate * activity

#         # Total impact is the sum of all individual channel impacts
#         total_impact = sum(channel_impacts.values())
#         for col in promo_columns:
#             if col not in model.params:
#                 continue

#             estimate = model.params[col]
#             p_value = model.pvalues[col]
#             activity = df[col].sum()

#             # Extract original variable name for linear activity
#             parts = col.split("_")

#             if "Tier" in parts:
#                 tier_index = parts.index("Tier")
#                 name = "_".join([parts[0], "Tier", parts[tier_index + 1]])
#             else:
#                 if "Adstock" in parts:
#                     adstock_index = parts.index("Adstock")
#                     name = "_".join(parts[:adstock_index])
#                 else:
#                     name = parts[0]

#             try:
#                 linear_activity = df[name].sum()
#             except KeyError:
#                 linear_activity = activity

#             target_sum = df[target_column].sum()
#             impact_percentage = ((estimate * activity) / modeled_sales)*100 if modeled_sales != 0 else 0
#             effectiveness = (estimate * linear_activity)

#             # Append row to list instead of concatenating DataFrames
#             result_rows.append({
#                 'Channel': col,
#                 'Estimate': estimate,
#                 'Impact Percentage': impact_percentage,
#                 'P-Value': p_value,
#                 'Effectiveness': effectiveness,
#                 'Linear Activity': linear_activity,
#                 'R²': r_squared,
#                 'Adjusted R²': adj_r_squared,
#                 'Total Modeled Activity': activity,
#                 'Modeled Sales': modeled_sales,
#                 'Actual Sales': actual_sales
#             })

#         # Include Intercept (const) in result rows
#         if 'const' in model.params:
#             estimate = model.params['const']
#             p_value = model.pvalues['const']
#             impact=estimate*len(df)
#             impact_percentage=(impact/modeled_sales)*100

#             result_rows.append({
#                 'Channel': 'Intercept',
#                 'Estimate': estimate,
#                 'Impact Percentage': impact_percentage,
#                 'P-Value': p_value,
#                 'Effectiveness': np.nan,
#                 'Linear Activity': np.nan,
#                 'R²': r_squared,
#                 'Adjusted R²': adj_r_squared,
#                 'Total Modeled Activity': np.nan,
#                 'Modeled Sales': modeled_sales,
#                 'Actual Sales': actual_sales
#             })

#         # Create result DataFrame from the list of rows after all rows are collected
#         result_table = pd.DataFrame(result_rows)

#         logging.info("OLS linear regression performed successfully.")
#         logging.info(f"Final Regression DataFrame head:\n{result_table.head()}")
#         return result_table

#     except Exception as e:
#         logging.error(f"Error performing OLS linear regression: {e}")
#         import traceback
#         logging.error(traceback.format_exc())
#         return pd.DataFrame([['Error', str(e)]])


# for this function one more column should be added in the result table one thta will calculate the no. of promo_columns and other column with list of significant columns
def perform_regression(df, promo_columns, target_column, date_column, start_date, end_date):
    """
    Perform regression analysis with the specified promotional columns.

    Args:
        df (pd.DataFrame): Input dataframe
        promo_columns (list): List of promotional channel columns to use as predictors
        target_column (str): Target variable name
        date_column (str): Column containing date information
        start_date: Start date for filtering data
        end_date: End date for filtering data

    Returns:
        pd.DataFrame: Regression results table
    """

    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
    try:
        # Make a copy of the input DataFrame to avoid modifying the original
        df = df.copy()
        final_df = df.copy()

        if not promo_columns:
            logging.error("No valid promotional columns selected for regression.")
            return pd.DataFrame([['Error', 'No valid promotional columns selected.']])

        # Ensure all promo columns exist in the dataframe
        missing_columns = [col for col in promo_columns if col not in df.columns]
        if missing_columns:
            error_msg = f"Missing columns in dataframe: {missing_columns}"
            logging.error(error_msg)
            return pd.DataFrame([['Error', error_msg]])

        # Prepare data for regression
        X = df[promo_columns]
        y = df[target_column]
        X = sm.add_constant(X)

        # Fit OLS regression model
        model = sm.OLS(y, X).fit()
        predictions = model.predict(X)

        # Metrics
        mape = np.mean(np.abs((y - predictions) / y)) * 100
        r_squared = model.rsquared
        adj_r_squared = model.rsquared_adj
        modeled_sales = predictions.sum()
        actual_sales = y.sum()
        
        # Calculate RMSE
        rmse = np.sqrt(np.mean((y - predictions) ** 2))
        
        # Get AIC
        aic = model.aic

        # Initialize an empty list to store result rows instead of empty DataFrame
        result_rows = []

        # Total modeled activity for impact calculation
        total_activity = sum(df[c].sum() * model.params[c] for c in promo_columns if c in model.params)
        channel_impacts = {}
        for col in promo_columns:
            if col in model.params:
                estimate = model.params[col]
                activity = df[col].sum()
                channel_impacts[col] = estimate * activity

        # Total impact is the sum of all individual channel impacts
        total_impact = sum(channel_impacts.values())
        for col in promo_columns:
            if col not in model.params:
                continue

            estimate = model.params[col]
            p_value = model.pvalues[col]
            activity = df[col].sum()

            # Extract original variable name for linear activity
            parts = col.split("_")

            if "Tier" in parts:
                tier_index = parts.index("Tier")
                name = "_".join([parts[0], "Tier", parts[tier_index + 1]])
            else:
                if "Adstock" in parts:
                    adstock_index = parts.index("Adstock")
                    name = "_".join(parts[:adstock_index])
                else:
                    name = parts[0]

            try:
                linear_activity = df[name].sum()
            except KeyError:
                linear_activity = activity

            target_sum = df[target_column].sum()
            impact_percentage = ((estimate * activity) / modeled_sales)*100 if modeled_sales != 0 else 0
            effectiveness = (estimate * linear_activity)
            significance= p_value<0.05
            channel_count=len(promo_columns)
            # Append row to list instead of concatenating DataFrames
            result_rows.append({
                'Channel': col,
                'Estimate': estimate,
                'Impact Percentage': impact_percentage,
                'P-Value': p_value,
                'Effectiveness': effectiveness,
                'Linear Activity': linear_activity,
                'R²': r_squared,
                'Adjusted R²': adj_r_squared,
                'AIC': aic,
                'RMSE': rmse,
                'Total Modeled Activity': activity,
                'Modeled Sales': modeled_sales,
                'Actual Sales': actual_sales,
                'Channel_count':channel_count,
                'significance':significance

                
            })

        # Include Intercept (const) in result rows
        if 'const' in model.params:
            estimate = model.params['const']
            p_value = model.pvalues['const']
            impact=estimate*len(df)
            impact_percentage=(impact/modeled_sales)*100

            result_rows.append({
                'Channel': 'Intercept',
                'Estimate': estimate,
                'Impact Percentage': impact_percentage,
                'P-Value': p_value,
                'Effectiveness': np.nan,
                'Linear Activity': np.nan,
                'R²': r_squared,
                'Adjusted R²': adj_r_squared,
                'AIC': aic,
                'RMSE': rmse,
                'Total Modeled Activity': np.nan,
                'Modeled Sales': modeled_sales,
                'Actual Sales': actual_sales,
                'Channel_count':channel_count,
                'significance':0
            })

        # Create result DataFrame from the list of rows after all rows are collected
        result_table = pd.DataFrame(result_rows)

        logging.info("OLS linear regression performed successfully.")
        logging.info(f"Final Regression DataFrame head:\n{result_table.head()}")
        return result_table

    except Exception as e:
        logging.error(f"Error performing OLS linear regression: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return pd.DataFrame([['Error', str(e)]])




def process_main_data(file_path,promo_channels,target_column,id_column,date_column,date_format,start_date,end_date, model_run_id=None,db_conn=None):
    """
    Clean and process the main data and update the PostgreSQL database directly.
    
    Args:
        file_path (str): Path to the input file
        model_run_id (UUID, optional): UUID of the model run to update in the database
        
    Returns:
        pandas.DataFrame: Processed main dataframe and related metadata
    """

    
    try:
        # Get column names and date format
        df = load_file(file_path)
        unit_price=get_unit_price()
        # Process dates
        df = process_dates(df, date_column,date_format)
        
        # Ensure numeric columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df = df.drop_duplicates()
        logging.info("Data successfully loaded and cleaned.")
        
        # Create lag variable
        df[f"{target_column}_lag_1"] = df.groupby(id_column)[target_column].shift(1).fillna(0)
        df = df.sort_values(by=[id_column, date_column])
        
        # Handle outliers
        df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)
        
        # If model_run_id is provided and we have a connection, update the database directly
        if model_run_id and db_conn:
            try:
                # Create a cursor object
                cursor = db_conn.cursor()
                
                # SQL to update model_data table
                sql = """
                    UPDATE model_data
                    SET 
                        date_column = %s,
                        id_column = %s,
                        target_column = %s,
                        promotional_columns = %s,
                        start_date = %s,
                        end_date = %s,
                        date_format = %s,
                        unit_price = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE model_run_id = %s
                """
                
                # Execute the update query with the values
                cursor.execute(sql, [
                    date_column,
                    id_column,
                    target_column,
                    promo_channels,  # This should be an array
                    start_date,
                    end_date,
                    date_format,
                    unit_price,
                    model_run_id
                ])
                
                # Commit the transaction
                db_conn.commit()
                
                # Close the cursor (but not the connection)
                cursor.close()
                
                logging.info(f"Successfully updated model_data for model_run_id: {model_run_id}")
                
            except Exception as e:
                logging.error(f"Error updating database in process_main_data: {str(e)}")
                # Try to rollback if possible
                try:
                    db_conn.rollback()
                except:
                    pass
        
        return df,unit_price
    
    except Exception as e:
        logging.error(f"Error processing main data: {str(e)}")
        raise

def process_spend_data(spends_file_path,spend_column,spend_channel_column, model_run_id=None,db_conn=None):
    """
    Process spend data and update the PostgreSQL database directly
    
    Args:
        spends_file_path (str): Path to the spend data file
        model_run_id (UUID, optional): UUID of the model run to update in the database
        
    Returns:
        pandas.DataFrame: Processed spend dataframe
    """

    
    df = load_file(spends_file_path)
    
    # Define spend column names (hardcoded for testing)
    
    
    # Check if spend columns exist in the dataframe
    if spend_column not in df.columns:
        error_msg = f"ERROR: Column '{spend_column}' not found in the spend dataframe. Available columns: {', '.join(df.columns)}"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately
        
    if spend_channel_column not in df.columns:
        error_msg = f"ERROR: Column '{spend_channel_column}' not found in the spend dataframe. Available columns: {', '.join(df.columns)}"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately
    
    # If model_run_id is provided and we have a database connection, update directly
    if model_run_id and db_conn:
        try:
            # Create a cursor object
            cursor = db_conn.cursor()
            
            # SQL to update model_data table
            sql = """
                UPDATE model_data
                SET 
                    spend_channel_column = %s,
                    spend_column = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE model_run_id = %s
            """
            
            # Execute the update query with the values
            cursor.execute(sql, [
                spend_channel_column,
                spend_column,
                model_run_id
            ])
            
            # Commit the transaction
            db_conn.commit()
            
            # Close the cursor (but not the connection)
            cursor.close()
            
            logging.info(f"Successfully updated spend data for model_run_id: {model_run_id}")
            
        except Exception as e:
            logging.error(f"Error updating database in process_spend_data: {str(e)}")
            # Try to rollback if possible
            try:
                db_conn.rollback()
            except:
                pass
    
    return df

def process_historical_data(historical_file_path,historical_impact,historical_channel_column, model_run_id=None,db_conn=None):
    """
    Process historical impact data and update the PostgreSQL database directly
    
    Args:
        historical_file_path (str): Path to the historical data file
        model_run_id (UUID, optional): UUID of the model run to update in the database
        
    Returns:
        pandas.DataFrame: Processed historical dataframe
    """

    df = load_file(historical_file_path)
    
    # Define historical column names (hardcoded for testing)
   
    
    # Check if historical columns exist in the dataframe
    if historical_impact not in df.columns:
        error_msg = f"ERROR: Column '{historical_impact}' not found in the historical dataframe. Available columns: {', '.join(df.columns)}"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately
        
    if historical_channel_column not in df.columns:
        error_msg = f"ERROR: Column '{historical_channel_column}' not found in the historical dataframe. Available columns: {', '.join(df.columns)}"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)  # Exit immediately
    
    # Additional processing specific to historical data can be added here
    print(f"Processed historical data with columns: {', '.join(df.columns)}")
    
    # If model_run_id is provided and we have a database connection, update directly
    if model_run_id and db_conn:
        try:
            # Create a cursor object
            cursor = db_conn.cursor()
            
            # SQL to update model_data table
            sql = """
                UPDATE model_data
                SET 
                    historical_channel_column = %s,
                    historical_impact = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE model_run_id = %s
            """
            
            # Execute the update query with the values
            cursor.execute(sql, [
                historical_channel_column,
                historical_impact,
                model_run_id
            ])
            
            # Commit the transaction
            db_conn.commit()
            
            # Close the cursor (but not the connection)
            cursor.close()
            
            logging.info(f"Successfully updated historical data for model_run_id: {model_run_id}")
            
        except Exception as e:
            logging.error(f"Error updating database in process_historical_data: {str(e)}")
            # Try to rollback if possible
            try:
                db_conn.rollback()
            except:
                pass
    
    return df

def get_adstock_ranges_from_gemini(promo_channels):
    """Use Gemini API to get recommended adstock ranges for different promotion types"""
    prompt = f"""
    For the following promotional channels in pharmaceutical marketing, provide the ideal adstock decay rate ranges.

    For adstock settings, categorize promotions into two groups: Personal Promotions and Non-Personal Promotions. Set the adstock range for Personal Promotions (including PDE, Call, Call Activity, and TV)
    between 70-80. For Non-Personal Promotions (such as Display and Banner), set the adstock range between 20-50.
    Only Speaker programs or conferences can have adstock range from 70-90
    Personal promotions include: Calls, PDE, TV etc
    Non-Personal include: Banners ,Headlines ,Ads etc
    Use the web and gather additional insights on these promotional activities, their impact on sales,
    and industry best practices. Incorporate any relevant findings to refine adstock selection and improve model accuracy.

    Categorize each channel as either "Personal Promotion", "Non-Personal Promotion", or "Other" and provide a
    recommended min and max adstock decay rate (as percentages between 10 and 90).

    Channels: {', '.join(promo_channels)}

    Return results in JSON format with this structure:
    {{
        "Channel_Name": {{
            "type": "Personal Promotion|Non-Personal Promotion|Other",
            "min_adstock": 10-90,
            "max_adstock": 10-90
        }},
        ...
    }}

    Ensure min_adstock is less than max_adstock. Base your ranges on typical pharmaceutical marketing benchmarks.
    """

    try:
        response = model.generate_content(prompt)
        raw_output = response.text.strip()
        raw_output = raw_output.replace("```json", "").replace("```", "").strip()
        adstock_ranges = json.loads(raw_output)

        logging.info(f"Retrieved adstock ranges from Gemini API: {adstock_ranges}")
        return adstock_ranges
    except Exception as e:
        logging.error(f"Error getting adstock ranges from Gemini: {e}")
        # Fallback default ranges if API call fails
        default_ranges = {}
        for channel in promo_channels:
            default_ranges[channel] = {
                "type": "Other",
                "min_adstock": 10,
                "max_adstock": 90
            }
        return default_ranges

def get_channel_adstock_range(promo_col):
    """
    Get recommended adstock range for a specific promotional channel.

    Args:
        promo_col (str): Name of the promotional channel column

    Returns:
        dict: Dictionary with channel type and min/max adstock values
    """
    # Get adstock ranges for all promo channels
    all_adstock_ranges = get_adstock_ranges_from_gemini([promo_col])
    print(f"All adstock ranges:{all_adstock_ranges}")
    # Get recommended range for this channel
    channel_range = all_adstock_ranges.get(promo_col, {
        "type": "Other",
        "min_adstock": 10,
        "max_adstock": 90
    })

    return channel_range
def get_transformation_functions():
    def safe_power(x, exp):
        return np.power(np.clip(x, 0, None), exp)

    def safe_log(x):
        return np.log1p(np.clip(x, 0, None))

    return {
        "Log": safe_log,
        # "Root1": lambda x: safe_power(x, 1/10),
        # "Root2": lambda x: safe_power(x, 2/10),
        # "Root3": lambda x: safe_power(x, 3/10),
        "Root4": lambda x: safe_power(x, 4/10),
        "Root5": lambda x: safe_power(x, 5/10),
        "Root6": lambda x: safe_power(x, 6/10),
        "Root7": lambda x: safe_power(x, 7/10),
        "Root8": lambda x: safe_power(x, 8/10),
        "Root9": lambda x: safe_power(x, 9/10)
        # "Sigmoid": lambda x: 1 / (1 + np.exp(-x))  # Optional, safe by design
    }

def apply_adstock(df, promo_col, id_col, adstock_rate):
    """
    Apply adstock transformation to a promotional channel with specified rate.

    Args:
        df (pd.DataFrame): Input dataframe
        promo_col (str): Name of promotional column
        id_col (str): Column that identifies time series groups
        adstock_rate (int): Adstock rate (0-100)

    Returns:
        tuple: (Column name, Series with adstocked values)
    """
    # print(f"apply_adedstock length: {len(df)}")
    rate = adstock_rate / 100
    adstocked = []

    for _, group in df.groupby(id_col):
        cumulative = 0
        group_adstocked = []
        for value in group[promo_col]:
            cumulative = value + rate * cumulative
            group_adstocked.append(cumulative)
        adstocked.extend(group_adstocked)

    adstock_col = f"{promo_col}_Adstock_{adstock_rate}"
    return adstock_col, pd.Series(adstocked, index=df.index)

def apply_transformation(series, transform_name, transform_func, base_col_name):
    """
    Apply transformation function to a series.

    Args:
        series (pd.Series): Data to transform
        transform_name (str): Name of transformation
        transform_func (callable): Transformation function
        base_col_name (str): Base column name

    Returns:
        tuple: (Column name, Transformed series)
    """
    # print(f"apply_transformation length: {len(df)}")
    transformed_col = f"{base_col_name}_{transform_name}"
    transformed_series = transform_func(series)
    return transformed_col, transformed_series
def fit_regression(df, feature_col,date_column, target_column,start_date,end_date, control_variables=None):
    """
    Fit regression model and return key metrics.

    Args:
        df (pd.DataFrame): Input dataframe
        feature_col (str): Feature column name
        target_column (str): Target column name
        control_variables (list): Additional control variables

    Returns:
        dict: Regression metrics or None if error
    """

    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
    # print(f"Fit_Regression length: {len(df)}")
    if control_variables is None:
        control_variables = []

    try:
        features = [feature_col] +[f"{target_column}_lag_1"]
        # features = [feature_col] + control_variables +[f"{target_column}_lag_1"]
        X = df[features].copy()
        X = sm.add_constant(X)
        y = df[target_column]

        model = sm.OLS(y, X).fit()

        return {
            'r_squared': model.rsquared,
            'p_value': model.pvalues[feature_col],
            'aic': model.aic
        }
    except Exception as e:
        logging.error(f"Error fitting model for {feature_col}: {e}")
        return None
    
def normalize_metric(values, higher_is_better=True):
    """
    Normalize metric values to 0-1 range.

    Args:
        values (array): Metric values
        higher_is_better (bool): Whether higher values are better

    Returns:
        array: Normalized values
    """
    if len(values) <= 1:
        return np.ones_like(values)

    min_val = np.min(values)
    max_val = np.max(values)

    if max_val == min_val:
        return np.ones_like(values)

    if higher_is_better:
        return (values - min_val) / (max_val - min_val)
    else:
        return (max_val - values) / (max_val - min_val)
def calculate_composite_score_transformation(results_df):
    """
    Calculate composite score based on metrics and business rules.

    Args:
        results_df (pd.DataFrame): DataFrame with model results

    Returns:
        pd.DataFrame: DataFrame with scores added
    """
    # Normalize metrics
    results_df['r_squared_norm'] = normalize_metric(results_df['r_squared'].values, higher_is_better=True)
    results_df['p_value_norm'] = normalize_metric(results_df['p_value'].values, higher_is_better=False)
    results_df['aic_norm'] = normalize_metric(results_df['aic'].values, higher_is_better=False)

    # Apply business score based on recommended range
    results_df['business_score'] = results_df['in_range'].apply(lambda x: 1.0 if x else 0.5)

    # Apply weights and calculate composite score
    # Mathematical metrics (100%)
    # R²: 40%, p-value: 20%, AIC: 40%
    results_df['math_score'] = (
        0.4 * results_df['r_squared_norm'] +
        0.2 * results_df['p_value_norm'] +
        0.4 * results_df['aic_norm']
    )

    # Final composite score - business score can boost combinations within recommended range
    results_df['final_score'] = 0.5*results_df['math_score'] +0.5* results_df['business_score']

    return results_df
def select_best_adstock_and_transformation(df, promo_col, id_col, target_column,date_column,start_date,end_date,control_variables=None):
    global temp_df, global_feature_df
    import gc
    """
    Select the top 3 adstock rate and transformation combinations for a promotional channel
    using composite scoring of multiple metrics. Include control variables in the regression.

    Optimized to avoid dataframe fragmentation.

    Args:
        df (pd.DataFrame): Input dataframe
        promo_col (str): Name of promotional column
        id_col (str): Column that identifies time series groups
        target_column (str): Target variable name
        control_variables (list): Additional control variables to include in regression

    Returns:
        tuple: (Updated dataframe, list of top 3 transformed column names)
    """
    print(f"\nSelecting top 3 adstock and transformation combinations for: {promo_col}")

    

    # Get channel info
    channel_range = get_channel_adstock_range(promo_col)
    min_adstock = channel_range["min_adstock"]
    max_adstock = channel_range["max_adstock"]
    channel_type = channel_range["type"]

    print(f"Channel type: {channel_type}, Recommended adstock range: {min_adstock}% to {max_adstock}%")

    # Get transformation functions
    transformations = get_transformation_functions()

    # Variables to store results and new columns to be added
    results = []
    new_columns = {}
    new_global_columns = {}  # To store all columns for global_feature_df

    

    # Iterate over all adstock rates
    for adstock_rate in range(10, 100, 10):
        # Apply adstock
        temp_df = df.copy()
        adstock_col, adstocked_series = apply_adstock(df, promo_col, id_col, adstock_rate)
        new_columns[adstock_col] = adstocked_series
        temp_df[adstock_col] = adstocked_series

        # Store for later batch addition to global_feature_df
        new_global_columns[adstock_col] = adstocked_series

        # Check if adstock rate is within recommended range (business context)
        in_recommended_range = min_adstock <= adstock_rate <= max_adstock

        # Apply all transformations to this adstocked column
        for transform_name, transform_func in transformations.items():
            transformed_col, transformed_series = apply_transformation(
                temp_df[adstock_col], transform_name, transform_func, adstock_col
            )
            new_columns[transformed_col] = transformed_series
            temp_df[transformed_col] = transformed_series

            # Store for later batch addition to global_feature_df
            new_global_columns[transformed_col] = transformed_series

            # Fit regression and get metrics
            metrics = fit_regression(temp_df, transformed_col,date_column,target_column,start_date,end_date, control_variables)

            if metrics:
                # Add to results
                results.append({
                    'adstock_rate': adstock_rate,
                    'transform': transform_name,
                    'col_name': transformed_col,
                    'r_squared': metrics['r_squared'],
                    'p_value': metrics['p_value'],
                    'aic': metrics['aic'],
                    'in_range': in_recommended_range
                })
    del temp_df
    # Add all new columns to global_feature_df at once to avoid fragmentation
    # if new_global_columns:
    #     global_feature_df = pd.concat([global_feature_df, pd.DataFrame(new_global_columns)], axis=1)
    #     # Make a copy to defragment after all additions
    #     global_feature_df = global_feature_df.copy()

    # Check if we have valid results
    if not results:
        logging.error(f"No valid models found for {promo_col}")
        return df, [], []

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)

    # Calculate scores
    results_df = calculate_composite_score_transformation(results_df)


    # Get the top 3 combinations
    results_df = results_df.sort_values('final_score', ascending=False)
    top_3_rows = results_df.head(3)

    # Create lists to store top 3 columns
    top_adstock_cols = []
    top_transformed_cols = []

    # Print details of the top 3 combinations
    print(f"\nTop 3 combinations selected for {promo_col}:")

    for i, (_, row) in enumerate(top_3_rows.iterrows(), 1):
        adstock_col = f"{promo_col}_Adstock_{int(row['adstock_rate'])}"
        transformed_col = row['col_name']

        top_adstock_cols.append(adstock_col)
        top_transformed_cols.append(transformed_col)

        print(f"\nCombination #{i}:")
        print(f"- Adstock rate: {int(row['adstock_rate'])}%")
        print(f"- Transformation: {row['transform']}")
        print(f"- R²: {row['r_squared']:.4f}")
        print(f"- p-value: {row['p_value']:.4f}")
        print(f"- AIC: {row['aic']:.4f}")
        print(f"- In recommended range: {'Yes' if row['in_range'] else 'No'}")
        print(f"- Final score: {row['final_score']:.4f}")

    # Only add the necessary columns to the original dataframe using concat
    # This includes all columns needed for the top 3 combinations
    columns_to_add = {}
    for col in set(top_adstock_cols + top_transformed_cols):
        columns_to_add[col] = new_columns[col]

    new_df = pd.concat([df, pd.DataFrame(columns_to_add)], axis=1)

    return new_df, top_adstock_cols, top_transformed_cols,channel_range

def process_promo_channels(df,promo_channels,target_column,id_column,date_column,start_date,end_date,control_variables):
    """Process each promotional channel to find best transformations"""
   
    
    # Dictionary to store top transformed columns for each promo channel
    transformed_channels_by_promo = {}
    adstocked_channels_by_promo = {}
    adstock_range_channel = {}
    all_transformed_features = []

    for promo_col in promo_channels:
        print(f"\nProcessing promotional channel: {promo_col}")

        # Use the modified method to get top 3 combinations with control variables
        df, best_adstock_cols, best_transformed_cols, adstock_range = select_best_adstock_and_transformation(
            df, promo_col, id_column, target_column,date_column, start_date, end_date, control_variables
        )

        print(f"- Best Adstock columns: {', '.join(best_adstock_cols)}")
        print(f"- Best Transformed columns: {', '.join(best_transformed_cols)}")

        # Store the top 3 transformed columns for this promo channel
        adstocked_channels_by_promo[promo_col] = best_adstock_cols
        adstock_range_channel[promo_col] = adstock_range
        transformed_channels_by_promo[promo_col] = best_transformed_cols
        all_transformed_features.extend(best_transformed_cols)

    if not all_transformed_features:
        return False, df, {}, {}, {}, [] 
    
    return True,df,transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_transformed_features

def get_benchmark_values(promo_channels, historical_df,historical_channel_column,historical_impact):
    """
    Gets benchmark values for promotional channels, asking only once per base channel
    and applying that value to all its transformations.

    Args:
        promo_channels: List of transformed promotional channel names

    Returns:
        Dictionary mapping each transformed channel to its benchmark value
    """
    
    # Identify base channel names and group transformations
    base_channels = {}

    # Common transformation indicators that would appear in transformed channel names
    transform_indicators = ['_adstock', '_decay', '_power', '_lag', '_transform', '_delayed']

    for channel in promo_channels:
        # Find if this is a transformed channel by looking for transformation indicators
        is_transformed = False
        base_name = channel

        for indicator in transform_indicators:
            if indicator.lower() in channel.lower():
                is_transformed = True
                # Extract the base name (everything before the transformation indicator)
                parts = channel.lower().split(indicator.lower(), 1)
                base_name = parts[0].rstrip('_')
                break

        # If no transformation indicator found, it's likely a base channel itself
        if not is_transformed:
            # For channels with format like "Channel_Adstock10", try to detect numbers
            numeric_split = re.match(r'(.+?)(\d+)$', channel)
            if numeric_split:
                base_name = numeric_split.group(1).rstrip('_')

        # Store in our dictionary
        if base_name not in base_channels:
            base_channels[base_name] = []
        base_channels[base_name].append(channel)

    # Get benchmark values for each base channel
    typical_values = {}
    
    d={str(k).lower(): v for k, v in zip(historical_df[historical_channel_column], historical_df[historical_impact])}
    

    # Ask for benchmark values only once per base channel
    for base_name, transformed_channels in base_channels.items():
        try:
            # benchmark = float(input(f"Enter benchmark for channel: {base_name}"))
            benchmark=d[base_name]

            # Apply this benchmark to all transformations of this base channel
            for channel in transformed_channels:
                typical_values[channel] = benchmark

        except ValueError:
            print(f"Invalid input for {base_name}. Using default value of 0.")
            for channel in transformed_channels:
                typical_values[channel] = 0.0

    return typical_values

def filter_dataframe_by_date(df, date_column, start_date, end_date):
    """Filter dataframe by start and end dates"""
   
    
    print("\nSelecting optimal channel combination...")
    print(f"before optimal channel selection {len(df)}")

    # Filter the dataframe before optimal channel selection
    df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
    if start_date and end_date:
            df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
    
    return df

def setup_parameters(date_column, id_column, target_column, promo_channels, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact):
    """Set up the parameters dictionary for modeling"""
    global params
    
    params = {
        'main_dataset': {
            'date_column': date_column,
            'id_column': id_column,
            'target_column': target_column,
            'promo_channels': promo_channels,
            'normalization_method': 'Percentile',
            'population_column': None,
            'start_date': start_date,
            'end_date': end_date,
            'data_level': 'HCP'
        },
        'spend_dataset': {
            'channel_column': spend_channel_column,
            'spend_column': spend_column
        },
        'historical_dataset': {
            'channel_column': historical_channel_column,
            'historical_impact': historical_impact
        }
    }
    
    return params

import numpy as np
import pandas as pd
import statsmodels.api as sm
import itertools
import logging
import traceback

def evaluate_model(df, features, target_column, benchmark_values=None):
    """
    Evaluate linear regression model with given features.

    Args:
        df (pd.DataFrame): Input data
        features (list): List of feature columns
        target_column (str): Target variable name
        benchmark_values (dict): Dictionary of benchmark values

    Returns:
        dict: Dictionary of model metrics
    """
    # Prepare data
    X = df[features].dropna()
    y = df.loc[X.index, target_column]

    if len(X) < len(features) + 2:
        print(f"  Not enough data points ({len(X)}) for {len(features)} features")
        return None

    # Add constant for intercept
    X_with_const = sm.add_constant(X)

    try:
        # Fit model
        model = sm.OLS(y, X_with_const).fit()

        # Get coefficients
        coefficients = model.params.drop('const').to_dict() if 'const' in model.params else model.params.to_dict()
        target_sum=df[target_column].sum()

        for col in features:
          estimate = model.params[col]
          activity = df[col].sum()

          impact_percentage = ((estimate * activity) / target_sum) if target_sum != 0 else 0

        # Calculate p-values for features
        p_values = model.pvalues.drop('const').to_dict() if 'const' in model.pvalues else model.pvalues.to_dict()
        avg_p_value = np.mean(list(p_values.values()))

        # Calculate benchmark deviations if benchmarks provided
        benchmark_deviations = {}
        if benchmark_values:
            for feature in coefficients:
                if feature in benchmark_values:
                    benchmark_val = benchmark_values[feature]
                    model_val = impact_percentage
                    relative_deviation = (model_val - benchmark_val) / ((benchmark_val) + 1e-6)
                    benchmark_deviations[feature] = relative_deviation

        avg_benchmark_deviation = np.mean(list(benchmark_deviations.values())) if benchmark_deviations else 0

        # Return metrics
        return {
            'R2': model.rsquared,
            'adj_R2': model.rsquared_adj,
            'AIC': model.aic,
            'BIC': model.bic,
            'coefficients': coefficients,
            'p_values': p_values,
            'avg_p_value': avg_p_value,
            'benchmark_deviations': benchmark_deviations,
            'avg_benchmark_deviation': avg_benchmark_deviation
        }

    except Exception as e:
        print(f"  Error evaluating model: {str(e)}")
        return None



def calculate_incremental_impact(baseline_model, augmented_model):
    """
    Calculate the impact of adding a new variable to the model.

    Args:
        baseline_model (dict): Metrics from baseline model
        augmented_model (dict): Metrics from augmented model (with additional variable)

    Returns:
        float: Incremental impact score
    """
    if not baseline_model or not augmented_model:
        return 0.0

    baseline_coefs = baseline_model['coefficients']
    augmented_coefs = augmented_model['coefficients']

    # Get common coefficients
    common_features = set(baseline_coefs.keys()) & set(augmented_coefs.keys())

    changes = []
    epsilon = 1e-6  # Small value to avoid division by zero

    for feat in common_features:
        baseline_val = baseline_coefs[feat]
        augmented_val = augmented_coefs[feat]

        relative_change = (augmented_val - baseline_val) / ((baseline_val) + epsilon)
        changes.append(relative_change)

    # Return average relative change
    return np.mean(changes) if changes else 0.0
def normalize_metric_channel(value, min_val, max_val, higher_is_better=True):
    """
    Normalize a metric to a 0-1 scale.

    Args:
        value (float): Metric value
        min_val (float): Minimum value in range
        max_val (float): Maximum value in range
        higher_is_better (bool): Whether higher values are better

    Returns:
        float: Normalized value
    """
    if max_val <= min_val:
        return 0.5  # Default if range is invalid

    # Clip value to range
    value = max(min_val, min(max_val, value))

    # Normalize
    if higher_is_better:
        return (value - min_val) / (max_val - min_val)
    else:
        return 1 - ((value - min_val) / (max_val - min_val))

def calculate_composite_score(metrics, ranges, weights):
    """
    Calculate composite score based on multiple metrics.

    Args:
        metrics (dict): Model evaluation metrics
        ranges (dict): Min-max ranges for each metric
        weights (dict): Weight for each metric in final score

    Returns:
        float: Composite score
    """
    # Normalize metrics
    r2_norm = normalize_metric_channel(metrics['R2'], *ranges['R2'], higher_is_better=True)
    p_norm = normalize_metric_channel(metrics['avg_p_value'], *ranges['p_value'], higher_is_better=False)
    aic_norm = normalize_metric_channel(metrics['AIC'], *ranges['AIC'], higher_is_better=False)

    # if 'incremental_impact' in metrics:
    #     # Lower incremental impact is better (less change to existing coefficients)
    #     incremental_norm = normalize_metric_channel(metrics['incremental_impact'],
    #                                      *ranges['incremental'],
    #                                      higher_is_better=False)
    # else:
    #     incremental_norm = 0.5  # Default if not available

    # Calculate benchmark score
    if 'avg_benchmark_deviation' in metrics:
        benchmark_score = max(0, (metrics['avg_benchmark_deviation'] / ranges['benchmark_deviation'][1]))
    else:
        benchmark_score = 0.5  # Default if not available

    # Calculate composite score
    composite_score = (
        weights['R2'] * r2_norm +
        weights['p_value'] * p_norm +
        weights['AIC'] * aic_norm +
        # weights['incremental'] * incremental_norm +
        weights['benchmark'] * benchmark_score
    )

    return composite_score




def select_optimal_channels(df, transformed_channels_by_promo, target_column, control_variables=None,
                           benchmark_values=None):
    import numpy as np
    import pandas as pd
    import copy

    print("\nSelecting optimal channel combination using one-variable-at-a-time optimization...")

    # Initialize control variables if not provided
    if control_variables is None:
        control_variables = []

    # Define ranges for normalization
    ranges = {
        'R2': (0.5, 0.95),  # Range for R²
        'p_value': (0.01, 0.1),  # Range for p-values
        'AIC': (df[target_column].min(), df[target_column].max() * 2),  # Range for AIC
        'benchmark_deviation': (0, 0.5)  # Range for benchmark deviation
    }

    # Define weights for composite score
    weights = {
        'R2': 0.20,        # 20% weight on R²
        'p_value': 0.20,   # 20% weight on p-value
        'AIC': 0.10,       # 10% weight on AIC
        'benchmark': 0.50  # 50% weight on business benchmark
    }

    # Results storage for all tested combinations
    all_results = []

    # Start with baseline model (only control variables)
    # base_features = control_variables.copy() + [f"{target_column}_lag_1"]
    base_features = [f"{target_column}_lag_1"]

    # Get promo channels
    promo_channels = list(transformed_channels_by_promo.keys())
    print(f"Evaluating {len(promo_channels)} promotional channels using one-variable-at-a-time optimization...")

    # Initialize with first transformation for each channel
    current_best_transforms = {}
    for channel in promo_channels:
        if transformed_channels_by_promo[channel]:  # Ensure there's at least one transformation
            current_best_transforms[channel] = transformed_channels_by_promo[channel][0]

    # One variable at a time optimization
    for channel_idx, current_channel in enumerate(promo_channels):
        print(f"\nTuning channel {channel_idx+1}/{len(promo_channels)}: {current_channel}")

        # Get transformations for this channel
        channel_transforms = transformed_channels_by_promo[current_channel]
        print(f"Testing {len(channel_transforms)} transformations for {current_channel}...")

        # Track best for this channel
        channel_best_score = -np.inf
        channel_best_transform = None
        channel_best_metrics = None

        # Try each transformation for this channel
        for transform_idx, transform in enumerate(channel_transforms):
            # Build feature set with current transformation for this channel and best for others
            feature_set = base_features.copy()
            for other_channel, best_transform in current_best_transforms.items():
                if other_channel == current_channel:
                    feature_set.append(transform)  # Use current transformation being tested
                else:
                    feature_set.append(best_transform)  # Use best transformation for other channels

            print(f"  Testing transformation {transform_idx+1}/{len(channel_transforms)}: {transform}")

            # Evaluate this feature set
            print(f"Feature set{feature_set}")
            metrics = evaluate_model(df, feature_set, target_column, benchmark_values)

            if not metrics:
                print(f"    Skipping invalid combination with {transform}")
                continue

            # Calculate composite score
            composite_score = calculate_composite_score(metrics, ranges, weights)
            metrics['composite_score'] = composite_score

            # Store result
            result = {
                'channel': current_channel,
                'transformation': transform,
                'features': feature_set.copy(),  # Store actual feature list instead of string
                'features_str': str(feature_set),  # Keep string version for display
                'R2': metrics['R2'],
                'AIC': metrics['AIC'],
                'avg_p_value': metrics['avg_p_value'],
                'avg_benchmark_deviation': metrics.get('avg_benchmark_deviation', 0),
                'composite_score': composite_score
            }
            all_results.append(result)

            # Check if this is best for current channel
            if composite_score > channel_best_score:
                channel_best_score = composite_score
                channel_best_transform = transform
                channel_best_metrics = metrics.copy()
                print(f"    New best for channel {current_channel}! Score: {composite_score:.4f}")
                print(f"    R²: {metrics['R2']:.4f}, AIC: {metrics['AIC']:.2f}")

        # Update the best transformation for this channel
        if channel_best_transform:
            print(f"\nBest transformation for {current_channel}: {channel_best_transform}")
            print(f"Score: {channel_best_score:.4f}")

            # Update current best transformation for this channel
            current_best_transforms[current_channel] = channel_best_transform
        else:
            print(f"Warning: No valid transformation found for channel {current_channel}")

    # Convert final best transformations to feature list
    best_features = base_features.copy()
    for channel, transform in current_best_transforms.items():
        best_features.append(transform)

    # Evaluate final model with all channels
    final_metrics = evaluate_model(df, best_features, target_column, benchmark_values)
    if not final_metrics:
        print("Warning: Final model with all channels could not be evaluated")
        # Fall back to the last valid metrics
        for channel in reversed(promo_channels):
            if channel in current_best_transforms:
                # Remove the last added channel transform
                partial_features = [f for f in best_features if f != current_best_transforms[channel]]
                partial_metrics = evaluate_model(df, partial_features, target_column, benchmark_values)
                if partial_metrics:
                    final_metrics = partial_metrics
                    print(f"Using metrics from model without {channel}")
                    best_features = partial_features  # Update best_features to the valid set
                    break

    # Convert results to DataFrame for easier analysis
    results_df = pd.DataFrame(all_results)

    # Get top 5 alternative models
    top_5_models = []
    if not results_df.empty:
        # Create a new column with a unique feature set identifier for grouping
        results_df['feature_set_key'] = results_df['features_str'].apply(lambda x: str(sorted(eval(x))))

        # Group by unique feature sets and get the highest score for each
        grouped_results = results_df.sort_values('composite_score', ascending=False)
        grouped_results = grouped_results.drop_duplicates(subset=['feature_set_key'])

        # Sort by composite score
        top_models = grouped_results.head(6)  # Get top 6 to include the best one

        # Compare each model feature set with the best one
        best_model_key = str(sorted(best_features))

        # Get top 5 models that are different from the best one
        count = 0
        for _, row in top_models.iterrows():
            model_features = row['features']  # Get the actual feature list

            # Skip if this is the same as the best model
            if str(sorted(model_features)) == best_model_key and count < 5:
                continue

            if count < 5:
                top_5_models.append({
                    'channel': row['channel'],
                    'transformation': row['transformation'],
                    'features': model_features,  # Store actual feature list
                    'features_str': row['features_str'],  # Keep string version for display
                    'R2': row['R2'],
                    'AIC': row['AIC'],
                    'avg_p_value': row['avg_p_value'],
                    'composite_score': row['composite_score']
                })
                count += 1

        # Save all results to CSV
        # Convert feature lists back to strings for CSV export
        export_results_df = results_df.copy()
        export_results_df['features'] = export_results_df['features_str']
        export_results_df = export_results_df.drop(['features_str', 'feature_set_key'], axis=1)

        csv_filename = "optimal_channel_selection_results.csv"
        export_results_df.to_csv(csv_filename, index=False)
        print(f"Saved detailed results to {csv_filename}")

        # Save top 5 alternative models to a separate CSV
        if top_5_models:
            # Prepare top 5 models for export
            top5_export = [{
                'channel': model['channel'],
                'transformation': model['transformation'],
                'features': model['features_str'],
                'R2': model['R2'],
                'AIC': model['AIC'],
                'avg_p_value': model['avg_p_value'],
                'composite_score': model['composite_score']
            } for model in top_5_models]

            top5_df = pd.DataFrame(top5_export)
            top5_csv_filename = "top5_alternative_models.csv"
            top5_df.to_csv(top5_csv_filename, index=False)
            print(f"Saved top 5 alternative models to {top5_csv_filename}")

    # Calculate composite score for final best features
    best_score = -np.inf
    if final_metrics:
        best_score = calculate_composite_score(final_metrics, ranges, weights)

    # Print best model details
    if final_metrics:
        promo_only = [f for f in best_features if f not in base_features]
        print("\nFinal optimal channel combination:")
        print(f"- Promotional channels: {', '.join(promo_only)}")
        print(f"- R²: {final_metrics['R2']:.4f}")
        print(f"- AIC: {final_metrics['AIC']:.2f}")
        print(f"- Avg. p-value: {final_metrics['avg_p_value']:.4f}")
        print(f"- Composite score: {best_score:.4f}")

    # Print top 5 alternative models
    if top_5_models:
        print("\nTop 5 alternative models:")
        for i, model in enumerate(top_5_models):
            print(f"{i+1}. Score: {model['composite_score']:.4f}, R²: {model['R2']:.4f}")
            print(f"   Channel: {model['channel']}, Transform: {model['transformation']}")
            promo_only = [f for f in model['features'] if f not in base_features]
            print(f"   Promotional channels: {', '.join(promo_only)}")

    # Extract feature sets for all 6 models (best + top 5 alternatives)
    all_model_features = [best_features]
    all_model_metrics = [final_metrics]

    for model in top_5_models:
        all_model_features.append(model['features'])

        # Get metrics for this feature set
        model_metrics = evaluate_model(df, model['features'], target_column, benchmark_values)
        all_model_metrics.append(model_metrics)

    return best_features, final_metrics, all_model_features, all_model_metrics,top_5_models


def fix_negative_estimates(df, best_features, target_column, control_variables, date_column, transformed_channels_by_promo, all_transformed_features, id_column,start_date,end_date):
    """
    Fix negative estimates in the best_features model by trying different adstock and transformation combinations.
    If a channel cannot be fixed, it will be removed from the model.

    Returns:
        Tuple of (new_best_features, fixed_channels_info)
    """

    print("\nChecking for negative estimates in best model...")

    # First run the regression with current best features to identify negative estimates
    result_table = perform_regression(df, best_features, target_column, date_column,start_date,end_date)

    if not isinstance(result_table, pd.DataFrame) or result_table.empty:
        print("Error running initial regression. Cannot fix negative estimates.")
        return best_features, {}

    # Check if we have the expected format from perform_regression
    if 'Channel' in result_table.columns and 'Estimate' in result_table.columns:
        var_column = 'Channel'
        est_column = 'Estimate'
    else:
        print("Warning: Unexpected regression result table format.")
        print("Available columns:", result_table.columns.tolist())
        print("Cannot proceed with fixing negative estimates.")
        return best_features, {}

    # Identify promotional channels with negative estimates
    negative_channels = []
    for _, row in result_table.iterrows():
        var_name = row[var_column]

        # Skip the intercept and any non-promotion variables
        if var_name == 'Intercept' or var_name not in all_transformed_features:
            continue

        est_value = row[est_column]

        if est_value < 0:
            # Extract the base channel name from the transformed variable
            base_channel = None
            for promo_col, transformed_cols in transformed_channels_by_promo.items():
                if var_name in transformed_cols:
                    base_channel = promo_col
                    break

            if base_channel:
                negative_channels.append((base_channel, var_name, est_value))

    if not negative_channels:
        print("No negative estimates found in promotional channels. Model is optimal.")
        return best_features, {}

    print(f"Found {len(negative_channels)} promotional channels with negative estimates:")
    for base_channel, variable, estimate in negative_channels:
        print(f"- {base_channel} (via {variable}): {estimate:.4f}")

    # Dictionary to track fixed channels and their new transformations
    fixed_channels = {}
    channels_to_remove = []
    
    # Dictionary to store only the final best transformations that we'll add to df
    best_transformations = {}

    # For each negative channel, try all adstock and transformation combinations
    for base_channel, current_variable, _ in negative_channels:
        print(f"\nAttempting to fix negative estimate for {base_channel}...")

        # Get adstock ranges for this channel
        adstock_ranges = get_adstock_ranges_from_gemini([base_channel])
        channel_range = adstock_ranges.get(base_channel, {
            "type": "Other",
            "min_adstock": 10,
            "max_adstock": 90
        })

        min_adstock = channel_range["min_adstock"]
        max_adstock = channel_range["max_adstock"]

        # Define the adstock values to test
        adstock_values = list(range(min_adstock, max_adstock + 1, 10))

        # Define transformations to test
        transformations = get_transformation_functions()
        best_estimate = 0
        best_variable = None
        best_transformed_series = None

        # Test each combination
        for adstock_rate in adstock_values:
            # Apply adstock transformation to a temporary series, not the original df
            adstock_col, adstocked_series = apply_adstock(df, base_channel, id_column, adstock_rate)
            
            for transform_name, transform_func in transformations.items():
                # Apply transformation to the temporary series
                transformed_col, transformed_series = apply_transformation(
                    adstocked_series, transform_name, transform_func, adstock_col
                )
                
                # Create a temporary DataFrame for this test
                temp_df = df.copy()
                temp_df[transformed_col] = transformed_series

                # Create a temporary feature set replacing the current transformation
                temp_features = best_features.copy()

                # Remove the current transformation of this channel from the features
                for chan_transform in transformed_channels_by_promo.get(base_channel, []):
                    if chan_transform in temp_features:
                        temp_features.remove(chan_transform)

                # Add the new transformation
                temp_features.append(transformed_col)

                # Run regression with this combination
                temp_result = perform_regression(temp_df, temp_features, target_column, date_column,start_date,end_date)

                if isinstance(temp_result, pd.DataFrame) and not temp_result.empty:
                    # Find the estimate for this new transformed variable
                    for _, row in temp_result.iterrows():
                        if var_column in row and row[var_column] == transformed_col:
                            if est_column in row:
                                estimate = row[est_column]
                                if estimate > best_estimate:
                                    best_estimate = estimate
                                    best_variable = transformed_col
                                    best_transformed_series = transformed_series

                # Clean up the temp_df to free memory
                del temp_df

        # After testing all combinations, check if we found a positive estimate
        if best_estimate > 0:
            print(f"Fixed negative estimate for {base_channel}. New variable: {best_variable}, New estimate: {best_estimate:.4f}")
            fixed_channels[base_channel] = {
                'old_variable': current_variable,
                'new_variable': best_variable,
                'new_estimate': best_estimate
            }
            # Store the best transformation to add to df later
            best_transformations[best_variable] = best_transformed_series
        else:
            print(f"Could not find positive estimate for {base_channel}. Will remove from model.")
            channels_to_remove.append(base_channel)

    # Update the best_features list with the fixed transformations
    new_best_features = best_features.copy()

    # First, remove ALL transformed variables for channels that need to be replaced or removed
    channels_to_process = list(fixed_channels.keys()) + channels_to_remove
    for base_channel in channels_to_process:
        # Remove all transformations of this channel from the feature list
        for transformed_col in transformed_channels_by_promo.get(base_channel, []):
            if transformed_col in new_best_features:
                new_best_features.remove(transformed_col)

    # Add back new variables for channels that were successfully fixed
    for base_channel, info in fixed_channels.items():
        # Add the new variable
        if info['new_variable'] not in new_best_features:
            new_best_features.append(info['new_variable'])
            
        # Now add the best transformation to the original df
        if info['new_variable'] in best_transformations:
            df[info['new_variable']] = best_transformations[info['new_variable']]

    # Run a final check to ensure no negative estimates remain
    final_check_result = perform_regression(df, new_best_features, target_column, date_column,start_date,end_date)
    if isinstance(final_check_result, pd.DataFrame) and not final_check_result.empty:
        still_negative = []

        for _, row in final_check_result.iterrows():
            if (var_column in row and row[var_column] in all_transformed_features and
                est_column in row and row[est_column] < 0):
                still_negative.append(row[var_column])

        if still_negative:
            print(f"Warning: {len(still_negative)} promotional channels still have negative estimates:")
            for var in still_negative:
                print(f"- {var}")
            print("Consider manual inspection of these variables.")

    print(f"\nOriginal model had {len(best_features)} features.")
    print(f"New model has {len(new_best_features)} features.")
    print(f"Fixed {len(fixed_channels)} channels with negative estimates.")
    print(f"Removed {len(channels_to_remove)} channels that couldn't be fixed.")

    return new_best_features, fixed_channels

def run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics):
    """Run regression analyses for top models and save results without Excel export"""
    global all_regression_results, best_result_table
    print(f"best fetaures in run_regression_analyses{best_features}")
    print(f"all_feature_sets in run_regression_analyses{all_feature_sets}")
    all_regression_results = []
    
    print("\nRunning regression analysis for top 6 models...")
    for i, feature_set in enumerate(all_feature_sets[:6]):  # Limit to top 6 models
        model_name = "Best Model" if i == 0 else f"Alternative Model {i}"
        print(f"\nRunning regression for {model_name}...")

        # Extract only promotional channels for display
        promo_features = [f for f in feature_set if f in all_transformed_features]
        promo_features_str = ', '.join(promo_features)
        print(f"Promotional channels: {promo_features_str}")

        # Perform regression
        result_table = perform_regression(df, feature_set, target_column, date_column, start_date, end_date)

        # Add model identifier
        if isinstance(result_table, pd.DataFrame) and not result_table.empty:
            try:
                # Add model columns if there's at least one row
                if len(result_table) > 0:
                    result_table['model'] = model_name
                    result_table['model_rank'] = i + 1

                all_regression_results.append(result_table)

                # Get metrics for this model
                metrics = all_metrics[i] if i < len(all_metrics) else None
                if metrics:
                    r2 = metrics['R2']
                    aic = metrics['AIC']
                    avg_p_value = metrics['avg_p_value']
                    print(f"R²: {r2:.4f}, AIC: {aic:.2f}")
                else:
                    print("Metrics not available for this model")
            except Exception as e:
                print(f"Error processing regression results: {e}")
        else:
            print(f"Error running regression for {model_name}")

    # Set the best result table
    best_result_table = all_regression_results[0] if all_regression_results else None

    return all_regression_results, best_result_table

def display_best_model_results():
    """Display the results of the best model"""
    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:
        print("\nBest Model Regression Results:")
        print("="*80)
        try:
            print(best_result_table.to_string(index=False))
        except Exception as e:
            print(f"Error displaying results table: {e}")
            print("Results available in Excel file.")
        print("="*80)
        return True
    return False

import psycopg2
import json
from psycopg2.extras import Json
import pandas as pd
import datetime
import logging

def update_model_regression(conn, model_run_id, all_transformed_features, best_result_table, 
                           transformed_channels_by_promo, adstocked_channels_by_promo, 
                           adstock_range_channel, parms, all_regression_results, best_features, all_feature_sets):
    """
    Update the JSONB fields in the model_data table for a specific model_run_id.
    
    Parameters:
    conn: PostgreSQL connection object
    model_run_id: UUID of the model run to update
    all_transformed_features: List of feature names
    best_result_table: DataFrame (will be converted to JSON)
    transformed_channels_by_promo: Dict
    adstocked_channels_by_promo: Dict
    adstock_range_channel: Dict
    parms: Dict (contains date column)
    all_regression_results: List of DataFrames
    best_features: List of best features
    all_feature_sets: List of all feature sets
    """
    if not conn or conn.closed:
        logging.error("Invalid database connection")
        return False
    
    # Custom JSON encoder to handle datetime objects
    class DateTimeEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, (datetime.datetime, datetime.date)):
                return obj.isoformat()
            return super().default(obj)
    
    # Helper function to safely convert any pandas DataFrames to serializable dictionaries
    def convert_pandas_to_dict(obj):
        if isinstance(obj, pd.DataFrame):
            # Convert DataFrame to dict with records orientation and convert datetime columns to ISO format
            for col in obj.select_dtypes(include=['datetime64']).columns:
                obj[col] = obj[col].astype(str)
            return json.loads(obj.to_json(orient='records', date_format='iso'))
        elif isinstance(obj, pd.Series):
            # Handle datetime Series
            if pd.api.types.is_datetime64_any_dtype(obj):
                obj = obj.astype(str)
            return json.loads(obj.to_json())
        elif isinstance(obj, dict):
            # Handle dictionaries - recursively process all values
            return {k: convert_pandas_to_dict(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            # Handle lists - recursively process all items
            return [convert_pandas_to_dict(item) for item in obj]
        elif isinstance(obj, (datetime.datetime, datetime.date)):
            # Convert datetime objects to ISO format string
            return obj.isoformat()
        else:
            return obj
    
    try:
        # Convert each parameter to a serializable format
        serialized_all_transformed_features = convert_pandas_to_dict(all_transformed_features)
        serialized_best_result_table = convert_pandas_to_dict(best_result_table)
        serialized_transformed_channels = convert_pandas_to_dict(transformed_channels_by_promo)
        serialized_adstocked_channels = convert_pandas_to_dict(adstocked_channels_by_promo)
        serialized_adstock_range = convert_pandas_to_dict(adstock_range_channel)
        serialized_parms = convert_pandas_to_dict(parms)
        serialized_regression_results = convert_pandas_to_dict(all_regression_results)
        serialized_best_features = convert_pandas_to_dict(best_features)
        serialized_all_feature_sets = convert_pandas_to_dict(all_feature_sets)
        
        # Create a cursor
        cursor = conn.cursor()
        
        # SQL query for update - now including model_status
        update_query = """
        UPDATE model_data
        SET 
            all_transformed_features = %s,
            best_result_table = %s,
            transformed_channels_by_promo = %s,
            adstocked_channels_by_promo = %s,
            adstock_range_channel = %s,
            parms = %s,
            model_results = %s,
            best_features = %s,
            all_features = %s,
            model_status = %s,
            updated_at = CURRENT_TIMESTAMP
        WHERE model_run_id = %s
        """
        
        # Execute the query with parameters - now including model_status
        cursor.execute(update_query, (
            Json(serialized_all_transformed_features),
            Json(serialized_best_result_table),
            Json(serialized_transformed_channels),
            Json(serialized_adstocked_channels),
            Json(serialized_adstock_range),
            Json(serialized_parms),
            Json(serialized_regression_results),
            Json(serialized_best_features),
            Json(serialized_all_feature_sets),
            "complete",  # Set model_status to "complete"
            model_run_id
        ))
        
        # Commit the transaction
        conn.commit()
        
        print(f"Successfully updated model run data for model_run_id: {model_run_id}, status set to complete")
        return True
    except Exception as e:
        print(f"Error updating database: {e}")
        conn.rollback()
        return False
    finally:
        # Close the cursor if it exists
        if 'cursor' in locals() and cursor is not None:
            cursor.close()



def get_channel_stats(best_result_table, transformed_channel_by_promo=None):
    """
    Returns the names of channels (excluding 'Intercept'), the R² value, 
    the Modelled Sales value, and a list of significant channels (p-value < 0.05)
    from best_result_table, with the significant channels converted back to their base names.
    
    Parameters:
    best_result_table (DataFrame): A DataFrame containing columns 'Channel', 
                                  'P-Value', 'Adjusted R²', and 'Modeled Sales'
    transformed_channel_by_promo (dict): Dictionary mapping base channel names to their 
                                         transformed versions
    
    Returns:
    tuple: (channel_names, r_squared, modelled_sales, significant_channels)
    """
    # Get the transformed names of channels excluding 'Intercept'
    transformed_channel_names = best_result_table[best_result_table['Channel'] != 'Intercept']['Channel'].tolist()
    
    # Create a mapping from transformed names to base names
    transformed_to_base = {}
    if transformed_channel_by_promo:
        for base_name, transformed_list in transformed_channel_by_promo.items():
            for transformed_name in transformed_list:
                transformed_to_base[transformed_name] = base_name
    
    # Convert transformed channel names to base names
    channel_names = []
    for channel in transformed_channel_names:
        if channel in transformed_to_base:
            # Use the mapping if available
            base_name = transformed_to_base[channel]
        else:
            # Extract base name by removing transformation segments
            base_parts = []
            for part in channel.split('_'):
                if part not in ['Adstock', 'Root', 'Log'] and not part.isdigit():
                    base_parts.append(part)
            base_name = '_'.join(base_parts)
        channel_names.append(base_name)
    
    # Get the R² value (which is the same throughout)
    r_squared = best_result_table['Adjusted R²'].iloc[0]
    
    # Get the Modelled Sales value (which is the same throughout)
    modelled_sales = best_result_table['Modeled Sales'].iloc[0]
    
    # Get list of channels with p-value < 0.05 (excluding Intercept)
    significant_transformed_channels = best_result_table[
        (best_result_table['P-Value'] < 0.05) & 
        (best_result_table['Channel'] != 'Intercept')
    ]['Channel'].tolist()
    
    # Convert transformed channel names back to base names for significant channels
    significant_channels = []
    for channel in significant_transformed_channels:
        if channel in transformed_to_base:
            # Use the mapping if available
            base_name = transformed_to_base[channel]
        else:
            # Extract base name by removing transformation segments
            base_parts = []
            for part in channel.split('_'):
                if part not in ['Adstock', 'Root', 'Log'] and not part.isdigit():
                    base_parts.append(part)
            base_name = '_'.join(base_parts)
        significant_channels.append(base_name)
    
    return channel_names, r_squared, modelled_sales, significant_channels

def run_pipeline():
    """Main pipeline function to orchestrate the marketing mix modeling process"""
    # Load input files
    db_conn = initialize_db_connection()
    # file_path = "dummy1.xlsx"
    # spends_file_path = "spends.xlsx"
    # historical_file_path = "historical_data.xlsx"

    file_path="MMix_data_for_testing.csv"
    spends_file_path="Spends_test.xlsx"
    historical_file_path="Historical_test - Copy.xlsx"

    #Variables
    # promo_channels = ['PDE','Copay']
    # promo_channels = [col.strip() for col in promo_channels]
    # date_column = 'Date'
    # id_column = 'ID'
    # target_column = 'NRx'
    # start_date = '202411'
    # end_date = '202512'
    # date_format = 'MM-YYYY'
    # start_date = datetime.datetime.strptime(start_date, '%Y%m')
    # end_date = datetime.datetime.strptime(end_date, '%Y%m')

    # spend_column = 'Spends'
    # spend_channel_column = 'Channel'

    # historical_impact = 'contributions%'
    # historical_channel_column = 'Channel'

    promo_channels = [
   'Organic_Engagement', 'Staff_Engagement', 'Email_Engagement', 'Digital_Engagement',
    'Lead_Engagement', 'Speaker_programs', 'Doximity_views', 'ReachMD',
    'CRM_Engagement_Count', 'Event_Attendance']
 
    promo_channels = [col.strip() for col in promo_channels]
    date_column = 'Time_Period'
    id_column = 'Identifier'
    target_column ='Units'
    start_date = '202401'
    end_date = '202412'
    date_format = 'YYYY-MM'
    start_date = datetime.datetime.strptime(start_date, '%Y%m')
    end_date = datetime.datetime.strptime(end_date, '%Y%m')

    # spend_column = 'Spends'
    # spend_channel_column = 'Channel'

    spend_column = 'Spend ($)'
    spend_channel_column = 'Channel'

    # historical_impact = 'contributions%'
    # historical_channel_column = 'Channel'

    historical_impact = 'Impact_Percent_2023'
    historical_channel_column = 'Channel'

    model_run_id = "00000000-0000-0000-0000-000000000001"
    df,unit_price=  process_main_data(file_path,promo_channels,target_column,id_column,date_column,date_format,start_date,end_date,model_run_id,db_conn)
    spends_df = process_spend_data(spends_file_path,spend_column,spend_channel_column, model_run_id=None,db_conn=None)
    historical_df = process_historical_data(historical_file_path,historical_impact,historical_channel_column, model_run_id=None,db_conn=None)
    
    

    # Setup control variables
    df, control_variables = add_control_variables(df, id_column, date_column)

    params=setup_parameters(date_column, id_column, target_column, promo_channels, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact)
    params['spends_df']=spends_df
    
    # Process promo channels
    success,df, transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_transformed_features = process_promo_channels(df,promo_channels,target_column,id_column,date_column,start_date,end_date,control_variables)
  
    #Get historical benchmark
    benchmark_values = get_benchmark_values(all_transformed_features, historical_df,historical_channel_column,historical_impact)
    
    #Filter the dataframe
    df = filter_dataframe_by_date(df,date_column,start_date,end_date)
    if start_date and end_date:
            df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
    print(df)

    # Select optimal feature set
    best_features, best_metrics, all_feature_sets, all_metrics,top_5_models = select_optimal_channels(
        df,
        transformed_channels_by_promo,
        target_column,
        control_variables=control_variables,
        benchmark_values=benchmark_values
    )
    
    # Fix negative estimates in model
   
    corrected_features, fixed_channels_info = fix_negative_estimates(
        df,
        best_features,
        target_column,
        control_variables,
        date_column,
        transformed_channels_by_promo,
        all_transformed_features,
        id_column,
        start_date,
        end_date
    )

    # Update the best features for final regression
    best_features = corrected_features
    all_feature_sets[0] = corrected_features

    # Run regression analyses
    all_regression_results, best_result_table = run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics,promo_channels,spends_df,spend_column,spend_channel_column,unit_price)

    channel_count, r_squared, modelled_sales, significant_channels=get_channel_stats(best_result_table,transformed_channels_by_promo)
    print(f"Channel_count:{channel_count}")
    params['channel_count']=channel_count
    params['Significant_Channels']=significant_channels

    update_model_regression(db_conn, model_run_id, all_transformed_features, best_result_table, 
                          transformed_channels_by_promo, adstocked_channels_by_promo, 
                          adstock_range_channel, params, all_regression_results,best_features,all_feature_sets)
    
   
    print(best_result_table)
    
    close_db_connection(db_conn)
    print(f"After loading data {len(df)}")
    if df is None:
        return "Error in data loading."
   

# db_conn = initialize_db_connection()
# model_run_id = "00000000-0000-0000-0000-000000000001"
# model=extract_model_data(db_conn,model_run_id)
# params=model['parms']
# params['channel_count']

def add_roi_to_result_tables(
    result_table,
    promo_channels, 
    all_transformed_features, 
    spends_df, 
    spend_column, 
    spend_channel_column,
    target_column,
    unit_price  # Default unit price if not provided
):
    """
    Add ROI as a column to regression result tables.
    
    Args:
        all_regression_results: List of DataFrames containing regression results
        promo_channels: List of promotional channel names (list of strings)
        all_transformed_features: List of all transformed feature names (list of strings)
        spends_df: DataFrame containing channel names and their corresponding spend amounts
        spend_column: Spends column name of spends_df
        spend_channel_column: Channel name of spends_df
        target_column: Name of the target variable (string)
        unit_price: Price per unit of the target variable (default=1)
        
    Returns:
        List of DataFrames: Updated regression result tables with ROI column
    """
    import pandas as pd
    import numpy as np
    
    # Create a mapping from transformed channel names to base channel names
    transformed_to_base = {}
    for base_channel in promo_channels:
        for transformed_channel in all_transformed_features:
            # Check if the transformed channel starts with the base channel name
            if transformed_channel.startswith(base_channel + "_"):
                transformed_to_base[transformed_channel] = base_channel
        # Also map the base channel to itself
        transformed_to_base[base_channel] = base_channel
    
    # Process each result table
    updated_results = []

    if result_table is None or result_table.empty:
            updated_results.append(result_table)
         
            
        # Make a copy to avoid modifying the original
    result_df = result_table.copy()
        
        # Add ROI column
    result_df['ROI'] = np.nan
        
        # Process each row in the result table
    for idx, row in result_df.iterrows():
            channel_name = row['Channel']
            
            # Skip Intercept and target lag columns
            if channel_name == 'Intercept' or channel_name.startswith(f'Lag({target_column}'):
                continue
                
            # Find the base channel for this transformed channel
            base_channel = None
            
            # First check direct mapping
            if channel_name in transformed_to_base:
                base_channel = transformed_to_base[channel_name]
            else:
                # Try to find by prefix
                for promo in promo_channels:
                    if channel_name.startswith(promo + "_") or channel_name == promo:
                        base_channel = promo
                        break
                        
                # If still not found, try more flexible matching
                if base_channel is None:
                    for promo in promo_channels:
                        if promo in channel_name:
                            base_channel = promo
                            break
            
            if base_channel:
                # Get the spend for this channel
                try:
                    channel_spend = spends_df[spends_df[spend_channel_column] == base_channel][spend_column].values[0]
                except (IndexError, KeyError):
                    print(f"Warning: No spend data found for channel '{base_channel}'")
                    channel_spend = 0
                
                # Calculate impact (use the Impact Percentage value directly)
                impact = row['Impact Percentage'] * row['Modeled Sales'] / 100
                
                # Calculate ROI
                if channel_spend > 0:
                    roi = (impact * unit_price) / channel_spend
                    result_df.at[idx, 'ROI'] = roi
                else:
                    result_df.at[idx, 'ROI'] = np.nan
        
        
    
    return result_df

def run_regression_analyses(df,target_column,date_column,start_date,end_date,all_feature_sets,best_features,all_transformed_features,all_metrics,promo_channels,spends_df,spend_column,spend_channel_column,unit_price=1):
    """Run regression analyses for top models and save results with ROI calculation"""
    global all_regression_results, best_result_table
    print(f"best features in run_regression_analyses{best_features}")
    print(f"all_feature_sets in run_regression_analyses{all_feature_sets}")
    all_regression_results = []
    
    print("\nRunning regression analysis for top 6 models...")
    for i, feature_set in enumerate(all_feature_sets[:6]):  # Limit to top 6 models
        model_name = "Best Model" if i == 0 else f"Alternative Model {i}"
        print(f"\nRunning regression for {model_name}...")

        # Extract only promotional channels for display
        promo_features = [f for f in feature_set if f in all_transformed_features]
        promo_features_str = ', '.join(promo_features)
        print(f"Promotional channels: {promo_features_str}")

        # Perform regression
        result_table = perform_regression(df, feature_set, target_column, date_column, start_date, end_date)

        # Add model identifier
        if isinstance(result_table, pd.DataFrame) and not result_table.empty:
            try:
                # Add model columns if there's at least one row
                if len(result_table) > 0:
                    result_table['model'] = model_name
                    result_table['model_rank'] = i + 1

                all_regression_results.append(result_table)

                # Get metrics for this model
                metrics = all_metrics[i] if i < len(all_metrics) else None
                if metrics:
                    r2 = metrics['R2']
                    aic = metrics['AIC']
                    avg_p_value = metrics['avg_p_value']
                    print(f"R²: {r2:.4f}, AIC: {aic:.2f}")
                else:
                    print("Metrics not available for this model")
            except Exception as e:
                print(f"Error processing regression results: {e}")
        else:
            print(f"Error running regression for {model_name}")

    # Set the best result table
    best_result_table = all_regression_results[0] if len(all_regression_results) > 0 else None
    
    # Add ROI column to all result tables
    if len(all_regression_results) > 0:
        print("\nAdding ROI calculation to regression results...")
        for idx, result_table in enumerate(all_regression_results):
            all_regression_results[idx] = add_roi_to_result_tables(
                result_table,
                promo_channels=promo_channels,
                all_transformed_features=all_transformed_features,
                spends_df=spends_df,
                spend_column=spend_column,
                spend_channel_column=spend_channel_column,
                target_column=target_column,
                unit_price=unit_price
            )
        
        # Update best result table reference
        best_result_table = all_regression_results[0] if len(all_regression_results) > 0 else None
        
        if best_result_table is not None:
            # Display summary of best model with ROI
            marketing_channels = best_result_table[(best_result_table['Channel'] != 'Intercept') & 
                                                  (~best_result_table['Channel'].str.startswith(f'Lag({target_column}'))]
            if not marketing_channels.empty:
                print("\nBest model results with ROI:")
                print(marketing_channels[['Channel', 'Estimate', 'Impact Percentage', 'ROI']].to_string(index=False))

    return all_regression_results, best_result_table

# transformed_channels_by_promo={}
# result_df=None
# all_transformed_features=[]

# control_variables=[]
# date_column=None
# params={}
# best_result_table=None


run_pipeline()

import psycopg2
import json
import pandas as pd
import numpy as np
import datetime
import logging
from psycopg2.extras import RealDictCursor
from uuid import UUID

def extract_model_data(conn, model_run_id):
    """
    Extract ALL model data from the database for a specific model_run_id and convert it back
    to its original Python data structures.
    
    Parameters:
    conn: PostgreSQL connection object
    model_run_id: UUID of the model run to extract
    
    Returns:
    dict: Dictionary containing all the extracted model data in their original format
    """
    if not conn or conn.closed:
        logging.error("Invalid database connection")
        return None
    
    try:
        # Create a cursor that returns rows as dictionaries
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # SQL query to extract ALL data for a specific model_run_id
        query = """
        SELECT *
        FROM model_data
        WHERE model_run_id = %s
        """
        
        # Execute the query
        cursor.execute(query, (model_run_id,))
        
        # Fetch the result
        result = cursor.fetchone()
        
        if not result:
            logging.error(f"No data found for model_run_id: {model_run_id}")
            return None
            
        # Convert result to Python data structures
        model_data = {}
        
        # Process standard columns (non-JSONB types)
        for column in result:
            value = result[column]
            
            # Skip columns we'll process specially later
            if column in ['all_transformed_features', 'best_result_table', 'transformed_channels_by_promo',
                         'adstocked_channels_by_promo', 'adstock_range_channel', 'parms',
                         'model_results', 'best_features', 'all_features']:
                continue
                
            # Process special types
            if isinstance(value, UUID):
                model_data[column] = str(value)
            elif isinstance(value, list) and column == 'promotional_columns':
                # Keep array types as lists
                model_data[column] = value
            else:
                # Copy other values directly
                model_data[column] = value
        
        # Convert JSONB columns back to appropriate Python structures
        
        # Convert all_transformed_features back to list
        if result['all_transformed_features']:
            model_data['all_transformed_features'] = result['all_transformed_features']
            # If it contains more complex data, additional conversion may be needed
        
        # Convert best_result_table back to DataFrame
        if result['best_result_table']:
            if isinstance(result['best_result_table'], list):
                model_data['best_result_table'] = pd.DataFrame(result['best_result_table'])
            else:
                # Handle case where best_result_table is stored in a different format
                model_data['best_result_table'] = pd.DataFrame.from_dict(result['best_result_table'])
        
        # Convert transformed_channels_by_promo back to dict
        if result['transformed_channels_by_promo']:
            model_data['transformed_channels_by_promo'] = result['transformed_channels_by_promo']
            # Convert inner values to appropriate data structures if needed
            for key, value in model_data['transformed_channels_by_promo'].items():
                if isinstance(value, list):
                    # Check if this looks like DataFrame data
                    if value and isinstance(value[0], dict) and all(isinstance(item, dict) for item in value):
                        model_data['transformed_channels_by_promo'][key] = pd.DataFrame(value)
        
        # Convert adstocked_channels_by_promo back to dict
        if result['adstocked_channels_by_promo']:
            model_data['adstocked_channels_by_promo'] = result['adstocked_channels_by_promo']
            # Convert inner values to appropriate data structures if needed
            for key, value in model_data['adstocked_channels_by_promo'].items():
                if isinstance(value, list):
                    # Check if this looks like DataFrame data
                    if value and isinstance(value[0], dict) and all(isinstance(item, dict) for item in value):
                        model_data['adstocked_channels_by_promo'][key] = pd.DataFrame(value)
        
        # Convert adstock_range_channel back to dict
        if result['adstock_range_channel']:
            model_data['adstock_range_channel'] = result['adstock_range_channel']
        
        # Convert parms back to dict
        if result['parms']:
            model_data['parms'] = result['parms']
            
            # Convert date strings back to datetime objects if needed
            for key, value in model_data['parms'].items():
                if isinstance(value, str) and key.lower().endswith('date'):
                    try:
                        model_data['parms'][key] = pd.to_datetime(value)
                    except:
                        # Keep as string if conversion fails
                        pass
        
        # Convert model_results back to list of DataFrames
        if result['model_results']:
            if isinstance(result['model_results'], list):
                # If it's a list of records that look like DataFrames
                model_results = []
                for item in result['model_results']:
                    if isinstance(item, list):
                        model_results.append(pd.DataFrame(item))
                    elif isinstance(item, dict):
                        # Handle different storage formats
                        model_results.append(pd.DataFrame.from_dict(item))
                model_data['model_results'] = model_results
            else:
                # If it's stored in a different format
                model_data['model_results'] = result['model_results']
        
        # Convert best_features back to list
        if result['best_features']:
            model_data['best_features'] = result['best_features']
            # Convert to appropriate format if needed
            if isinstance(model_data['best_features'], dict) and 'series' in model_data['best_features']:
                try:
                    model_data['best_features'] = pd.Series(model_data['best_features']['series'])
                except:
                    pass
        
        # Convert all_features back to list
        if result['all_features']:
            model_data['all_features'] = result['all_features']
            # Convert nested structures if needed
            if isinstance(model_data['all_features'], list):
                # If it's a list of feature sets, each potentially being a DataFrame
                for i, feature_set in enumerate(model_data['all_features']):
                    if isinstance(feature_set, list) and feature_set and isinstance(feature_set[0], dict):
                        model_data['all_features'][i] = pd.DataFrame(feature_set)
        
        return model_data
    
    except Exception as e:
        logging.error(f"Error extracting data from database: {e}")
        return None
    finally:
        # Close the cursor if it exists
        if 'cursor' in locals() and cursor is not None:
            cursor.close()

# Example usage:
# conn = psycopg2.connect("dbname=mydb user=user password=pass host=localhost")
# model_data = extract_model_data(conn, "123e4567-e89b-12d3-a456-426614174000")
# 
# # Access all data including standard columns
# date_column = model_data['date_column']
# id_column = model_data['id_column']
# target_column = model_data['target_column']
# promotional_columns = model_data['promotional_columns']
# start_date = model_data['start_date']
# end_date = model_data['end_date']
# 
# # Access complex columns
# best_result_table = model_data['best_result_table']
# best_features = model_data['best_features']
# all_features = model_data['all_features']

def update_multiple_channel_transformations(df,id_column,best_features, channel_updates):
    """
    Update multiple channel transformations and return updated feature list for regression.
    
    Args:
        best_result_table (pd.DataFrame): The current best result table
        channel_updates (list): List of (base_channel, new_adstock_rate, new_transform_name) tuples
    
    Returns:
        list: List of features for perform_regression, with updated transformed features
    """
    
    # Create a copy of the best_features to update
    updated_features = best_features.copy()
    
    print(f"Starting multiple channel updates. Initial features: {len(best_features)}")
    
    for base_channel, new_adstock_rate, new_transform_name in channel_updates:
        # Identify the current feature used for this base channel in updated_features
        current_feature = None
        for feature in updated_features:
            if feature.startswith(f"{base_channel}_Adstock_"):
                current_feature = feature
                break
        
        if current_feature is None:
            print(f"Warning: No feature for {base_channel} found in features list. Skipping.")
            continue
        
        print(f"\nUpdating channel: {base_channel}")
        print(f"Current feature: {current_feature}")
        
        # Create the new channel name
        new_feature_name = f"{base_channel}_Adstock_{new_adstock_rate}_{new_transform_name}"
        print(f"New feature name will be: {new_feature_name}")
        
        # Check if the base channel exists in the dataframe
        if base_channel not in df.columns:
            print(f"Warning: Base channel {base_channel} not found in dataframe. Skipping.")
            continue
        
        # Apply new adstock
        print(f"Applying adstock with rate {new_adstock_rate}...")
        try:
            adstock_col, adstocked_series = apply_adstock(
                df, 
                base_channel, 
                id_column,  # Assuming id_column is globally available
                new_adstock_rate
            )
            print(f"Created adstock column: {adstock_col}")
            
            # Apply new transformation
            transform_funcs = get_transformation_functions()
            if new_transform_name not in transform_funcs:
                print(f"Warning: Transformation {new_transform_name} not found. Skipping.")
                continue
            
            print(f"Applying transformation: {new_transform_name}...")
            transform_func = transform_funcs[new_transform_name]
            transformed_col = new_feature_name
            _, transformed_series = apply_transformation(
                adstocked_series,
                new_transform_name,
                transform_func,
                adstock_col
            )
            print(f"Created transformed column: {transformed_col}")
            
            # Add the transformed data to the dataframe
            df[transformed_col] = transformed_series
            print(f"Added {transformed_col} to dataframe")
            
            # Update feature list by replacing the current feature with the new one
            feature_index = updated_features.index(current_feature)
            updated_features[feature_index] = new_feature_name
            print(f"Updated features: Replaced {current_feature} with {new_feature_name}")
            
        except Exception as e:
            print(f"Error updating {base_channel}: {e}")
            print(f"Skipping this channel and continuing with others.")
    
    print(f"\nMultiple channel updates complete. Total features in updated list: {len(updated_features)}")
    return df,updated_features





# def process_and_filter_main_data(file_path, promo_channels, date_column, id_column, target_column, start_date, end_date, date_format):
#     """
#     Clean and process the main data and update the PostgreSQL database directly.
    
#     Args:
#         file_path (str): Path to the input file
#         promo_channels (list): List of promotional channel columns
#         date_column (str): Name of the date column
#         id_column (str): Name of the ID column
#         target_column (str): Name of the target column
#         start_date (datetime.date): Start date for filtering
#         end_date (datetime.date): End date for filtering
#         date_format (str): Format of dates in the original data
        
#     Returns:
#         pandas.DataFrame: Processed main dataframe
#     """

#     import pandas as pd
#     import numpy as np
#     import logging
    
#     try:
#         # Load the data
#         df = load_file(file_path)
#         df['original_date'] = df[date_column].copy()
        
#         # Convert dates
#         df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))
#         print(type(date_column))  # This will print <class 'str'>

#         # Print debugging info about the conversion success
#         na_count = df[date_column].isna().sum()
#         total_rows = len(df)
#         print(f"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted")

#         if na_count > 0:
#             # Show examples of problematic values
#             problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]
#             print(f"Examples of problematic date values: {problem_examples}")

#         # Drop the empty dates
#         orig_len = len(df)
#         df = df.dropna(subset=[date_column])
#         print(f"Dropped {orig_len - len(df)} rows with invalid dates")

#         # Drop the debug column
#         df = df.drop(columns=['original_date'])
        
#         # Ensure numeric columns
#         numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
#         for col in numeric_columns:
#             df[col] = pd.to_numeric(df[col], errors='coerce')
#         df = df.drop_duplicates()
#         logging.info("Data successfully loaded and cleaned.")
        
#         # Create lag variable
#         df = df.sort_values(by=[id_column, date_column])
#         df[f"{target_column}_lag_1"] = df.groupby(id_column)[target_column].shift(1).fillna(0)
        
#         # Handle outliers
#         df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)

#         # Convert start_date and end_date to pandas datetime64 for correct comparison
        
        
#         # Filter the dataframe by date range
#         df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
        
#         print(f"Type of df[date_column]: {type(df[date_column].iloc[0]) if not df.empty else 'No data'}")
        
        
#         return df
    
#     except Exception as e:
#         logging.error(f"Error processing main data: {str(e)}")
#         raise


def process_and_filter_main_data(file_path, promo_channels, date_column, id_column, target_column, 
                        start_date, end_date, date_format, best_features=None):
    """
    Clean and process the main data and update the PostgreSQL database directly.
    Also creates all necessary transformed features specified in best_features.
    
    Args:
        file_path (str): Path to the input file
        promo_channels (list): List of promotional channel columns
        date_column (str): Name of the date column
        id_column (str): Name of the ID column
        target_column (str): Name of the target column
        start_date (datetime.date): Start date for filtering
        end_date (datetime.date): End date for filtering
        date_format (str): Format of dates in the original data
        best_features (list, optional): List of features needed for the model. 
                                        If provided, ensures all these features are created.
        
    Returns:
        pandas.DataFrame: Processed main dataframe with all required features
    """
    import pandas as pd
    import numpy as np
    import logging
    import re
    
    try:
        # Load the data
        df = load_file(file_path)
        df['original_date'] = df[date_column].copy()
        
        # Convert dates
        df[date_column] = df[date_column].apply(lambda x: convert_date(x, date_format))
        print(type(date_column))  # This will print <class 'str'>

        # Print debugging info about the conversion success
        na_count = df[date_column].isna().sum()
        total_rows = len(df)
        print(f"Date conversion: {total_rows-na_count}/{total_rows} dates successfully converted")

        if na_count > 0:
            # Show examples of problematic values
            problem_examples = df[df[date_column].isna()]['original_date'].unique()[:5]
            print(f"Examples of problematic date values: {problem_examples}")

        # Drop the empty dates
        orig_len = len(df)
        df = df.dropna(subset=[date_column])
        print(f"Dropped {orig_len - len(df)} rows with invalid dates")

        # Drop the debug column
        df = df.drop(columns=['original_date'])
        
        # Ensure numeric columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df = df.drop_duplicates()
        logging.info("Data successfully loaded and cleaned.")
        
        # Create lag variable
        df = df.sort_values(by=[id_column, date_column])
        df[f"{target_column}_lag_1"] = df.groupby(id_column)[target_column].shift(1).fillna(0)
        
        # Handle outliers
        df = handle_outliers(df, promo_channels, target_column, num_sigmas=3)
        
        # Filter the dataframe by date range
        df = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
        
        print(f"Type of df[date_column]: {type(df[date_column].iloc[0]) if not df.empty else 'No data'}")
        
        # Create all necessary transformed features if best_features is provided
        if best_features:
            print(f"Ensuring all required features are created: {best_features}")
            # Regular expression pattern to extract base channel, adstock rate, and transformation
            pattern = r'([A-Za-z0-9_]+)_Adstock_(\d+)_([A-Za-z0-9]+)'
            
            for feature in best_features:
                # Skip features that are already there or don't match the pattern
                if feature in df.columns or not re.match(pattern, feature):
                    continue
                
                # Extract components
                match = re.match(pattern, feature)
                if match:
                    base_channel, adstock_rate, transform_name = match.groups()
                    adstock_rate = int(adstock_rate)
                    
                    print(f"Creating feature: {feature} from base channel {base_channel}")
                    
                    # Make sure base channel exists
                    if base_channel not in df.columns:
                        print(f"Warning: Base channel {base_channel} not found in dataframe. Skipping.")
                        continue
                    
                    # Apply adstock
                    try:
                        adstock_col, adstocked_series = apply_adstock(
                            df, 
                            base_channel, 
                            id_column,
                            adstock_rate
                        )
                        
                        # Apply transformation
                        transform_funcs = get_transformation_functions()
                        if transform_name not in transform_funcs:
                            print(f"Warning: Transformation {transform_name} not found. Skipping.")
                            continue
                        
                        transform_func = transform_funcs[transform_name]
                        _, transformed_series = apply_transformation(
                            adstocked_series,
                            transform_name,
                            transform_func,
                            adstock_col
                        )
                        
                        # Add the transformed data to the dataframe
                        df[feature] = transformed_series
                        print(f"Successfully created feature: {feature}")
                        
                    except Exception as e:
                        print(f"Error creating {feature}: {e}")
            
        return df
    
    except Exception as e:
        logging.error(f"Error processing main data: {str(e)}")
        raise

import psycopg2
import json
from psycopg2.extras import Json
import pandas as pd
import datetime
import logging

def update_model_specific_fields(conn, model_run_id, best_result_table=None, all_regression_results=None, 
                                best_features=None, all_feature_sets=None):
    """
    Update specific JSONB fields in the model_data table for a specific model_run_id.
    Only updates the fields that are provided (not None).
    
    Parameters:
    conn: PostgreSQL connection object
    model_run_id: UUID of the model run to update
    best_result_table: DataFrame (will be converted to JSON)
    all_regression_results: List of regression results
    best_features: List of best features
    all_feature_sets: List of all feature sets (will be stored as all_features in database)
    
    Returns:
    bool: True if update was successful, False otherwise
    """
    if not conn or conn.closed:
        logging.error("Invalid database connection")
        return False
    
    # Helper function to safely convert pandas DataFrames to serializable dictionaries
    def convert_pandas_to_dict(obj):
        if isinstance(obj, pd.DataFrame):
            # Convert DataFrame to dict with records orientation and convert datetime columns to ISO format
            for col in obj.select_dtypes(include=['datetime64']).columns:
                obj[col] = obj[col].astype(str)
            return json.loads(obj.to_json(orient='records', date_format='iso'))
        elif isinstance(obj, pd.Series):
            # Handle datetime Series
            if pd.api.types.is_datetime64_any_dtype(obj):
                obj = obj.astype(str)
            return json.loads(obj.to_json())
        elif isinstance(obj, dict):
            # Handle dictionaries - recursively process all values
            return {k: convert_pandas_to_dict(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            # Handle lists - recursively process all items
            return [convert_pandas_to_dict(item) for item in obj]
        elif isinstance(obj, (datetime.datetime, datetime.date)):
            # Convert datetime objects to ISO format string
            return obj.isoformat()
        else:
            return obj
    
    try:
        # Build dynamic SQL query based on provided parameters
        update_fields = []
        params = []
        
        # Process best_result_table if provided
        if best_result_table is not None:
            serialized_best_result_table = convert_pandas_to_dict(best_result_table)
            update_fields.append("best_result_table = %s")
            params.append(Json(serialized_best_result_table))
        
        # Process all_regression_results if provided
        if all_regression_results is not None:
            serialized_regression_results = convert_pandas_to_dict(all_regression_results)
            update_fields.append("model_results = %s")
            params.append(Json(serialized_regression_results))
        
        # Process best_features if provided
        if best_features is not None:
            serialized_best_features = convert_pandas_to_dict(best_features)
            update_fields.append("best_features = %s")
            params.append(Json(serialized_best_features))
        
        # Process all_feature_sets if provided (stored as all_features in database)
        if all_feature_sets is not None:
            serialized_all_feature_sets = convert_pandas_to_dict(all_feature_sets)
            update_fields.append("all_features = %s")
            params.append(Json(serialized_all_feature_sets))
        
        # Add updated_at timestamp
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        
        # If no fields to update, return early
        if not update_fields:
            logging.warning("No fields provided for update")
            return False
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Build the SQL query
        update_query = f"""
        UPDATE model_data
        SET {', '.join(update_fields)}
        WHERE model_run_id = %s
        """
        
        # Add the model_run_id to params
        params.append(model_run_id)
        
        # Execute the query with parameters
        cursor.execute(update_query, params)
        
        # Commit the transaction
        conn.commit()
        
        # Check if any rows were updated
        rows_updated = cursor.rowcount
        if rows_updated > 0:
            print(f"Successfully updated {rows_updated} row(s) for model_run_id: {model_run_id}")
            return True
        else:
            print(f"No rows were updated for model_run_id: {model_run_id}")
            return False
    
    except Exception as e:
        print(f"Error updating database: {e}")
        if conn:
            conn.rollback()
        return False
    
    finally:
        # Close the cursor if it exists
        if 'cursor' in locals() and cursor is not None:
            cursor.close()



def update_channels():
    
    db_conn = initialize_db_connection()
    if db_conn is None:
        print("Error: Invalid database connection")
        return False
        
    model_run_id = "00000000-0000-0000-0000-000000000001"

    # Changed conn to db_conn here
    model_data = extract_model_data(db_conn, model_run_id)
    
    all_features = model_data['all_features']
    best_result_table = model_data['best_result_table']
    best_features = model_data['best_features']
    all_regression_results = model_data['model_results']
    date_column = model_data['date_column']
    id_column = model_data['id_column']
    target_column = model_data['target_column']
    promotional_columns = model_data['promotional_columns']
    start_date = model_data['start_date']
    end_date = model_data['end_date']
    date_format = model_data['date_format']
    unit_price=float(model_data['unit_price'])
    all_transformed_features=model_data['all_transformed_features']
    spend_column=model_data['spend_column']
    spend_channel_column=model_data['spend_channel_column']

    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)

    # file_path = '../dummy1.xlsx'
    file_path="MMix_data_for_testing.csv"

    spend_file_path="Spends_test.xlsx"
    spends_df=load_file(spend_file_path)



    df = process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date, date_format,best_features)
    
    channel_updates = [
        ('Organic_Engagement', 10, 'Log')
    ]

    df, updated_features = update_multiple_channel_transformations(
        df, id_column, best_features, channel_updates
    )
                    
    # Run regression again with the updated features
    print("\nRunning regression with updated channel transformations...")
                    
    # Update best_features with the new feature list
    best_features = updated_features
    print(f"best features: {best_features}")
    
    all_features[0] = best_features
                    
    # Perform regression with updated feature set
    updated_result_table = perform_regression(
        df, 
        updated_features, 
        target_column, 
        date_column, 
        start_date, 
        end_date
    )
    updated_result_table['model'] = 'Best Model'
    updated_result_table['model_rank'] = 1
    
    updated_result_table=add_roi_to_result_tables(
                updated_result_table,
                promotional_columns,
                all_transformed_features,
                spends_df,
                spend_column,
                spend_channel_column,
                target_column,
                unit_price
            )
    
                    
    # Update best_result_table with the new results
    if isinstance(updated_result_table, pd.DataFrame) and not updated_result_table.empty:
        best_result_table = updated_result_table
                        
        # Also update in all_regression_results if it exists
        if all_regression_results and len(all_regression_results) > 0:
            all_regression_results[0] = best_result_table
    
    print(f"best result table: {best_result_table}")
    
    update_successful = update_model_specific_fields(
        db_conn,
        model_run_id,
        best_result_table=best_result_table,
        all_regression_results=all_regression_results,
        best_features=best_features,
        all_feature_sets=all_features
    )
    
    close_db_connection(db_conn)
    return update_successful

update_channels()

def result_extraction():
    db_conn=initialize_db_connection()
    model_run_id="00000000-0000-0000-0000-000000000001"


    # load features from the database
    model_data = extract_model_data(db_conn, model_run_id)
    all_transformed_features=model_data['all_transformed_features']
    transformed_channels_by_promo=model_data['transformed_channels_by_promo']
    adstocked_channels_by_promo=model_data['adstocked_channels_by_promo']
    adstock_range_channel=model_data['adstock_range_channel']
    all_features=model_data['all_features']
    best_result_table = model_data['best_result_table']
    best_features = model_data['best_features']
    all_regression_results=model_data['model_results']
    date_column = model_data['date_column']
    id_column = model_data['id_column']
    target_column = model_data['target_column']
    promotional_columns = model_data['promotional_columns']
    start_date = model_data['start_date']
    end_date = model_data['end_date']
    date_format = model_data['date_format']
    spend_channel_column = model_data['spend_channel_column']
    spend_column = model_data['spend_column']
    historical_channel_column = model_data['historical_channel_column']
    historical_impact = model_data['historical_impact']
    unit_price = model_data['unit_price']
    params=model_data['parms']

    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    
    
    return target_column, date_column, id_column, start_date, end_date, spend_channel_column,spend_column,historical_channel_column,historical_impact,unit_price,best_result_table,all_regression_results,transformed_channels_by_promo,adstocked_channels_by_promo,adstock_range_channel,all_features,all_transformed_features,promotional_columns,best_features,date_format,params
   

def execute_code_with_retries(code_to_execute, local_vars, max_attempts=6):
    """
    Attempts to execute the given code multiple times until success or max attempts reached.
    Now properly handles matplotlib figures.
    
    Args:
        code_to_execute: String containing Python code to execute
        local_vars: Dictionary of local variables for execution context
        max_attempts: Maximum number of attempts to try executing the code
        
    Returns:
        tuple: (success flag, output text, matplotlib figure or None, error message if any)
    """
    import matplotlib.pyplot as plt
    import io
    from contextlib import redirect_stdout
    import traceback
    
    for attempt in range(1, max_attempts + 1):
        print(f"\nAttempt {attempt}/{max_attempts}...")
        
        # Clear any existing figures before execution
        plt.close('all')
        
        # Create a string buffer to capture output
        output_buffer = io.StringIO()
        error_message = None
        success = False
        figure = None
        
        # Make a copy of the local variables to prevent pollution between attempts
        local_vars_copy = {k: v for k, v in local_vars.items()}
        
        try:
            # Redirect stdout to capture print statements
            with redirect_stdout(output_buffer):
                # Execute the code
                exec(code_to_execute, globals(), local_vars_copy)
            
            # CRITICAL FIX: Check for figures AFTER execution
            if plt.get_fignums():
                # Get the current figure (last created figure)
                figure = plt.gcf()
                print(f"Figure created with {len(plt.get_fignums())} total figures")
                
                # IMPORTANT: Don't close the figure here - we need to return it
                # The figure will be closed by the calling function after saving
            else:
                print("No matplotlib figures were created")
            
            # Check if there's any output and if it contains error messages
            output_text = output_buffer.getvalue()
            if "An error occurred:" in output_text or "Error:" in output_text:
                error_message = output_text
                raise Exception(f"Execution produced an error: {output_text}")
            
            # If we get here, execution was successful
            success = True
            return success, output_text, figure, None
                
        except Exception as e:
            error_message = f"Error: {str(e)}\n{traceback.format_exc()}"
            # Clear any figures that might have been created during failed execution
            plt.close('all')
        
        # If we're here, this attempt failed
        if attempt < max_attempts:
            print(f"Attempt {attempt} failed: {error_message}")
            print("Trying again with a slightly modified approach...")
            
            # For next attempt, you would regenerate code here (assuming model is available)
            # This part would need to be implemented based on your Gemini model setup
        else:
            print(f"All {max_attempts} attempts failed.")
            return False, "", None, error_message
    
    # If we reach here, all attempts failed
    return False, "", None, "Maximum attempts reached without success."


def interactive_mmm_chatbot(user_question, df, target_column, date_column, promo_channels, all_transformed_features, best_result_table, params, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel, spends_df, spend_channel_column, spend_column, historical_df, historical_channel_column, historical_impact, unit_price):
    """
    A non-interactive version of the MMM chatbot that processes a single user question
    and returns the appropriate response (text, image, or both).
    """
    import pandas as pd
    import matplotlib.pyplot as plt
    import seaborn as sns
    import numpy as np
    import json
    import logging
    from datetime import datetime
    import matplotlib.dates as mdates
    from statsmodels.stats.outliers_influence import variance_inflation_factor
    from matplotlib_venn import venn2
    import sys
    import io
    from contextlib import redirect_stdout
    import traceback
    import base64
    from io import BytesIO
    
    # Clear any existing figures at the start
    plt.close('all')
    
    # Set matplotlib backend to Agg for non-interactive environments
    plt.switch_backend('Agg')
    
    knowledge_base = """
      These are some of the functions that are commonly used in marketing mix to answer user questions
    def month_on_month_summary(df: pd.DataFrame, date_column: str, target_column: str, promo_columns: list):

        logging.info("Generating month-on-month summary visualization.")
        df_filtered=df.copy(deep=True)
        # Convert date column to datetime format
        df_filtered[date_column] = pd.to_datetime(df_filtered[date_column])

        # Filter the DataFrame based on the date range
        # df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
        df_grouped = df_filtered.groupby(date_column).sum()

        plt.figure(figsize=(12, 6))

        # Plot promotional columns as line plots
        sns.lineplot(data=df_grouped[promo_columns], palette='tab10', linewidth=2)

        # Plot target column as a filled area plot
        plt.fill_between(df_grouped.index, df_grouped[target_column], color='gray', alpha=0.3, label=target_column)

        # Format the x-axis with readable date formatting
        plt.gca().xaxis.set_major_locator(mdates.MonthLocator())  # Show every month
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))  # Format as "Month Year"
        plt.xticks(rotation=45)  # Rotate the labels for better readability

        plt.title("Month-on-Month Summary")
        plt.xlabel("Time Period")
        plt.ylabel("Value")
        plt.legend()
        plt.tight_layout()  # Adjust layout to fit rotated labels
        plt.show(block=False)
        plt.pause(0.1)

    Correlation summary
    def correlation_summary(df, promo_channels, target_column, date_column=None, start_date=None, end_date=None):
  
    import pandas as pd
    import seaborn as sns
    import matplotlib.pyplot as plt
    import traceback
    
    try:
        # Find which promo channels exist in the dataframe
        available_promo_cols = []
        for promo in promo_channels:
            if promo in df.columns:
                available_promo_cols.append(promo)
        
        print(f"Found {len(available_promo_cols)} promotional channels in the dataframe: {available_promo_cols}")
        
        if not available_promo_cols:
            print("No promotional channels found in the DataFrame.")
            print("Available columns: ", df.columns.tolist())
            return
        
        # Calculate correlation matrix with available promo columns and target
        correlation_data = df[available_promo_cols + [target_column]].corr(method='pearson')
        
        # Plot the heatmap for correlations
        plt.figure(figsize=(10, 6))
        sns.heatmap(correlation_data, annot=True, cmap="coolwarm", fmt=".2f", linewidths=0.5)
        plt.title(f"Correlation of Marketing Channels with {target_column}")
        plt.tight_layout()
        plt.show()
        
        print("Correlation heatmap displayed. Check for strong positive or negative correlations with the target variable.")
        
    except Exception as e:
        print(f"An error occurred during correlation analysis: {str(e)}")
        traceback.print_exc()
    Aggregate channel summary
    def aggregate_channel_summary(df: pd.DataFrame, id_column: str, promo_columns: list, target_column: str):

        Compute total, min, max, avg values for each channel, along with reach and frequency.

        logging.info("Computing aggregated channel summary.")
        summary = df[promo_columns + [target_column]].agg(['sum', 'min', 'max', 'mean'])

        reach = {col: df[df[col] > 0][id_column].nunique() for col in promo_columns}
        frequency = {col: summary.loc['sum', col] / reach[col] if reach[col] > 0 else 0 for col in promo_columns}

        reach_df = pd.DataFrame.from_dict(reach, orient='index', columns=['Reach'])
        frequency_df = pd.DataFrame.from_dict(frequency, orient='index', columns=['Frequency'])

        summary = pd.concat([summary, reach_df.T, frequency_df.T])
        summary= summary.reset_index()
        summary.set_index('index', inplace=True)
        print("Aggregated Channel Summary with Reach and Frequency:")
        print(summary)
    
    Tier Based summary
    def tier_based_summary(df: pd.DataFrame, tier_column: str, promo_columns: list, target_column: str):

        Compute total, min, max, avg values for each channel at the tier level, along with reach and frequency.

        logging.info("Computing tier-based aggregated summary.")
        tier_summary = df.groupby(tier_column).agg({col: ['sum', 'min', 'max', 'mean'] for col in promo_columns + [target_column]})
        tier_reach = df.groupby(tier_column)[promo_columns].apply(lambda x: x.gt(0).sum())

        tier_reach.index = tier_reach.index.astype(str)  # Convert to string for consistency
        tier_summary.index = tier_summary.index.astype(str)

        tier_frequency = tier_summary.loc[:, (slice(None), 'sum')].div(tier_reach.replace(0, 1))
        tier_frequency.columns = pd.MultiIndex.from_tuples([(col, 'frequency') for col in promo_columns])

        tier_summary = pd.concat([tier_summary, tier_reach.add_suffix('_reach'), tier_frequency], axis=1)

        print("Tier-Based Aggregated Channel Summary with Reach and Frequency:")
        print(tier_summary.reset_index())

    Overlap_summary
    def overlap_summary(df: pd.DataFrame, id_column: str, target_column: str, promo_columns: str, date_column: str, start_date: str, end_date: str):

        Generate a Venn diagram and summary table showing overlap between sales and selected promotion at ID level.

        logging.info("Generating overlap summary.")
        df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]
        for promo_column in promo_columns:
            df_filtered[promo_column] = df_filtered[promo_column].fillna(0)
            df_grouped = df_filtered.groupby(id_column).agg({target_column: 'sum', promo_column: 'sum'}).reset_index()
            df_grouped['promo_flag'] = df_grouped[promo_column] > 0
            df_grouped['sales_flag'] = df_grouped[target_column] > 0

            both = df_grouped[df_grouped['promo_flag'] & df_grouped['sales_flag']][id_column].nunique()
            only_sales = df_grouped[~df_grouped['promo_flag'] & df_grouped['sales_flag']][id_column].nunique()
            only_promo = df_grouped[df_grouped['promo_flag'] & ~df_grouped['sales_flag']][id_column].nunique()
            neither = df_grouped[~df_grouped['promo_flag'] & ~df_grouped['sales_flag']][id_column].nunique()

            plt.figure(figsize=(6, 6))
            venn2(subsets=(only_sales, only_promo, both), set_labels=("Sales", promo_column))
            plt.title("Overlap Summary")
            plt.show(block=False)
            plt.pause(0.1)


            summary_table = pd.DataFrame({
                "Category": ["Sales & Promotion", "Sales Only", "Promotion Only", "Neither"],
                "Unique IDs": [both, only_sales, only_promo, neither],
                "Total Sales": [df_grouped.loc[df_grouped['sales_flag'], target_column].sum()] * 4,
                "Total Promotional Volume": [df_grouped.loc[df_grouped['promo_flag'], promo_column].sum()] * 4
            })
            print("Overlap Summary Table:")
            print(summary_table)

    Box plot summaru generation
    def box_plot_summary(df: pd.DataFrame, target_column: str, promo_columns: list, date_column: str, start_date: str, end_date: str):

        Generate box plots for the target variable and promotional variables over time.

        logging.info("Generating box plot summaries.")
        df_filtered = df[(df[date_column] >= start_date) & (df[date_column] <= end_date)]

        # selected_promo_columns = input("Enter promotional variable columns to include in box plots (comma-separated): ").split(',')

        plt.figure(figsize=(12, 6))
        sns.boxplot(x=date_column, y=target_column, data=df_filtered)
        plt.xticks(rotation=45)
        plt.title(f"Box Plot of {target_column} Over Time")
        plt.show(block=False)
        plt.pause(0.1)


        for promo in promo_columns:
            if promo in df_filtered.columns:
                plt.figure(figsize=(12, 6))
                sns.boxplot(x=date_column, y=promo, data=df_filtered)
                plt.xticks(rotation=45)
                plt.title(f"Box Plot of {promo} Over Time")
                plt.show(block=False)
                plt.pause(0.1)
    
                


EXAMPLE code pattern for filtering out non-marketing variables
# Example code pattern for filtering out non-marketing variables
def get_marketing_variables_only(best_result_table, promo_channels):
     marketing_rows = []
    
    for _, row in best_result_table.iterrows():
        var_name = row.get('Channel', '')
        # Check if this is a legitimate marketing variable
        is_marketing_var = False
        
        # Check if the variable is derived from any promo channel
        for channel in promo_channels:
            if var_name.startswith(channel + '_') or var_name == channel:
                is_marketing_var = True
                break
        
        # Explicitly exclude non-marketing variables
        if (var_name == 'Intercept' or 
            var_name.startswith('T') or 
            'time' in var_name.lower() or 
            'trend' in var_name.lower() or 
            'Rtime' in var_name or
            'season' in var_name.lower() or
            'month' in var_name.lower() or
            'week' in var_name.lower() or
            'quarter' in var_name.lower() or
            '_lag_' in var_name.lower() or  # Exclude variables with '_lag_' pattern
            (var_name.lower().endswith('lag') or  # Ends with 'lag'
             any(var_name.lower().endswith(f'lag_{i}') for i in range(10)))):
            is_marketing_var = False
            
        if is_marketing_var:
            marketing_rows.append(row)
    
    return pd.DataFrame(marketing_rows)

# Then use this when answering questions about top marketing drivers
marketing_only_table = get_marketing_variables_only(best_result_table, promo_channels)
top_n_drivers = marketing_only_table.sort_values('Impact Percentage', ascending=False).head(3)
    """

    # Verify that Gemini API is available
    try:
        # import genai  # Assuming this is your Gemini import
        model = genai.GenerativeModel("gemini-2.0-flash")
        print("Gemini API initialized successfully.")
    except Exception as e:
        return {
            'text_output': f"Error: Could not initialize Gemini API: {e}. Please check your API key configuration.",
            'image': None,
            'output_type': 'text'
        }

    # Ensure the date column is in datetime format
    if df[date_column].dtype != 'datetime64[ns]':
        try:
            df[date_column] = pd.to_datetime(df[date_column])
            print(f"Converted {date_column} to datetime format.")
        except Exception as e:
            print(f"Warning: Could not convert {date_column} to datetime format: {e}")

    # Generate column summaries for Gemini context
    column_summaries = {}
    for col in df.columns:
        if col in [date_column, target_column] + promo_channels:
            try:
                summary = {
                    "min": float(df[col].min()),
                    "max": float(df[col].max()),
                    "mean": float(df[col].mean()),
                    "median": float(df[col].median())
                }
                column_summaries[col] = summary
            except:
                pass

    # Extract model coefficients and other important info
    model_coefficients = {}
    channel_impacts = {}
    channel_effectiveness = {}
    best_result_columns = []
    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:
        best_result_columns = list(best_result_table.columns)

    if isinstance(best_result_table, pd.DataFrame) and not best_result_table.empty:
        try:
            for _, row in best_result_table.iterrows():
                channel = row.get('Channel', '')
                if channel:
                    model_coefficients[channel] = row.get('Estimate', 0)
                    channel_impacts[channel] = row.get('Impact Percentage', 0)
                    channel_effectiveness[channel] = row.get('Effectiveness', 0)
        except Exception as e:
            print(f"Warning: Could not extract model details: {e}")

    # Analyze question type
    question_needs_viz = any(kw in user_question.lower() for kw in [
        'show', 'graph', 'plot', 'chart', 'visualize', 'trend', 'compare', 'timeline',
        'over time', 'pattern', 'correlation'
    ])

    # Create context for Gemini
    context = f"""
    I'm a marketing mix modeling chatbot generating Python code to answer questions about a marketing mix model.

USER QUESTION: "{user_question}"

CRITICAL PLOTTING REQUIREMENTS:
1. ALWAYS use plt.figure(figsize=(12, 8)) to create a new figure before any plotting
2. NEVER use plt.show(block=False) or plt.pause() - these don't work in non-interactive environments
3. ALWAYS end plotting code with just plt.show() - this is CRITICAL for figure capture
4. Use plt.tight_layout() before plt.show() to ensure proper spacing
5. For multiple subplots, use plt.subplots() and ensure all are properly formatted
6. ALWAYS include proper titles, labels, and legends for all plots

EXAMPLE PLOTTING PATTERN:
```python
import matplotlib.pyplot as plt
import seaborn as sns

# Create figure
plt.figure(figsize=(12, 8))

# Your plotting code here
# ... plotting operations ...

# Format the plot
plt.title("Your Title Here")
plt.xlabel("X Label")
plt.ylabel("Y Label")
plt.legend()
plt.tight_layout()

# CRITICAL: End with plt.show() - this is required for figure capture
plt.show()
```

I'm a marketing mix modeling chatbot generating Python code to answer questions about a marketing mix model.

USER QUESTION: "{user_question}"
KNOWLEDGE BASE OF PRE-DEFINED FUNCTIONS:
IMPORTANT IMPLEMENTATION NOTES:
1. The knowledge base contains EXAMPLE functions only - they are NOT actually implemented in the runtime environment.
2. You MUST generate COMPLETE, SELF-CONTAINED code that includes ALL necessary imports and function definitions.
3. Even if a similar function exists in the knowledge base, you must re-implement it fully in your response.
4. DO NOT reference or call any functions from the knowledge base directly - they don't exist at runtime.
5. Include explicit imports for all libraries used (matplotlib.pyplot as plt, seaborn as sns, etc.) at the beginning of your code.
6. All visualization code must include proper exception handling and end with plt.show() to display results.
7. Ensure your code uses ONLY the variables provided in the execution context:
  - df, target_column, date_column, promo_channels, all_transformed_features, best_result_table,
  - params, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel,
  - model_coefficients, channel_impacts, channel_effectiveness, spends_df
8. You can use any standard Python libraries that are already imported in the main function.
9. The knowledge base functions are for REFERENCE ONLY to understand the expected style and approach.
{knowledge_base}
## CRITICAL: VARIABLE ACCESS INSTRUCTIONS
Your code will be executed in an environment where all variables are available, but you MUST:
1. First verify the existence of ALL variables before using them
2. Use ONLY the local variables already defined in the execution environment
3. DO NOT try to import or redefine these variables
4. ALWAYS check if dictionaries like channel_impacts exist before accessing them

## CRITICAL: VARIABLE VERIFICATION CODE PATTERN
ALWAYS include this pattern at the beginning of your code:

```python
# First verify all required variables exist
required_vars = ['df', 'target_column', 'date_column', 'promo_channels', 
                 'channel_impacts', 'model_coefficients', 'best_result_table',
                 'transformed_channels_by_promo', 'adstocked_channels_by_promo',
                 'spends_df', 'spend_column', 'spend_channel_column']

missing_vars = []
for var_name in required_vars:
    if var_name not in locals() and var_name not in globals():
        missing_vars.append(var_name)
        
if missing_vars:
    print(f"Error: Missing required variables: {{', '.join(missing_vars)}}")
    # Create empty placeholders for missing variables to prevent further errors
    for var_name in missing_vars:
        if var_name == 'df' or var_name.endswith('_df'):
            exec(f"{{var_name}} = pd.DataFrame()")
        elif var_name.endswith('_dict') or var_name in ['channel_impacts', 'model_coefficients']:
            exec(f"{{var_name}} = dict()")
        elif var_name.endswith('s'):
            exec(f"{{var_name}} = []")
        else:
            exec(f"{{var_name}} = None")

For Correlation Question:
If pharses like final transformed activity/ final features / final activity or similar are used then refer to the {best_result_table} to get the final heatmap with only the 'marketing activities'

For questions related to sales perspective/profot/loss:
After calculations and final output give a small sentance as well describing the output always

## CRITICAL: ROI CALCULATION 
For ANY question related to ROI calculations:
the {best_result_table} contains the ROI column for the respective channels. Use this column to print ROI but the base channel will be stored as base_channel_Adstock in the {best_result_table}. Use this column to calculate the ROI..
eg. PDE will be PDE_Adstock etc

## CRITICAL: CORRELATION ANALYSIS
For questions related to correlations:
- Always use the BASE promo_channels list (not transformed channels)
- Before calculating correlations, PRINT the available columns in df to verify
- Check if each promo channel exists in df and print which ones are found
- If no channels are found, print a helpful debug message with the available columns
- If asked about "transformed" correlations, explain that raw data is being used 
  instead because transformed columns are not available in the dataframe
  
VARIABLE INITIALIZATION REQUIREMENTS

CRITICAL: You MUST initialize ALL your own variables before use
Initialize channel_name = None at the beginning of your code
Initialize all variables that might be referenced later with appropriate defaults
For loops, define iterator variables explicitly before usage
For dictionary access, check if keys exist before accessing them

CODE SAFETY REQUIREMENTS

Use descriptive, unique variable names (e.g., impact_ch_name, base_ch_name)
NEVER use generic variable names like 'channel', 'ch', or 'ch_name' in loops
For dictionary iteration, use clear naming: for base_ch_name, values in dict_name.items():
Before accessing dictionaries: ALWAYS use .get() with default values
Before accessing DataFrame columns: ALWAYS check if columns exist
Before any list or DataFrame index access: ALWAYS check length
Initialize result containers (lists, dicts) BEFORE any loop
Handle potential errors with try/except blocks

AVAILABLE VARIABLES
This code will be executed directly in the Python environment with access to these variables:

df: The pandas DataFrame containing all data
target_column: Name of the target variable (string)
date_column: Name of the date column (string)
promo_channels: List of promotional channel names (list of strings)
all_transformed_features: List of all transformed feature names (list of strings)
best_result_table: DataFrame with results from the best regression model
params: Dictionary with all analysis parameters
transformed_channels_by_promo: Dictionary with base channel as key and its top 3 transformations
adstocked_channels_by_promo: Dictionary with base channel as key and its top 3 adstocks used
adstock_range_channel: Dictionary of adstock range applied for each promo_channel
model_coefficients: Dictionary mapping variable names to their coefficients
channel_impacts: Dictionary of channel impact percentages (contributions)
channel_effectiveness: Dictionary of channel effectiveness metrics
spends_df: DataFrame containing channel names and their corresponding spend amounts
spend_column: Spends column name of spends_df
spend_channel_column: Channel name of spends_df
historical_df: DataFrame containing channel names and their corresponding contributions of the history
historical_impact: contribution column name of historical_df
historical_channel_column: Channel name of historical_df
unit_price: The price per unit of the target variable (obtain from user or use standard value)

IMPORTS
Always include these imports at the beginning of your code:
pythonimport pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import matplotlib.dates as mdates
import io
import sys
import traceback
from contextlib import redirect_stdout
DATASET CONTEXT

Target variable: {target_column}
Date column: {date_column}
Promotional channels: {', '.join(promo_channels)}
Time period: {df[date_column].min().strftime('%Y-%m-%d') if hasattr(df[date_column].min(), 'strftime') else df[date_column].min()} to {df[date_column].max().strftime('%Y-%m-%d') if hasattr(df[date_column].max(), 'strftime') else df[date_column].max()}
Dataset shape: {df.shape}
unit price: {unit_price}
Spend information available in spends_df with columns {spend_channel_column} and {spend_column}
Historical data in historical_df with columns {historical_channel_column} and {historical_impact}

MODEL CONTEXT:
- Model coefficients: {json.dumps(model_coefficients)}
- Channel impacts (contributions): {json.dumps(channel_impacts)}
- Channel effectiveness: {json.dumps(channel_effectiveness)}
- Transformed channels by promo: Dictionary with base channel as key and its top 3 transformations
- Adstocked channels by promo: Dictionary with base channel as key and its top 3 adstocks used
- Adstock range by channel: Dictionary of adstock range applied for each promo_channel
- Best result table columns: {best_result_columns}
- T1, T2, T3, etc. are trend variables used to understand trends in the data and NOT considered as promotions
- NOTE: When calculating ROI, you'll need to map between base channel names in spends_df (like 'PDE') and transformed channel names in best_result_table (like 'PDE_Adstock_10_Log')

VARIABLE CLASSIFICATION:
- CRITICAL: When asked about "drivers", "channels", "marketing factors", "top N marketing channels", or similar terms:
  * STRICTLY INCLUDE ONLY actual marketing variables (promo_channels and their transformations)
  * STRICTLY EXCLUDE ALL of the following, which are NOT marketing drivers:
    - Intercept/constant term (represents baseline/base sales)
    - ANY trend variables (variables starting with 'T' followed by a number like T1, T2, or containing 'trend', 'time', or 'Rtime')
    - ANY seasonal variables (containing 'season', 'month', 'week', 'quarter')
    - ANY lag variables (containing '_lag_' or ending with 'lag' followed by a number like 'target_lag_1')
    - ANY other non-promotional variables

LAG VARIABLES CLASSIFICATION - CRITICAL:
- Lag variables (e.g., 'target_lag_1', 'sales_lag_2', or any variable containing '_lag_') are ALWAYS considered NON-MARKETING variables
- They represent the influence of past target values and MUST NEVER be included in marketing contribution analyses
- When filtering variables for marketing analysis, ALWAYS exclude lag variables
- Common lag variable patterns include:
  * Variables containing '_lag_' anywhere in the name
  * Variables ending with 'lag' followed by a number (e.g., 'revlag1')
  * Any variable containing 'lag' that isn't clearly a marketing channel name
- When categorizing variables, lag variables should ALWAYS be grouped with other non-marketing factors like intercept and trend


CODE REQUIREMENTS

Include ALL necessary imports at the beginning
Use matplotlib/seaborn for visualizations with proper titles, labels, and legends
End visualizations with plt.show()
Include print statements with brief interpretations
Format text-based results as clear, readable output
Handle all potential errors gracefully
Use try/except blocks liberally to catch any possible errors

EXAMPLE OF PROPER VARIABLE HANDLING
python# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import traceback

# First verify all required variables exist
required_vars = ['df', 'target_column', 'date_column', 'promo_channels', 
                 'channel_impacts', 'model_coefficients']
missing_vars = []
for var_name in required_vars:
    if var_name not in locals() and var_name not in globals():
        missing_vars.append(var_name)
if missing_vars:
    print(f"Error: Missing required variables:  {{', '.join(missing_vars)}}")

# Initialize ALL variables
channel_name = None
impact_value = 0
filtered_channels = []
result_dict = {{}}

try:
    # Check if dictionary exists before using it
    if 'channel_impacts' in locals() or 'channel_impacts' in globals():
        # Safe dictionary access with get()
        for impact_ch_name, impact_val in channel_impacts.items():
            if impact_ch_name in promo_channels:
                filtered_channels.append(impact_ch_name)
        
        # Handle edge cases
        if not filtered_channels:
            print("No matching channels found")
        else:
            print(f"Found {{len(filtered_channels)}} channels")
    else:
        print("channel_impacts dictionary not available")
        
except Exception as e:
    print(f"Error during analysis: {{str(e)}}")
    traceback.print_exc()

CRITICAL_ FOR VISULIZATION
When analyzing time series data with dates in YYYYMM format (e.g., 202411), please:

1. Convert dates to proper datetime objects using:
   `df['Date'] = pd.to_datetime(df['Date'].astype(str).str[:4] + '-' + df['Date'].astype(str).str[4:6] + '-01')`

2. For questions like "Show the average [target] for each month or quarter across the years":
   - Extract year and month components from the converted dates
   - Group by month or quarter to aggregate across all years
   - Calculate averages for each time period (month/quarter)
   - Create visualizations showing the average values by time period with proper labels
   - Include an "Overall Average" line or reference

3. Ensure the code handles:
   - Both monthly and quarterly aggregation options
   - Proper sorting of months/quarters in chronological order
   - Clear labels for time periods (e.g., "Jan", "Feb", "Mar" instead of 1, 2, 3)
   - Visualization of results with appropriate title, axis labels, and legend
   - LABEL the points for the visulaizations

The resulting analysis should make it easy to identify seasonal patterns regardless of year.

I NEED COMPLETE, EXECUTABLE PYTHON CODE ONLY.

Generate only the Python code needed to answer the question. Make sure to follow the plotting requirements exactly.
    """

    try:
        print("\nAnalyzing your question...")
        response = model.generate_content(context)
        code_to_execute = response.text.strip()

        # Clean up the code
        if code_to_execute.startswith("```python"):
            code_to_execute = code_to_execute[9:]
        if code_to_execute.startswith("```"):
            code_to_execute = code_to_execute[3:]
        if code_to_execute.endswith("```"):
            code_to_execute = code_to_execute[:-3]

        code_to_execute = code_to_execute.strip()
        print("\nGenerating analysis...")
        print("="*50)
        print("GENERATED CODE:")
        print("="*50)
        print(code_to_execute)
        print("="*50)

        # Set up the execution environment
        local_vars = {
            'df': df.copy(),
            'target_column': target_column,
            'date_column': date_column,
            'promo_channels': promo_channels,
            'all_transformed_features': all_transformed_features,
            'best_result_table': best_result_table,
            'params': params,
            'model_coefficients': model_coefficients,
            'channel_impacts': channel_impacts,
            'channel_effectiveness': channel_effectiveness,
            'transformed_channels_by_promo': transformed_channels_by_promo,
            'adstocked_channels_by_promo': adstocked_channels_by_promo,
            'adstock_range_channel': adstock_range_channel,
            'spends_df': spends_df.copy() if spends_df is not None else pd.DataFrame(),
            'spend_channel_column': spend_channel_column,
            'spend_column': spend_column,
            'historical_df': historical_df.copy() if historical_df is not None else pd.DataFrame(),
            'historical_channel_column': historical_channel_column,
            'historical_impact': historical_impact,
            'unit_price': unit_price,
            'pd': pd,
            'plt': plt,
            'sns': sns,
            'np': np,
            'datetime': datetime,
            'mdates': mdates,
            'variance_inflation_factor': variance_inflation_factor,
            'venn2': venn2,
            'logging': logging,
            'user_question': user_question
        }

        # Execute the code with retries - now returns figure as well
        success, output_text, figure, error_message = execute_code_with_retries(code_to_execute, local_vars)
        
        # Initialize result dictionary
        result = {
            'text_output': "",
            'image': None,
            'output_type': 'text'
        }
        
        if success:
            print("\nAnalysis completed successfully!")
            result['text_output'] = output_text if output_text else "Analysis completed successfully!"
            
            # Check if we have a figure
            if figure is not None:
                result['image'] = figure
                
                if output_text:
                    result['output_type'] = 'both'
                else:
                    result['output_type'] = 'image'
                    
                print("Figure captured successfully!")
                print(f"Figure object type: {type(figure)}")
                print(f"Figure size: {figure.get_size_inches()}")
            else:
                result['output_type'] = 'text'
                print("No figure was created.")
                
        else:
            error_msg = f"Failed to generate analysis after multiple attempts.\n"
            if error_message:
                error_msg += f"Last error: {error_message}\n"
            error_msg += "Please try rephrasing your question or ask something different."
            result['text_output'] = error_msg
            result['output_type'] = 'text'

        return result

    except Exception as e:
        error_msg = f"Error generating analysis: {e}\nPlease try again with a different question."
        print(f"Exception in interactive_mmm_chatbot: {e}")
        traceback.print_exc()
        return {
            'text_output': error_msg,
            'image': None,
            'output_type': 'text'
        }





def initialize_gcs_client(bucket_name, project_id, credentials_path):
    """Initialize GCP storage client"""
    from google.cloud import storage
    from google.oauth2 import service_account
    
    credentials = service_account.Credentials.from_service_account_file(credentials_path)
    gcs_client = storage.Client(project=project_id, credentials=credentials)
    bucket = gcs_client.bucket(bucket_name)
    return bucket

def upload_image_to_gcs(bucket, image_data, file_path):
    """Upload image to GCS and return the URL"""
    try:
        blob = bucket.blob(file_path)
        blob.upload_from_string(image_data, content_type="image/png")
        
        # With uniform bucket-level access enabled, we use the standard GCS URL format
        image_url = f"https://storage.googleapis.com/{bucket.name}/{file_path}"
        
        print(f"Successfully uploaded image to: {image_url}")
        return image_url
    except Exception as e:
        print(f"Error uploading to GCS: {str(e)}")
        return None

def save_message_to_db(conn, owner_id, message_text, answers, model_run_id, image_url=None):
    """Save message to database and return message_id"""
    cursor = conn.cursor()
    
    # SQL to insert message (message_id is SERIAL, auto-generated)
    sql = """
    INSERT INTO messages (owner_id, message_text, answer, model_run_id, image_url)
    VALUES (%s, %s, %s, %s, %s)
    RETURNING message_id
    """
    
    # Execute the SQL
    cursor.execute(sql, (owner_id, message_text, answers, model_run_id, image_url))
    
    # Get the message_id
    message_id = cursor.fetchone()[0]
    
    # Commit the transaction
    conn.commit()
    cursor.close()
    
    return message_id

def connect_to_database(db_config):
    """Establish database connection"""
    import psycopg2
    try:
        conn = psycopg2.connect(
            host=db_config['host'],
            database=db_config['name'],
            user=db_config['user'],
            password=db_config['password'],
            port=db_config['port']
        )
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def chatbot(user_question, owner_id, model_run_id, gcs_config):
    """Process chatbot response and handle image upload/database storage"""
    import os
    from datetime import datetime
    import io

    try:
        # Connect to database
        db_conn = get_db_connection()
        if not db_conn:
            print("Failed to connect to database")
            exit(1)
        
        # Initialize GCS bucket
        gcs_bucket = initialize_gcs_client(
            bucket_name=gcs_config['bucket_name'],
            project_id=gcs_config['project_id'],
            credentials_path=gcs_config['credentials_path']
        )
        
        # Run the chatbot with a sample owner ID and model_run_id
 
    except Exception as e:
        print(f"Error in main execution: {e}")
        import traceback
        traceback.print_exc()
    
    # Get response from chatbot
    target_column, date_column, id_column, start_date, end_date, spend_channel_column, spend_column, historical_channel_column, historical_impact, unit_price, best_result_table, all_regression_results, transformed_channels_by_promo, adstocked_channels_by_promo, adstock_range_channel, all_features, all_transformed_features, promotional_columns, best_features, date_format,params = result_extraction()
    
    # Load dataframes
    file_path = "../MMix_data_for_testing.csv"
    spends_file_path = "../Spends_test.xlsx"
    historical_file_path = "../Historical_test.xlsx"

    df = load_file(file_path)
    spends_df = load_file(spends_file_path)
    historical_df = load_file(historical_file_path)
    
    df = process_and_filter_main_data(file_path, promotional_columns, date_column, id_column, target_column, start_date, end_date, date_format, best_features)
    df, control_variables = add_control_variables(df, id_column, date_column)
           
    
    result = interactive_mmm_chatbot(
        user_question=user_question,
        df=df,
        target_column=target_column,
        date_column=date_column,
        promo_channels=promotional_columns,
        all_transformed_features=all_transformed_features,
        best_result_table=best_result_table,
        params=params,
        transformed_channels_by_promo=transformed_channels_by_promo,
        adstocked_channels_by_promo=adstocked_channels_by_promo,
        adstock_range_channel=adstock_range_channel,
        spends_df=spends_df,
        spend_channel_column=spend_channel_column,
        spend_column=spend_column,
        historical_df=historical_df,
        historical_channel_column=historical_channel_column,
        historical_impact=historical_impact,
        unit_price=unit_price
    )
    
    # Handle the result
    output_type = result['output_type']
    print(f"output_type: {output_type}")
    
    # Print text output if available
    if 'text_output' in result and result['text_output']:
        print("\nAnalysis Results:")
        print(result['text_output'])
    
    # Handle image if available
    if output_type in ['image', 'both'] and result['image'] is not None:
        # Create images directory if it doesn't exist
        os.makedirs('images', exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"mmm_analysis_{timestamp}.png"
        filepath = os.path.join('images', filename)
        
        try:
            # Save the figure with high DPI for better quality
            result['image'].savefig(filepath, dpi=300, bbox_inches='tight', 
                                  facecolor='white', edgecolor='none')
            
            print(f"\nImage successfully saved to: {filepath}")
            
            # Verify the file was actually created
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"File size: {file_size} bytes")
            else:
                print("Warning: File was not created despite no errors")
                
        except Exception as save_error:
            print(f"Error saving figure: {save_error}")
            traceback.print_exc()
        
        finally:
            # Close the figure to free memory
            plt.close(result['image'])
        
        # Optional: Display the image if you're in a Jupyter notebook
        from IPython.display import Image, display
        display(Image(filepath)) 
    
    elif output_type in ['image', 'both']:
        print("\nExpected an image but none was generated.")
        print("This might be because:")
        print("1. The generated code didn't create any plots")
        print("2. The code had errors during execution")
        print("3. plt.show() was not called properly in the generated code")
    
    
    image_url = None
    
    # Handle image upload if image exists
    if result['output_type'] in ['image', 'both'] and result['image'] is not None:
        try:
            # Convert matplotlib figure to bytes
            img_buffer = io.BytesIO()
            result['image'].savefig(img_buffer, format='png', dpi=300, 
                                    bbox_inches='tight', facecolor='white', edgecolor='none')
            img_buffer.seek(0)
            image_data = img_buffer.getvalue()
            
            # Generate unique filename for GCS
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            gcs_filename = f"mmm_analysis/{owner_id}/{model_run_id}_{timestamp}.png"
            
            # Upload to GCS
            image_url = upload_image_to_gcs(gcs_bucket, image_data, gcs_filename)
            
            print(f"Image uploaded to GCS: {image_url}")
            
        except Exception as e:
            print(f"Error processing image: {e}")
 
    
    # Save bot response to database
    bot_response_text = result.get('text_output', 'Analysis completed')
    bot_message_id = save_message_to_db(
        conn=db_conn,
        owner_id=owner_id,
        message_text=user_question,
        answers=bot_response_text,
        model_run_id=model_run_id,
        image_url=image_url
    )
    
    # print(f"User message saved with ID: {user_message_id}")
    print(f"Bot response saved with ID: {bot_message_id}")
    
    return {
        'response': result,
        'image_url': image_url,
        'bot_message_id': bot_message_id
    }

if __name__ == "__main__":
    # Set up database configuration
    db_config = {
        'host': 'localhost',
        'name': 'MMX',
        'user': 'postgres',
        'password': 'something',
        'port': 5432
    }
    
    # Set up GCS configuration
    GCS_BUCKET_NAME = 'novomix'
    GCS_PROJECT_ID = 'aichatbot-412315'

    gcs_config = {
        'bucket_name': GCS_BUCKET_NAME,
        'project_id': GCS_PROJECT_ID,
        'credentials_path': '../Service Account.json'
    }
    
    # Initialize connections
    try:
        owner_id='09'
        model_run_id='session123'
        # user_question="Which channel delivered the highest ROI?"
        # user_question="Are there any obvious correlations between our different marketing activities?"
        user_question="What is the trend of target?"
        # user_question="What was the Return on Investment (ROI) for our spend on Organic_Engagement?"
        # Process the chatbot response with integrated upload and database storage
        result = chatbot(
            user_question=user_question,
            owner_id=owner_id,
            model_run_id=model_run_id,
            gcs_config=gcs_config
        )
        
        print("\n=== Final Results ===")
        print(f"Image URL: {result['image_url']}")
        # print(f"User Message ID: {result['user_message_id']}")
        print(f"Bot Message ID: {result['bot_message_id']}")
        
    except Exception as e:
        print(f"Error in main execution: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up database connection
        if 'db_conn' in locals() and db_conn:
            db_conn.close()
            print("Database connection closed")

Successfully uploaded image to: https://storage.googleapis.com/novomix/mmm_analysis/user123/session456_20250522_152310.png
Image uploaded to GCS: https://storage.googleapis.com/novomix/mmm_analysis/user123/session456_20250522_152310.png
Error in main execution: column "answers" of relation "messages" does not exist
LINE 2:     INSERT INTO messages (owner_id, message_text, answers, m...
                                                          ^

Database connection closed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Temp\ipykernel_6748\2699388310.py", line 268, in <module>
    result = process_chatbot_response(
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Temp\ipykernel_6748\2699388310.py", line 198, in process_chatbot_response
    user_message_id = save_message_to_db(
                      ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Temp\ipykernel_6748\2699388310.py", line 137, in save_message_to_db
    cursor.execute(sql, (owner_id, message_text, message_by, model_run_id, image_url))
psycopg2.errors.UndefinedColumn: column "answers" of relation "messages" does not exist
LINE 2:     INSERT INTO messages (owner_id, message_text, answers, m...
                                                          ^