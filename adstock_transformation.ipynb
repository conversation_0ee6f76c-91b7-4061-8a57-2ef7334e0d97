{"cells": [{"cell_type": "code", "execution_count": null, "id": "081a3f51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eedb6d31", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "def get_transformation_functions():\n", "    \"\"\"\n", "    Return a dictionary of transformation functions for feature engineering.\n", "    Optimized to use vectorized operations.\n", "    \"\"\"\n", "    def safe_power(x, exp):\n", "        return np.power(np.maximum(x, 0), exp)\n", "    \n", "    def safe_log(x):\n", "        return np.log1p(np.maximum(x, 0))\n", "    \n", "    return {\n", "        \"Log\": safe_log,\n", "        \"Root4\": lambda x: safe_power(x, 0.4),\n", "        \"Root5\": lambda x: safe_power(x, 0.5),\n", "        \"Root6\": lambda x: safe_power(x, 0.6),\n", "        \"Root7\": lambda x: safe_power(x, 0.7),\n", "        \"Root8\": lambda x: safe_power(x, 0.8),\n", "        \"Root9\": lambda x: safe_power(x, 0.9)\n", "    }\n", "\n", "def apply_adstock(df, promo_col, id_col, adstock_rate):\n", "    \"\"\"\n", "    Apply adstock transformation to a promotional channel with specified rate.\n", "    Optimized to use groupby and transform for better performance.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promo_col (str): Name of promotional column\n", "        id_col (str): Column that identifies time series groups\n", "        adstock_rate (int): Adstock rate (0-100)\n", "\n", "    Returns:\n", "        tuple: (Column name, Series with adstocked values)\n", "    \"\"\"\n", "    rate = adstock_rate / 100\n", "    \n", "    # Define a function to apply adstock within each group\n", "    def adstock_group(group):\n", "        result = np.zeros(len(group))\n", "        cumulative = 0\n", "        for i, value in enumerate(group):\n", "            cumulative = value + rate * cumulative\n", "            result[i] = cumulative\n", "        return result\n", "    \n", "    # Apply the function to each group using transform\n", "    adstocked_series = df.groupby(id_col)[promo_col].transform(\n", "        lambda group: adstock_group(group.values)\n", "    )\n", "    \n", "    adstock_col = f\"{promo_col}_Adstock_{adstock_rate}\"\n", "    return adstock_col, adstocked_series\n", "\n", "def apply_transformation(series, transform_name, transform_func, base_col_name):\n", "    \"\"\"\n", "    Apply transformation function to a series.\n", "    Optimized for direct vectorized operations.\n", "\n", "    Args:\n", "        series (pd.Series): Data to transform\n", "        transform_name (str): Name of transformation\n", "        transform_func (callable): Transformation function\n", "        base_col_name (str): Base column name\n", "\n", "    Returns:\n", "        tuple: (Column name, Transformed series)\n", "    \"\"\"\n", "    transformed_col = f\"{base_col_name}_{transform_name}\"\n", "    transformed_series = transform_func(series)\n", "    return transformed_col, transformed_series\n", "\n", "def generate_features(df, promotional_columns, id_col):\n", "    \"\"\"\n", "    Generate features by applying adstock and transformations.\n", "    Optimized to avoid DataFrame fragmentation by collecting all new columns\n", "    and adding them at once using concat.\n", "    \n", "    Args:\n", "        df (pd.DataFrame): Input dataframe\n", "        promotional_columns (list): List of promotional column names\n", "        id_col (str): Column that identifies time series groups\n", "        \n", "    Returns:\n", "        pd.DataFrame: DataFrame with generated features\n", "    \"\"\"\n", "    base_df = df.copy()\n", "    transformations = get_transformation_functions()\n", "    adstock_rates = range(10, 100, 10)\n", "    \n", "    # Dictionary to collect all new columns\n", "    new_columns_dict = {}\n", "    \n", "    # Calculate all adstock features and transformations\n", "    for promo_col in promotional_columns:\n", "        for adstock_rate in adstock_rates:\n", "            # Apply adstock\n", "            adstock_col, adstocked_series = apply_adstock(df, promo_col, id_col, adstock_rate)\n", "            new_columns_dict[adstock_col] = adstocked_series\n", "            \n", "            # Apply transformations to this adstocked column\n", "            for transform_name, transform_func in transformations.items():\n", "                transformed_col, transformed_series = apply_transformation(\n", "                    adstocked_series, transform_name, transform_func, adstock_col\n", "                )\n", "                new_columns_dict[transformed_col] = transformed_series\n", "    \n", "    # Create a DataFrame from all the new columns\n", "    new_columns_df = pd.DataFrame(new_columns_dict, index=df.index)\n", "    \n", "    # Concatenate with the original DataFrame\n", "    global_df = pd.concat([base_df, new_columns_df], axis=1)\n", "    \n", "    return global_df\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "bede0a7b", "metadata": {}, "outputs": [], "source": ["# Example usage:\n", "df=pd.read_excel('dummy1.xlsx')\n", "promotional_columns=['PDE','Copay']\n", "id_col='ID'\n", "global_df3 = generate_features(df, promotional_columns, id_col)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}